"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TransactionStatus = exports.PaymentMethod = exports.TransactionType = void 0;
var TransactionType;
(function (TransactionType) {
    TransactionType["PAYMENT"] = "PAYMENT";
    TransactionType["REFUND"] = "REFUND";
    TransactionType["COMMISSION"] = "COMMISSION";
    TransactionType["DRIVER_PAYOUT"] = "DRIVER_PAYOUT";
    TransactionType["PARTNER_PAYOUT"] = "PARTNER_PAYOUT";
})(TransactionType || (exports.TransactionType = TransactionType = {}));
var PaymentMethod;
(function (PaymentMethod) {
    PaymentMethod["CASH"] = "CASH";
    PaymentMethod["STRIPE"] = "STRIPE";
    PaymentMethod["ORANGE_MONEY"] = "ORANGE_MONEY";
    PaymentMethod["MTN_MONEY"] = "MTN_MONEY";
    PaymentMethod["WAVE"] = "WAVE";
})(PaymentMethod || (exports.PaymentMethod = PaymentMethod = {}));
var TransactionStatus;
(function (TransactionStatus) {
    TransactionStatus["PENDING"] = "PENDING";
    TransactionStatus["COMPLETED"] = "COMPLETED";
    TransactionStatus["FAILED"] = "FAILED";
    TransactionStatus["CANCELLED"] = "CANCELLED";
    TransactionStatus["REFUNDED"] = "REFUNDED";
})(TransactionStatus || (exports.TransactionStatus = TransactionStatus = {}));
//# sourceMappingURL=payment.js.map