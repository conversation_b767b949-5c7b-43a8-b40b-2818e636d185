import { ConfigService } from '@nestjs/config';
export interface DistanceResult {
    distance: number;
    duration: number;
    status: 'OK' | 'NOT_FOUND' | 'ZERO_RESULTS' | 'ERROR';
}
export declare class DistanceService {
    private readonly configService;
    private readonly logger;
    private readonly googleMapsApiKey;
    constructor(configService: ConfigService);
    calculateDistanceAndDuration(originLat: number, originLng: number, destLat: number, destLng: number): Promise<DistanceResult>;
    private calculateHaversineDistance;
    calculateMultipleDistances(origins: Array<{
        lat: number;
        lng: number;
    }>, destinations: Array<{
        lat: number;
        lng: number;
    }>): Promise<DistanceResult[][]>;
    findNearestDriver(pickupLat: number, pickupLng: number, availableDrivers: Array<{
        id: string;
        lat: number;
        lng: number;
    }>): Promise<{
        driverId: string;
        distance: number;
        duration: number;
    } | null>;
    isWithinServiceArea(latitude: number, longitude: number): boolean;
    estimateDeliveryTimeWithTraffic(distance: number, hour: number): number;
    private toRadians;
}
