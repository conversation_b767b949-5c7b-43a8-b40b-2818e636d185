import { PriceCalculationRequest, PriceCalculationResult } from '@wali/shared';
export declare class PricingService {
    private readonly logger;
    calculatePrice(request: PriceCalculationRequest): Promise<PriceCalculationResult>;
    private calculateDistance;
    private calculateTypeFee;
    private calculateItemsFee;
    private calculateSurcharges;
    validateOrder(request: PriceCalculationRequest): {
        valid: boolean;
        errors: string[];
    };
    private isWithinIvoryCoast;
    private toRadians;
    estimateDeliveryTime(pickupLat: number, pickupLon: number, deliveryLat: number, deliveryLon: number): number;
    private isInPlateau;
}
