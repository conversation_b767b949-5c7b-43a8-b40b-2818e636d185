import { z } from 'zod';
export declare const phoneSchema: z.ZodString;
export declare const emailSchema: z.ZodOptional<z.ZodString>;
export declare const passwordSchema: z.ZodString;
export declare const userRegistrationSchema: z.ZodObject<{
    phone: z.ZodString;
    email: z.ZodOptional<z.ZodString>;
    firstName: z.ZodString;
    lastName: z.ZodString;
    password: z.ZodString;
    role: z.ZodDefault<z.ZodEnum<["CLIENT", "DRIVER", "PARTNER"]>>;
}, "strip", z.ZodTypeAny, {
    phone?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    role?: "CLIENT" | "DRIVER" | "PARTNER";
    password?: string;
}, {
    phone?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    role?: "CLIENT" | "DRIVER" | "PARTNER";
    password?: string;
}>;
export declare const addressSchema: z.ZodObject<{
    street: z.ZodString;
    city: z.ZodString;
    district: z.ZodOptional<z.ZodString>;
    landmark: z.ZodOptional<z.ZodString>;
    latitude: z.ZodNumber;
    longitude: z.ZodNumber;
    label: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    street?: string;
    city?: string;
    district?: string;
    landmark?: string;
    latitude?: number;
    longitude?: number;
    label?: string;
}, {
    street?: string;
    city?: string;
    district?: string;
    landmark?: string;
    latitude?: number;
    longitude?: number;
    label?: string;
}>;
export declare const orderCreationSchema: z.ZodObject<{
    type: z.ZodEnum<["DELIVERY", "FOOD", "SHOPPING"]>;
    pickupAddress: z.ZodString;
    pickupLatitude: z.ZodNumber;
    pickupLongitude: z.ZodNumber;
    deliveryAddress: z.ZodString;
    deliveryLatitude: z.ZodNumber;
    deliveryLongitude: z.ZodNumber;
    notes: z.ZodOptional<z.ZodString>;
    scheduledAt: z.ZodOptional<z.ZodDate>;
    items: z.ZodOptional<z.ZodArray<z.ZodObject<{
        name: z.ZodString;
        description: z.ZodOptional<z.ZodString>;
        quantity: z.ZodNumber;
        unitPrice: z.ZodNumber;
    }, "strip", z.ZodTypeAny, {
        name?: string;
        description?: string;
        quantity?: number;
        unitPrice?: number;
    }, {
        name?: string;
        description?: string;
        quantity?: number;
        unitPrice?: number;
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    type?: "DELIVERY" | "FOOD" | "SHOPPING";
    items?: {
        name?: string;
        description?: string;
        quantity?: number;
        unitPrice?: number;
    }[];
    pickupAddress?: string;
    pickupLatitude?: number;
    pickupLongitude?: number;
    deliveryAddress?: string;
    deliveryLatitude?: number;
    deliveryLongitude?: number;
    notes?: string;
    scheduledAt?: Date;
}, {
    type?: "DELIVERY" | "FOOD" | "SHOPPING";
    items?: {
        name?: string;
        description?: string;
        quantity?: number;
        unitPrice?: number;
    }[];
    pickupAddress?: string;
    pickupLatitude?: number;
    pickupLongitude?: number;
    deliveryAddress?: string;
    deliveryLatitude?: number;
    deliveryLongitude?: number;
    notes?: string;
    scheduledAt?: Date;
}>;
export declare const validatePhone: (phone: string) => boolean;
export declare const validateEmail: (email: string) => boolean;
export declare const formatPhone: (phone: string) => string;
