"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-addresses/page",{

/***/ "(app-pages-browser)/./src/components/maps/GoogleMapsProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/maps/GoogleMapsProvider.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleMapsProvider: function() { return /* binding */ GoogleMapsProvider; },\n/* harmony export */   useGoogleMaps: function() { return /* binding */ useGoogleMaps; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/script */ \"(app-pages-browser)/../../node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useGoogleMaps,GoogleMapsProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst GoogleMapsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isLoaded: false,\n    loadError: null\n});\nconst useGoogleMaps = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GoogleMapsContext);\n    if (!context) {\n        throw new Error(\"useGoogleMaps must be used within a GoogleMapsProvider\");\n    }\n    return context;\n};\n_s(useGoogleMaps, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst GoogleMapsProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadError, setLoadError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scriptLoaded, setScriptLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const apiKey = \"AIzaSyBgNfqhT6lk47eh3gA0Oc9uxsB16r5lTMY\";\n    // Vérifier si Google Maps est déjà chargé\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _window_google;\n        if ((_window_google = window.google) === null || _window_google === void 0 ? void 0 : _window_google.maps) {\n            setIsLoaded(true);\n            setScriptLoaded(true);\n        }\n    }, []);\n    const handleScriptLoad = ()=>{\n        var _window_google;\n        console.log(\"✅ Google Maps API loaded successfully\");\n        setScriptLoaded(true);\n        // Vérifier que l'API est vraiment disponible\n        if ((_window_google = window.google) === null || _window_google === void 0 ? void 0 : _window_google.maps) {\n            setIsLoaded(true);\n            setLoadError(null);\n        } else {\n            setLoadError(\"Google Maps API loaded but not available\");\n        }\n    };\n    const handleScriptError = (error)=>{\n        console.error(\"❌ Failed to load Google Maps API:\", error);\n        setLoadError(\"Failed to load Google Maps API\");\n        setIsLoaded(false);\n    };\n    if (!apiKey) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleMapsContext.Provider, {\n            value: {\n                isLoaded: false,\n                loadError: \"Google Maps API key not configured\"\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\GoogleMapsProvider.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleMapsContext.Provider, {\n        value: {\n            isLoaded,\n            loadError\n        },\n        children: [\n            !scriptLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_2___default()), {\n                src: \"https://maps.googleapis.com/maps/api/js?key=\".concat(apiKey, \"&libraries=places,marker&language=fr&region=CI&loading=async\"),\n                strategy: \"afterInteractive\",\n                onLoad: handleScriptLoad,\n                onError: handleScriptError\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\GoogleMapsProvider.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\GoogleMapsProvider.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(GoogleMapsProvider, \"jDTmpbB42zLFbeBAjQDfp9bBHTA=\");\n_c = GoogleMapsProvider;\nvar _c;\n$RefreshReg$(_c, \"GoogleMapsProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/maps/GoogleMapsProvider.tsx\n"));

/***/ })

});