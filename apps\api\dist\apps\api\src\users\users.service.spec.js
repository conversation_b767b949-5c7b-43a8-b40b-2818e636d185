"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
describe('UsersService', () => {
    let service;
    let prismaService;
    const mockUser = {
        id: '1',
        phone: '+2250701234567',
        email: '<EMAIL>',
        firstName: 'Kouassi',
        lastName: 'Yao',
        avatar: null,
        role: client_1.UserRole.CLIENT,
        isActive: true,
        isVerified: true,
        createdAt: new Date(),
        updatedAt: new Date(),
    };
    const mockPrismaService = {
        user: {
            findUnique: jest.fn(),
            update: jest.fn(),
            count: jest.fn(),
        },
        order: {
            count: jest.fn(),
        },
        transaction: {
            findMany: jest.fn(),
        },
        rating: {
            findMany: jest.fn(),
        },
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                users_service_1.UsersService,
                { provide: prisma_service_1.PrismaService, useValue: mockPrismaService },
            ],
        }).compile();
        service = module.get(users_service_1.UsersService);
        prismaService = module.get(prisma_service_1.PrismaService);
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    describe('getProfile', () => {
        it('should return user profile successfully', async () => {
            const expectedProfile = {
                id: mockUser.id,
                phone: mockUser.phone,
                email: mockUser.email,
                firstName: mockUser.firstName,
                lastName: mockUser.lastName,
                avatar: mockUser.avatar,
                role: mockUser.role,
                isActive: mockUser.isActive,
                isVerified: mockUser.isVerified,
            };
            mockPrismaService.user.findUnique.mockResolvedValue(expectedProfile);
            const result = await service.getProfile('1');
            expect(result).toEqual(expectedProfile);
            expect(mockPrismaService.user.findUnique).toHaveBeenCalledWith({
                where: { id: '1' },
                select: {
                    id: true,
                    phone: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    avatar: true,
                    role: true,
                    isActive: true,
                    isVerified: true,
                },
            });
        });
        it('should throw NotFoundException if user does not exist', async () => {
            mockPrismaService.user.findUnique.mockResolvedValue(null);
            await expect(service.getProfile('1')).rejects.toThrow(common_1.NotFoundException);
        });
        it('should throw NotFoundException if user is inactive', async () => {
            mockPrismaService.user.findUnique.mockResolvedValue({
                ...mockUser,
                isActive: false,
            });
            await expect(service.getProfile('1')).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('updateProfile', () => {
        const updateDto = {
            firstName: 'Nouveau',
            lastName: 'Nom',
            email: '<EMAIL>',
        };
        it('should update profile successfully', async () => {
            const updatedUser = { ...mockUser, ...updateDto };
            mockPrismaService.user.findUnique
                .mockResolvedValueOnce(mockUser)
                .mockResolvedValueOnce(null);
            mockPrismaService.user.update.mockResolvedValue(updatedUser);
            const result = await service.updateProfile('1', updateDto);
            expect(result).toEqual(updatedUser);
            expect(mockPrismaService.user.update).toHaveBeenCalledWith({
                where: { id: '1' },
                data: {
                    firstName: 'Nouveau',
                    lastName: 'Nom',
                    email: '<EMAIL>',
                },
                select: {
                    id: true,
                    phone: true,
                    email: true,
                    firstName: true,
                    lastName: true,
                    avatar: true,
                    role: true,
                    isActive: true,
                    isVerified: true,
                },
            });
        });
        it('should throw ConflictException if email already exists', async () => {
            mockPrismaService.user.findUnique
                .mockResolvedValueOnce(mockUser)
                .mockResolvedValueOnce({ id: '2', email: '<EMAIL>' });
            await expect(service.updateProfile('1', updateDto)).rejects.toThrow(common_1.ConflictException);
        });
        it('should not check email uniqueness if email is unchanged', async () => {
            const updateDtoSameEmail = {
                firstName: 'Nouveau',
                email: mockUser.email,
            };
            mockPrismaService.user.findUnique.mockResolvedValueOnce(mockUser);
            mockPrismaService.user.update.mockResolvedValue({
                ...mockUser,
                firstName: 'Nouveau',
            });
            await service.updateProfile('1', updateDtoSameEmail);
            expect(mockPrismaService.user.findUnique).toHaveBeenCalledTimes(1);
        });
    });
    describe('deleteAccount', () => {
        it('should soft delete account successfully', async () => {
            mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
            mockPrismaService.user.update.mockResolvedValue({
                ...mockUser,
                isActive: false,
                email: null,
            });
            const result = await service.deleteAccount('1');
            expect(result).toEqual({
                message: 'Votre compte a été supprimé avec succès',
            });
            expect(mockPrismaService.user.update).toHaveBeenCalledWith({
                where: { id: '1' },
                data: {
                    isActive: false,
                    email: null,
                },
            });
        });
        it('should throw NotFoundException if user does not exist', async () => {
            mockPrismaService.user.findUnique.mockResolvedValue(null);
            await expect(service.deleteAccount('1')).rejects.toThrow(common_1.NotFoundException);
        });
    });
    describe('getUserStats', () => {
        it('should return user statistics successfully', async () => {
            mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
            mockPrismaService.order.count
                .mockResolvedValueOnce(12)
                .mockResolvedValueOnce(10);
            mockPrismaService.transaction.findMany.mockResolvedValue([
                { amount: 2500 },
                { amount: 3000 },
                { amount: 1500 },
            ]);
            mockPrismaService.rating.findMany.mockResolvedValue([
                { rating: 5 },
                { rating: 4 },
                { rating: 5 },
            ]);
            const result = await service.getUserStats('1');
            expect(result).toEqual({
                totalOrders: 12,
                completedOrders: 10,
                totalSpent: 7000,
                averageRating: 4.7,
            });
        });
        it('should handle zero ratings correctly', async () => {
            mockPrismaService.user.findUnique.mockResolvedValue(mockUser);
            mockPrismaService.order.count
                .mockResolvedValueOnce(0)
                .mockResolvedValueOnce(0);
            mockPrismaService.transaction.findMany.mockResolvedValue([]);
            mockPrismaService.rating.findMany.mockResolvedValue([]);
            const result = await service.getUserStats('1');
            expect(result).toEqual({
                totalOrders: 0,
                completedOrders: 0,
                totalSpent: 0,
                averageRating: 0,
            });
        });
    });
});
//# sourceMappingURL=users.service.spec.js.map