"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const addresses_service_1 = require("./addresses.service");
const dto_1 = require("./dto");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let AddressesController = class AddressesController {
    constructor(addressesService) {
        this.addressesService = addressesService;
    }
    async getUserAddresses(user) {
        return this.addressesService.getUserAddresses(user.id);
    }
    async getAddressById(id, user) {
        return this.addressesService.getAddressById(id, user.id);
    }
    async createAddress(user, createAddressDto) {
        return this.addressesService.createAddress(user.id, createAddressDto);
    }
    async updateAddress(id, user, updateAddressDto) {
        return this.addressesService.updateAddress(id, user.id, updateAddressDto);
    }
    async deleteAddress(id, user) {
        return this.addressesService.deleteAddress(id, user.id);
    }
    async setDefaultAddress(id, user) {
        return this.addressesService.setDefaultAddress(id, user.id);
    }
    async searchNearbyAddresses(user, latitude, longitude, radius) {
        return this.addressesService.searchNearbyAddresses(user.id, Number(latitude), Number(longitude), radius ? Number(radius) : 5);
    }
    async geocodeAddress(address) {
        return this.addressesService.geocodeAddress(address);
    }
    async reverseGeocode(latitude, longitude) {
        return this.addressesService.reverseGeocode(Number(latitude), Number(longitude));
    }
};
exports.AddressesController = AddressesController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Liste des adresses de l\'utilisateur',
        description: 'Récupère toutes les adresses de livraison de l\'utilisateur connecté'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Liste des adresses récupérée avec succès.',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string', example: 'cuid123456789' },
                    label: { type: 'string', example: 'Maison' },
                    street: { type: 'string', example: 'Rue des Jardins, Villa 12' },
                    city: { type: 'string', example: 'Abidjan' },
                    district: { type: 'string', example: 'Cocody' },
                    landmark: { type: 'string', example: 'Près de la pharmacie' },
                    latitude: { type: 'number', example: 5.3364 },
                    longitude: { type: 'number', example: -4.0267 },
                    isDefault: { type: 'boolean', example: true },
                    createdAt: { type: 'string', format: 'date-time' }
                }
            }
        }
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "getUserAddresses", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Détails d\'une adresse',
        description: 'Récupère les détails d\'une adresse spécifique'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de l\'adresse',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Adresse récupérée avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Adresse non trouvée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "getAddressById", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Créer une nouvelle adresse',
        description: 'Ajoute une nouvelle adresse de livraison pour l\'utilisateur'
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Adresse créée avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Données invalides ou coordonnées hors de Côte d\'Ivoire'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.CreateAddressDto]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "createAddress", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Mettre à jour une adresse',
        description: 'Modifie une adresse existante de l\'utilisateur'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de l\'adresse à modifier',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Adresse mise à jour avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Adresse non trouvée'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Données invalides'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, dto_1.UpdateAddressDto]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "updateAddress", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Supprimer une adresse',
        description: 'Supprime une adresse de l\'utilisateur'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de l\'adresse à supprimer',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Adresse supprimée avec succès.',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Adresse supprimée avec succès' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Adresse non trouvée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "deleteAddress", null);
__decorate([
    (0, common_1.Put)(':id/default'),
    (0, swagger_1.ApiOperation)({
        summary: 'Définir comme adresse par défaut',
        description: 'Définit une adresse comme adresse de livraison par défaut'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de l\'adresse à définir par défaut',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Adresse définie comme par défaut avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Adresse non trouvée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "setDefaultAddress", null);
__decorate([
    (0, common_1.Get)('nearby/search'),
    (0, swagger_1.ApiOperation)({
        summary: 'Rechercher des adresses à proximité',
        description: 'Trouve les adresses de l\'utilisateur dans un rayon donné'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'latitude',
        description: 'Latitude du point de recherche',
        example: 5.3364,
        type: 'number'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'longitude',
        description: 'Longitude du point de recherche',
        example: -4.0267,
        type: 'number'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'radius',
        description: 'Rayon de recherche en kilomètres',
        example: 5,
        required: false,
        type: 'number'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Adresses à proximité trouvées.',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    id: { type: 'string' },
                    label: { type: 'string' },
                    street: { type: 'string' },
                    city: { type: 'string' },
                    district: { type: 'string' },
                    latitude: { type: 'number' },
                    longitude: { type: 'number' },
                    distance: { type: 'number', description: 'Distance en kilomètres' }
                }
            }
        }
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)('latitude')),
    __param(2, (0, common_1.Query)('longitude')),
    __param(3, (0, common_1.Query)('radius')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number, Number]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "searchNearbyAddresses", null);
__decorate([
    (0, common_1.Post)('geocode'),
    (0, swagger_1.ApiOperation)({
        summary: 'Géocoder une adresse',
        description: 'Convertit une adresse textuelle en coordonnées GPS'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Géocodage réussi.',
        schema: {
            type: 'object',
            properties: {
                latitude: { type: 'number', example: 5.3364 },
                longitude: { type: 'number', example: -4.0267 },
                formattedAddress: { type: 'string', example: 'Rue des Jardins, Cocody, Abidjan, Côte d\'Ivoire' },
                city: { type: 'string', example: 'Abidjan' },
                district: { type: 'string', example: 'Cocody' },
                confidence: { type: 'number', example: 0.8 }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Impossible de géolocaliser cette adresse'
    }),
    __param(0, (0, common_1.Body)('address')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "geocodeAddress", null);
__decorate([
    (0, common_1.Post)('reverse-geocode'),
    (0, swagger_1.ApiOperation)({
        summary: 'Géocodage inverse',
        description: 'Convertit des coordonnées GPS en adresse'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Géocodage inverse réussi.',
        schema: {
            type: 'object',
            properties: {
                street: { type: 'string', example: 'Rue de la République' },
                city: { type: 'string', example: 'Abidjan' },
                district: { type: 'string', example: 'Plateau' },
                formattedAddress: { type: 'string', example: 'Rue de la République, Plateau, Abidjan, Côte d\'Ivoire' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Coordonnées invalides ou hors de Côte d\'Ivoire'
    }),
    __param(0, (0, common_1.Body)('latitude')),
    __param(1, (0, common_1.Body)('longitude')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], AddressesController.prototype, "reverseGeocode", null);
exports.AddressesController = AddressesController = __decorate([
    (0, swagger_1.ApiTags)('Adresses'),
    (0, common_1.Controller)('addresses'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [addresses_service_1.AddressesService])
], AddressesController);
//# sourceMappingURL=addresses.controller.js.map