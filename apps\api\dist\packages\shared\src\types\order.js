"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ORDER_TYPE_LABELS = exports.ORDER_STATUS_MESSAGES = exports.PRICING_CONFIG = exports.OrderStatus = exports.OrderType = void 0;
var OrderType;
(function (OrderType) {
    OrderType["DELIVERY"] = "DELIVERY";
    OrderType["FOOD"] = "FOOD";
    OrderType["SHOPPING"] = "SHOPPING";
})(OrderType || (exports.OrderType = OrderType = {}));
var OrderStatus;
(function (OrderStatus) {
    OrderStatus["PENDING"] = "PENDING";
    OrderStatus["CONFIRMED"] = "CONFIRMED";
    OrderStatus["ASSIGNED"] = "ASSIGNED";
    OrderStatus["PICKED_UP"] = "PICKED_UP";
    OrderStatus["IN_TRANSIT"] = "IN_TRANSIT";
    OrderStatus["DELIVERED"] = "DELIVERED";
    OrderStatus["CANCELLED"] = "CANCELLED";
    OrderStatus["FAILED"] = "FAILED";
})(OrderStatus || (exports.OrderStatus = OrderStatus = {}));
exports.PRICING_CONFIG = {
    BASE_PRICES: {
        [OrderType.DELIVERY]: 1000,
        [OrderType.FOOD]: 1500,
        [OrderType.SHOPPING]: 2000,
    },
    PRICE_PER_KM: 200,
    PRICE_PER_MINUTE: 50,
    FREE_DISTANCE: 2,
    NIGHT_SURCHARGE: 0.5,
    WEEKEND_SURCHARGE: 0.3,
    RAIN_SURCHARGE: 0.2,
    MIN_ORDER_AMOUNT: 500,
    MAX_DISTANCE: 50,
};
exports.ORDER_STATUS_MESSAGES = {
    [OrderStatus.PENDING]: 'Commande en attente de confirmation',
    [OrderStatus.CONFIRMED]: 'Commande confirmée, recherche d\'un livreur',
    [OrderStatus.ASSIGNED]: 'Livreur assigné, préparation en cours',
    [OrderStatus.PICKED_UP]: 'Commande récupérée, en route vers vous',
    [OrderStatus.IN_TRANSIT]: 'Commande en transit',
    [OrderStatus.DELIVERED]: 'Commande livrée avec succès',
    [OrderStatus.CANCELLED]: 'Commande annulée',
    [OrderStatus.FAILED]: 'Échec de la livraison',
};
exports.ORDER_TYPE_LABELS = {
    [OrderType.DELIVERY]: 'Livraison Express',
    [OrderType.FOOD]: 'Livraison de Repas',
    [OrderType.SHOPPING]: 'Courses et Achats',
};
//# sourceMappingURL=order.js.map