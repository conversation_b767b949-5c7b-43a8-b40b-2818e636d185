"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateOrderDto = exports.CreateOrderItemDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const shared_1 = require("@wali/shared");
class CreateOrderItemDto {
}
exports.CreateOrderItemDto = CreateOrderItemDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nom de l\'article',
        example: 'Riz au gras',
        minLength: 2,
        maxLength: 100
    }),
    (0, class_validator_1.IsString)({ message: 'Le nom de l\'article doit être une chaîne de caractères' }),
    (0, class_validator_1.MinLength)(2, { message: 'Le nom de l\'article doit contenir au moins 2 caractères' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Le nom de l\'article ne peut pas dépasser 100 caractères' }),
    __metadata("design:type", String)
], CreateOrderItemDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Description de l\'article (optionnel)',
        example: 'Riz au gras avec poulet et légumes',
        required: false,
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'La description doit être une chaîne de caractères' }),
    (0, class_validator_1.MaxLength)(500, { message: 'La description ne peut pas dépasser 500 caractères' }),
    __metadata("design:type", String)
], CreateOrderItemDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quantité',
        example: 2,
        minimum: 1,
        maximum: 100
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La quantité doit être un nombre' }),
    (0, class_validator_1.Min)(1, { message: 'La quantité doit être au moins 1' }),
    (0, class_validator_1.Max)(100, { message: 'La quantité ne peut pas dépasser 100' }),
    __metadata("design:type", Number)
], CreateOrderItemDto.prototype, "quantity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Prix unitaire en FCFA',
        example: 2500,
        minimum: 0
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'Le prix unitaire doit être un nombre' }),
    (0, class_validator_1.Min)(0, { message: 'Le prix unitaire ne peut pas être négatif' }),
    __metadata("design:type", Number)
], CreateOrderItemDto.prototype, "unitPrice", void 0);
class CreateOrderDto {
}
exports.CreateOrderDto = CreateOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type de commande',
        enum: shared_1.OrderType,
        example: shared_1.OrderType.FOOD
    }),
    (0, class_validator_1.IsEnum)(shared_1.OrderType, { message: 'Le type de commande doit être DELIVERY, FOOD ou SHOPPING' }),
    __metadata("design:type", typeof (_a = typeof shared_1.OrderType !== "undefined" && shared_1.OrderType) === "function" ? _a : Object)
], CreateOrderDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Adresse de récupération',
        example: 'Restaurant Chez Tante Marie, Rue des Jardins, Cocody',
        minLength: 10,
        maxLength: 200
    }),
    (0, class_validator_1.IsString)({ message: 'L\'adresse de récupération doit être une chaîne de caractères' }),
    (0, class_validator_1.MinLength)(10, { message: 'L\'adresse de récupération doit contenir au moins 10 caractères' }),
    (0, class_validator_1.MaxLength)(200, { message: 'L\'adresse de récupération ne peut pas dépasser 200 caractères' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "pickupAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Latitude de l\'adresse de récupération',
        example: 5.3364,
        minimum: -90,
        maximum: 90
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La latitude de récupération doit être un nombre' }),
    (0, class_validator_1.Min)(-90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    (0, class_validator_1.Max)(90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "pickupLatitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Longitude de l\'adresse de récupération',
        example: -4.0267,
        minimum: -180,
        maximum: 180
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La longitude de récupération doit être un nombre' }),
    (0, class_validator_1.Min)(-180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    (0, class_validator_1.Max)(180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "pickupLongitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Adresse de livraison',
        example: 'Villa 12, Résidence Les Palmiers, Cocody',
        minLength: 10,
        maxLength: 200
    }),
    (0, class_validator_1.IsString)({ message: 'L\'adresse de livraison doit être une chaîne de caractères' }),
    (0, class_validator_1.MinLength)(10, { message: 'L\'adresse de livraison doit contenir au moins 10 caractères' }),
    (0, class_validator_1.MaxLength)(200, { message: 'L\'adresse de livraison ne peut pas dépasser 200 caractères' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "deliveryAddress", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Latitude de l\'adresse de livraison',
        example: 5.3400,
        minimum: -90,
        maximum: 90
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La latitude de livraison doit être un nombre' }),
    (0, class_validator_1.Min)(-90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    (0, class_validator_1.Max)(90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "deliveryLatitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Longitude de l\'adresse de livraison',
        example: -4.0300,
        minimum: -180,
        maximum: 180
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La longitude de livraison doit être un nombre' }),
    (0, class_validator_1.Min)(-180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    (0, class_validator_1.Max)(180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    __metadata("design:type", Number)
], CreateOrderDto.prototype, "deliveryLongitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notes ou instructions spéciales (optionnel)',
        example: 'Sonner à l\'interphone, appartement 3A',
        required: false,
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Les notes doivent être une chaîne de caractères' }),
    (0, class_validator_1.MaxLength)(500, { message: 'Les notes ne peuvent pas dépasser 500 caractères' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date et heure de livraison souhaitée (optionnel)',
        example: '2024-01-15T14:30:00Z',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'La date de livraison doit être au format ISO 8601' }),
    __metadata("design:type", String)
], CreateOrderDto.prototype, "scheduledAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Liste des articles à livrer',
        type: [CreateOrderItemDto],
        minItems: 1
    }),
    (0, class_validator_1.IsArray)({ message: 'Les articles doivent être un tableau' }),
    (0, class_validator_1.ArrayMinSize)(1, { message: 'Au moins un article est requis' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => CreateOrderItemDto),
    __metadata("design:type", Array)
], CreateOrderDto.prototype, "items", void 0);
//# sourceMappingURL=create-order.dto.js.map