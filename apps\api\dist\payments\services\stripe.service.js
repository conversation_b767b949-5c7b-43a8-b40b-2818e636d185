"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var StripeService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StripeService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const stripe_1 = require("stripe");
let StripeService = StripeService_1 = class StripeService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(StripeService_1.name);
        this.stripe = new stripe_1.default(this.configService.get('STRIPE_SECRET_KEY'), {
            apiVersion: '2023-10-16',
        });
    }
    async createPaymentIntent(amount, currency = 'xof', metadata) {
        try {
            const paymentIntent = await this.stripe.paymentIntents.create({
                amount: Math.round(amount * 100),
                currency,
                metadata,
                automatic_payment_methods: {
                    enabled: true,
                },
            });
            this.logger.log(`PaymentIntent créé: ${paymentIntent.id}`);
            return paymentIntent;
        }
        catch (error) {
            this.logger.error('Erreur lors de la création du PaymentIntent', error);
            throw error;
        }
    }
    async confirmPaymentIntent(paymentIntentId, paymentMethodId) {
        try {
            const paymentIntent = await this.stripe.paymentIntents.confirm(paymentIntentId, {
                payment_method: paymentMethodId,
            });
            this.logger.log(`PaymentIntent confirmé: ${paymentIntent.id}`);
            return paymentIntent;
        }
        catch (error) {
            this.logger.error('Erreur lors de la confirmation du PaymentIntent', error);
            throw error;
        }
    }
    async retrievePaymentIntent(paymentIntentId) {
        try {
            return await this.stripe.paymentIntents.retrieve(paymentIntentId);
        }
        catch (error) {
            this.logger.error('Erreur lors de la récupération du PaymentIntent', error);
            throw error;
        }
    }
    async createRefund(paymentIntentId, amount, reason) {
        try {
            const refundData = {
                payment_intent: paymentIntentId,
            };
            if (amount) {
                refundData.amount = Math.round(amount * 100);
            }
            if (reason) {
                refundData.reason = reason;
            }
            const refund = await this.stripe.refunds.create(refundData);
            this.logger.log(`Remboursement créé: ${refund.id}`);
            return refund;
        }
        catch (error) {
            this.logger.error('Erreur lors de la création du remboursement', error);
            throw error;
        }
    }
    async constructWebhookEvent(payload, signature) {
        const webhookSecret = this.configService.get('STRIPE_WEBHOOK_SECRET');
        try {
            return this.stripe.webhooks.constructEvent(payload, signature, webhookSecret);
        }
        catch (error) {
            this.logger.error('Erreur lors de la vérification du webhook Stripe', error);
            throw error;
        }
    }
    async createCustomer(email, phone, name, metadata) {
        try {
            const customerData = {};
            if (email)
                customerData.email = email;
            if (phone)
                customerData.phone = phone;
            if (name)
                customerData.name = name;
            if (metadata)
                customerData.metadata = metadata;
            const customer = await this.stripe.customers.create(customerData);
            this.logger.log(`Client Stripe créé: ${customer.id}`);
            return customer;
        }
        catch (error) {
            this.logger.error('Erreur lors de la création du client Stripe', error);
            throw error;
        }
    }
    async attachPaymentMethod(paymentMethodId, customerId) {
        try {
            const paymentMethod = await this.stripe.paymentMethods.attach(paymentMethodId, {
                customer: customerId,
            });
            this.logger.log(`Méthode de paiement attachée: ${paymentMethod.id}`);
            return paymentMethod;
        }
        catch (error) {
            this.logger.error('Erreur lors de l\'attachement de la méthode de paiement', error);
            throw error;
        }
    }
    async listCustomerPaymentMethods(customerId, type = 'card') {
        try {
            const paymentMethods = await this.stripe.paymentMethods.list({
                customer: customerId,
                type: type,
            });
            return paymentMethods.data;
        }
        catch (error) {
            this.logger.error('Erreur lors de la récupération des méthodes de paiement', error);
            throw error;
        }
    }
    async createSetupIntent(customerId, metadata) {
        try {
            const setupIntent = await this.stripe.setupIntents.create({
                customer: customerId,
                metadata,
                automatic_payment_methods: {
                    enabled: true,
                },
            });
            this.logger.log(`SetupIntent créé: ${setupIntent.id}`);
            return setupIntent;
        }
        catch (error) {
            this.logger.error('Erreur lors de la création du SetupIntent', error);
            throw error;
        }
    }
};
exports.StripeService = StripeService;
exports.StripeService = StripeService = StripeService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], StripeService);
//# sourceMappingURL=stripe.service.js.map