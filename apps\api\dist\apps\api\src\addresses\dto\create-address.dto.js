"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAddressDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const shared_1 = require("../../../../../packages/shared/src");
class CreateAddressDto {
    constructor() {
        this.isDefault = false;
    }
}
exports.CreateAddressDto = CreateAddressDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Libellé de l\'adresse (ex: Maison, Bureau)',
        example: 'Maison',
        required: false,
        maxLength: 50
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Le libellé doit être une chaîne de caractères' }),
    (0, class_validator_1.MaxLength)(50, { message: 'Le libellé ne peut pas dépasser 50 caractères' }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "label", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Rue et numéro de l\'adresse',
        example: 'Rue des Jardins, Résidence Les Palmiers, Villa 12',
        minLength: 5,
        maxLength: 200
    }),
    (0, class_validator_1.IsString)({ message: 'La rue doit être une chaîne de caractères' }),
    (0, class_validator_1.MinLength)(5, { message: 'La rue doit contenir au moins 5 caractères' }),
    (0, class_validator_1.MaxLength)(200, { message: 'La rue ne peut pas dépasser 200 caractères' }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "street", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Ville en Côte d\'Ivoire',
        example: 'Abidjan',
        enum: shared_1.IVORY_COAST_CITIES
    }),
    (0, class_validator_1.IsString)({ message: 'La ville doit être une chaîne de caractères' }),
    (0, class_validator_1.IsIn)(shared_1.IVORY_COAST_CITIES, {
        message: 'La ville doit être une ville valide de Côte d\'Ivoire'
    }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Quartier ou district (obligatoire pour Abidjan)',
        example: 'Cocody',
        required: false,
        enum: shared_1.ABIDJAN_DISTRICTS
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Le quartier doit être une chaîne de caractères' }),
    (0, class_validator_1.MaxLength)(100, { message: 'Le quartier ne peut pas dépasser 100 caractères' }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Point de repère pour faciliter la localisation',
        example: 'Près de la pharmacie du carrefour',
        required: false,
        maxLength: 200
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Le point de repère doit être une chaîne de caractères' }),
    (0, class_validator_1.MaxLength)(200, { message: 'Le point de repère ne peut pas dépasser 200 caractères' }),
    __metadata("design:type", String)
], CreateAddressDto.prototype, "landmark", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Latitude GPS de l\'adresse',
        example: 5.3364,
        minimum: -90,
        maximum: 90
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La latitude doit être un nombre' }),
    (0, class_validator_1.Min)(-90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    (0, class_validator_1.Max)(90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    __metadata("design:type", Number)
], CreateAddressDto.prototype, "latitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Longitude GPS de l\'adresse',
        example: -4.0267,
        minimum: -180,
        maximum: 180
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La longitude doit être un nombre' }),
    (0, class_validator_1.Min)(-180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    (0, class_validator_1.Max)(180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    __metadata("design:type", Number)
], CreateAddressDto.prototype, "longitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Définir comme adresse par défaut',
        example: false,
        required: false,
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'isDefault doit être un booléen' }),
    __metadata("design:type", Boolean)
], CreateAddressDto.prototype, "isDefault", void 0);
//# sourceMappingURL=create-address.dto.js.map