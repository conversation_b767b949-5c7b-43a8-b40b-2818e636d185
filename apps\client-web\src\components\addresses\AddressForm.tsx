'use client';

import { useState, useEffect } from 'react';
import { useAddresses } from '@/hooks/useAddresses';
import { 
  CreateAddressRequest, 
  UpdateAddressRequest, 
  Address,
  IVORY_COAST_CITIES,
  ABIDJAN_DISTRICTS 
} from '@wali/shared';

interface AddressFormProps {
  address?: Address;
  onSuccess?: (address: Address) => void;
  onCancel?: () => void;
}

export default function AddressForm({ address, onSuccess, onCancel }: AddressFormProps) {
  const { 
    createAddress, 
    updateAddress, 
    geocodeAddress, 
    getCurrentLocation,
    isLoading, 
    error, 
    clearError 
  } = useAddresses();

  const [formData, setFormData] = useState<CreateAddressRequest>({
    label: '',
    street: '',
    city: 'Abidjan',
    district: '',
    landmark: '',
    latitude: 5.3364, // Coordonnées par défaut d'Abidjan
    longitude: -4.0267,
    isDefault: false,
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isGeolocating, setIsGeolocating] = useState(false);

  // Initialiser le formulaire avec les données de l'adresse existante
  useEffect(() => {
    if (address) {
      setFormData({
        label: address.label || '',
        street: address.street,
        city: address.city,
        district: address.district || '',
        landmark: address.landmark || '',
        latitude: address.latitude,
        longitude: address.longitude,
        isDefault: address.isDefault,
      });
    }
  }, [address]);

  const handleInputChange = (field: keyof CreateAddressRequest, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    clearError();
    setValidationErrors([]);
  };

  const validateForm = (): string[] => {
    const errors: string[] = [];

    if (!formData.street.trim() || formData.street.length < 5) {
      errors.push('La rue doit contenir au moins 5 caractères');
    }

    if (!formData.city) {
      errors.push('La ville est requise');
    }

    if (formData.city === 'Abidjan' && !formData.district) {
      errors.push('Le quartier est obligatoire pour Abidjan');
    }

    if (formData.latitude < -90 || formData.latitude > 90) {
      errors.push('La latitude doit être comprise entre -90 et 90');
    }

    if (formData.longitude < -180 || formData.longitude > 180) {
      errors.push('La longitude doit être comprise entre -180 et 180');
    }

    return errors;
  };

  const handleGeocodeAddress = async () => {
    if (!formData.street.trim() || !formData.city) {
      setValidationErrors(['Veuillez remplir la rue et la ville avant de géolocaliser']);
      return;
    }

    setIsGeolocating(true);
    try {
      const addressString = `${formData.street}, ${formData.district ? formData.district + ', ' : ''}${formData.city}`;
      const result = await geocodeAddress(addressString);
      
      setFormData(prev => ({
        ...prev,
        latitude: result.latitude,
        longitude: result.longitude,
        city: result.city,
        district: result.district || prev.district,
      }));

      setValidationErrors([]);
    } catch (error) {
      console.error('Erreur géocodage:', error);
    } finally {
      setIsGeolocating(false);
    }
  };

  const handleGetCurrentLocation = async () => {
    setIsGeolocating(true);
    try {
      const location = await getCurrentLocation();
      setFormData(prev => ({
        ...prev,
        latitude: location.latitude,
        longitude: location.longitude,
      }));
      setValidationErrors([]);
    } catch (error) {
      setValidationErrors([error instanceof Error ? error.message : 'Erreur de géolocalisation']);
    } finally {
      setIsGeolocating(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    clearError();

    // Validation côté client
    const errors = validateForm();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    try {
      let result: Address;
      if (address) {
        // Mise à jour
        const updateData: UpdateAddressRequest = {};
        if (formData.label !== address.label) updateData.label = formData.label;
        if (formData.street !== address.street) updateData.street = formData.street;
        if (formData.city !== address.city) updateData.city = formData.city;
        if (formData.district !== address.district) updateData.district = formData.district;
        if (formData.landmark !== address.landmark) updateData.landmark = formData.landmark;
        if (formData.latitude !== address.latitude) updateData.latitude = formData.latitude;
        if (formData.longitude !== address.longitude) updateData.longitude = formData.longitude;
        if (formData.isDefault !== address.isDefault) updateData.isDefault = formData.isDefault;

        result = await updateAddress(address.id, updateData);
      } else {
        // Création
        result = await createAddress(formData);
      }

      onSuccess?.(result);
    } catch (error) {
      console.error('Erreur sauvegarde adresse:', error);
    }
  };

  return (
    <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {address ? 'Modifier l\'adresse' : 'Nouvelle adresse'}
        </h2>
        {onCancel && (
          <button
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        )}
      </div>

      {/* Affichage des erreurs */}
      {(error || validationErrors.length > 0) && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
          {error && <p>{error}</p>}
          {validationErrors.map((err, index) => (
            <p key={index}>{err}</p>
          ))}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Libellé */}
        <div>
          <label htmlFor="label" className="block text-sm font-medium text-gray-700">
            Libellé (optionnel)
          </label>
          <input
            type="text"
            id="label"
            value={formData.label}
            onChange={(e) => handleInputChange('label', e.target.value)}
            placeholder="Maison, Bureau, Chez maman..."
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
          />
        </div>

        {/* Rue */}
        <div>
          <label htmlFor="street" className="block text-sm font-medium text-gray-700">
            Rue et numéro *
          </label>
          <input
            type="text"
            id="street"
            value={formData.street}
            onChange={(e) => handleInputChange('street', e.target.value)}
            placeholder="Rue des Jardins, Résidence Les Palmiers, Villa 12"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
            required
          />
        </div>

        {/* Ville et Quartier */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="city" className="block text-sm font-medium text-gray-700">
              Ville *
            </label>
            <select
              id="city"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              required
            >
              {IVORY_COAST_CITIES.map(city => (
                <option key={city} value={city}>{city}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="district" className="block text-sm font-medium text-gray-700">
              Quartier {formData.city === 'Abidjan' && '*'}
            </label>
            {formData.city === 'Abidjan' ? (
              <select
                id="district"
                value={formData.district}
                onChange={(e) => handleInputChange('district', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                required={formData.city === 'Abidjan'}
              >
                <option value="">Sélectionner un quartier</option>
                {ABIDJAN_DISTRICTS.map(district => (
                  <option key={district} value={district}>{district}</option>
                ))}
              </select>
            ) : (
              <input
                type="text"
                id="district"
                value={formData.district}
                onChange={(e) => handleInputChange('district', e.target.value)}
                placeholder="Quartier ou zone"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
              />
            )}
          </div>
        </div>

        {/* Point de repère */}
        <div>
          <label htmlFor="landmark" className="block text-sm font-medium text-gray-700">
            Point de repère (optionnel)
          </label>
          <input
            type="text"
            id="landmark"
            value={formData.landmark}
            onChange={(e) => handleInputChange('landmark', e.target.value)}
            placeholder="Près de la pharmacie du carrefour, face à l'école..."
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
          />
        </div>

        {/* Coordonnées GPS */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Coordonnées GPS
          </label>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="latitude" className="block text-xs text-gray-500">
                Latitude
              </label>
              <input
                type="number"
                id="latitude"
                value={formData.latitude}
                onChange={(e) => handleInputChange('latitude', parseFloat(e.target.value))}
                step="0.000001"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                required
              />
            </div>
            <div>
              <label htmlFor="longitude" className="block text-xs text-gray-500">
                Longitude
              </label>
              <input
                type="number"
                id="longitude"
                value={formData.longitude}
                onChange={(e) => handleInputChange('longitude', parseFloat(e.target.value))}
                step="0.000001"
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                required
              />
            </div>
          </div>

          {/* Boutons de géolocalisation */}
          <div className="flex space-x-2 mt-2">
            <button
              type="button"
              onClick={handleGeocodeAddress}
              disabled={isGeolocating}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium disabled:opacity-50"
            >
              {isGeolocating ? 'Géolocalisation...' : '📍 Géolocaliser l\'adresse'}
            </button>
            <button
              type="button"
              onClick={handleGetCurrentLocation}
              disabled={isGeolocating}
              className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium disabled:opacity-50"
            >
              {isGeolocating ? 'Localisation...' : '🎯 Ma position'}
            </button>
          </div>
        </div>

        {/* Adresse par défaut */}
        <div className="flex items-center">
          <input
            type="checkbox"
            id="isDefault"
            checked={formData.isDefault}
            onChange={(e) => handleInputChange('isDefault', e.target.checked)}
            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
          />
          <label htmlFor="isDefault" className="ml-2 block text-sm text-gray-900">
            Définir comme adresse par défaut
          </label>
        </div>

        {/* Boutons d'action */}
        <div className="flex justify-end space-x-3">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium"
            >
              Annuler
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading}
            className="bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Enregistrement...' : (address ? 'Modifier' : 'Créer')}
          </button>
        </div>
      </form>
    </div>
  );
}
