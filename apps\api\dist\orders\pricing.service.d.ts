import { PriceCalculationRequest, PriceCalculationResult } from '@wali/shared';
import { DistanceService } from './distance.service';
export declare class PricingService {
    private readonly distanceService;
    private readonly logger;
    constructor(distanceService: DistanceService);
    calculatePrice(request: PriceCalculationRequest): Promise<PriceCalculationResult>;
    private calculateDistance;
    private calculateTypeFee;
    private calculateItemsFee;
    private calculateSurcharges;
    validateOrder(request: PriceCalculationRequest): {
        valid: boolean;
        errors: string[];
    };
    private isWithinIvoryCoast;
    private toRadians;
    estimateDeliveryTime(pickupLat: number, pickupLon: number, deliveryLat: number, deliveryLon: number): number;
    private isInPlateau;
}
