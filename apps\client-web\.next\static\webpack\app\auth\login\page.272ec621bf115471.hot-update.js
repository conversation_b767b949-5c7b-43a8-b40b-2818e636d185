"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./src/app/auth/login/page.tsx":
/*!*************************************!*\
  !*** ./src/app/auth/login/page.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LoginPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2,Mail,Package,Phone!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2,Mail,Package,Phone!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2,Mail,Package,Phone!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2,Mail,Package,Phone!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2,Mail,Package,Phone!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2,Mail,Package,Phone!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Eye,EyeOff,Loader2,Mail,Package,Phone!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useWaliAuth */ \"(app-pages-browser)/./src/hooks/useWaliAuth.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/../../node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login, isLoading, error, clearError } = (0,_hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__.useWaliAuth)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        phone: \"\",\n        password: \"\",\n        rememberMe: false\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loginMethod, setLoginMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"email\");\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Validation en temps réel\n    const validateField = (field, value)=>{\n        const errors = {\n            ...validationErrors\n        };\n        switch(field){\n            case \"email\":\n                if (value && !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_12__.validateEmail)(value)) {\n                    errors.email = \"Format d'email invalide\";\n                } else {\n                    delete errors.email;\n                }\n                break;\n            case \"phone\":\n                if (value && !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_12__.validatePhone)(value)) {\n                    errors.phone = \"Format de t\\xe9l\\xe9phone invalide (ex: +225 01 23 45 67 89)\";\n                } else {\n                    delete errors.phone;\n                }\n                break;\n            case \"password\":\n                if (value && value.length < 6) {\n                    errors.password = \"Le mot de passe doit contenir au moins 6 caract\\xe8res\";\n                } else {\n                    delete errors.password;\n                }\n                break;\n        }\n        setValidationErrors(errors);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        validateField(field, value);\n        if (error) clearError();\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation finale\n        const identifier = loginMethod === \"email\" ? formData.email : formData.phone;\n        if (!identifier || !formData.password) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Veuillez remplir tous les champs\");\n            return;\n        }\n        if (Object.keys(validationErrors).length > 0) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(\"Veuillez corriger les erreurs dans le formulaire\");\n            return;\n        }\n        try {\n            const credentials = {\n                [loginMethod]: identifier,\n                password: formData.password\n            };\n            await login(credentials);\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"Connexion r\\xe9ussie !\");\n            router.push(\"/wali-dashboard\");\n        } catch (err) {\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(err.message || \"Erreur de connexion\");\n        }\n    };\n    // Comptes de démonstration\n    const demoAccounts = [\n        {\n            type: \"Client\",\n            email: \"<EMAIL>\",\n            phone: \"+***********\",\n            password: \"password123\",\n            description: \"Compte client pour passer des commandes\"\n        },\n        {\n            type: \"Livreur\",\n            email: \"<EMAIL>\",\n            phone: \"+***********\",\n            password: \"password123\",\n            description: \"Compte livreur pour accepter des livraisons\"\n        }\n    ];\n    const fillDemoAccount = (account)=>{\n        setFormData((prev)=>({\n                ...prev,\n                email: account.email,\n                phone: account.phone,\n                password: account.password\n            }));\n        setLoginMethod(\"email\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-12 w-12 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-2xl font-bold text-gray-900\",\n                                    children: \"WALI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Connexion\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"Connectez-vous \\xe0 votre compte WALI Livraison\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    children: \"Se connecter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    children: \"Utilisez votre email ou num\\xe9ro de t\\xe9l\\xe9phone\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.Tabs, {\n                                        value: loginMethod,\n                                        onValueChange: (value)=>setLoginMethod(value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsList, {\n                                                className: \"grid w-full grid-cols-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                        value: \"email\",\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Email\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsTrigger, {\n                                                        value: \"phone\",\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"T\\xe9l\\xe9phone\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                                value: \"email\",\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"email\",\n                                                            children: \"Adresse email\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"email\",\n                                                            type: \"email\",\n                                                            placeholder: \"<EMAIL>\",\n                                                            value: formData.email,\n                                                            onChange: (e)=>handleInputChange(\"email\", e.target.value),\n                                                            className: validationErrors.email ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.email\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_9__.TabsContent, {\n                                                value: \"phone\",\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"phone\",\n                                                            children: \"Num\\xe9ro de t\\xe9l\\xe9phone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"phone\",\n                                                            type: \"tel\",\n                                                            placeholder: \"+225 01 23 45 67 89\",\n                                                            value: formData.phone,\n                                                            onChange: (e)=>handleInputChange(\"phone\", e.target.value),\n                                                            className: validationErrors.phone ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.phone\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                htmlFor: \"password\",\n                                                children: \"Mot de passe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        id: \"password\",\n                                                        type: showPassword ? \"text\" : \"password\",\n                                                        placeholder: \"Votre mot de passe\",\n                                                        value: formData.password,\n                                                        onChange: (e)=>handleInputChange(\"password\", e.target.value),\n                                                        className: \"pr-10 \".concat(validationErrors.password ? \"border-red-500\" : \"\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPassword(!showPassword),\n                                                        className: \"absolute inset-y-0 right-0 pr-3 flex items-center\",\n                                                        children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 17\n                                            }, this),\n                                            validationErrors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-red-600 mt-1\",\n                                                children: validationErrors.password\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_10__.Checkbox, {\n                                                        id: \"remember\",\n                                                        checked: formData.rememberMe,\n                                                        onCheckedChange: (checked)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    rememberMe: checked\n                                                                }))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"remember\",\n                                                        className: \"text-sm\",\n                                                        children: \"Se souvenir de moi\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/auth/forgot-password\",\n                                                className: \"text-sm text-blue-600 hover:underline\",\n                                                children: \"Mot de passe oubli\\xe9 ?\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.Alert, {\n                                        className: \"border-red-200 bg-red-50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-4 w-4 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_8__.AlertDescription, {\n                                                className: \"text-red-700\",\n                                                children: error\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        className: \"w-full\",\n                                        disabled: isLoading,\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Eye_EyeOff_Loader2_Mail_Package_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"Connexion en cours...\"\n                                            ]\n                                        }, void 0, true) : \"Se connecter\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                    className: \"border-blue-200 bg-blue-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                    className: \"text-blue-900\",\n                                    children: \"\\uD83E\\uDDEA Comptes de D\\xe9monstration\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                    className: \"text-blue-700\",\n                                    children: \"Testez l'application avec ces comptes pr\\xe9d\\xe9finis\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: demoAccounts.map((account, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-3 bg-white rounded-lg border\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-sm\",\n                                                            children: account.type\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 290,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-600\",\n                                                            children: account.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: [\n                                                                account.email,\n                                                                \" • \",\n                                                                account.phone\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    size: \"sm\",\n                                                    variant: \"outline\",\n                                                    onClick: ()=>fillDemoAccount(account),\n                                                    children: \"Utiliser\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Pas encore de compte ?\",\n                            \" \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                href: \"/auth/register\",\n                                className: \"text-blue-600 hover:underline font-medium\",\n                                children: \"Cr\\xe9er un compte\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                        href: \"/\",\n                        className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                        children: \"← Retour \\xe0 l'accueil\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n            lineNumber: 130,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\auth\\\\login\\\\page.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_s(LoginPage, \"l1Dxu2bVIcDYSM5ixPOTyKOBB6Y=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__.useWaliAuth\n    ];\n});\n_c = LoginPage;\nvar _c;\n$RefreshReg$(_c, \"LoginPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/login/page.tsx\n"));

/***/ })

});