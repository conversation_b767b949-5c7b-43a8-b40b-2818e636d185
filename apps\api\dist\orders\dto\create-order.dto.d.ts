import { OrderType } from '@wali/shared';
export declare class CreateOrderItemDto {
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
}
export declare class CreateOrderDto {
    type: OrderType;
    pickupAddress: string;
    pickupLatitude: number;
    pickupLongitude: number;
    deliveryAddress: string;
    deliveryLatitude: number;
    deliveryLongitude: number;
    notes?: string;
    scheduledAt?: string;
    items: CreateOrderItemDto[];
}
