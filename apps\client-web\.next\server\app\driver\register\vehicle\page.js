/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/driver/register/vehicle/page";
exports.ids = ["app/driver/register/vehicle/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdriver%2Fregister%2Fvehicle%2Fpage&page=%2Fdriver%2Fregister%2Fvehicle%2Fpage&appPaths=%2Fdriver%2Fregister%2Fvehicle%2Fpage&pagePath=private-next-app-dir%2Fdriver%2Fregister%2Fvehicle%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdriver%2Fregister%2Fvehicle%2Fpage&page=%2Fdriver%2Fregister%2Fvehicle%2Fpage&appPaths=%2Fdriver%2Fregister%2Fvehicle%2Fpage&pagePath=private-next-app-dir%2Fdriver%2Fregister%2Fvehicle%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?b784\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'driver',\n        {\n        children: [\n        'register',\n        {\n        children: [\n        'vehicle',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/driver/register/vehicle/page.tsx */ \"(rsc)/./src/app/driver/register/vehicle/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/driver/register/vehicle/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/driver/register/vehicle/page\",\n        pathname: \"/driver/register/vehicle\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdriver%2Fregister%2Fvehicle%2Fpage&page=%2Fdriver%2Fregister%2Fvehicle%2Fpage&appPaths=%2Fdriver%2Fregister%2Fvehicle%2Fpage&pagePath=private-next-app-dir%2Fdriver%2Fregister%2Fvehicle%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/__barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!../../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!../../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertCircle: () => (/* reexport safe */ _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ArrowLeft: () => (/* reexport safe */ _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   ArrowRight: () => (/* reexport safe */ _icons_arrow_right_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Bike: () => (/* reexport safe */ _icons_bike_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Car: () => (/* reexport safe */ _icons_car_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Truck: () => (/* reexport safe */ _icons_truck_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_alert_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/alert-circle.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _icons_arrow_left_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/arrow-left.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _icons_arrow_right_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/arrow-right.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _icons_bike_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/bike.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/bike.js\");\n/* harmony import */ var _icons_car_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/car.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/check-circle.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/package.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _icons_truck_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/truck.js */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/truck.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */ \n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1BbGVydENpcmNsZSxBcnJvd0xlZnQsQXJyb3dSaWdodCxCaWtlLENhcixDaGVja0NpcmNsZSxNb3RvcmN5Y2xlLFBhY2thZ2UsVHJ1Y2shPSEuLi8uLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ2dFO0FBQ0o7QUFDRTtBQUNiO0FBQ0Y7QUFDaUI7QUFDVCIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/M2JmMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovIFxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBbGVydENpcmNsZSB9IGZyb20gXCIuL2ljb25zL2FsZXJ0LWNpcmNsZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEFycm93TGVmdCB9IGZyb20gXCIuL2ljb25zL2Fycm93LWxlZnQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBBcnJvd1JpZ2h0IH0gZnJvbSBcIi4vaWNvbnMvYXJyb3ctcmlnaHQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCaWtlIH0gZnJvbSBcIi4vaWNvbnMvYmlrZS5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENhciB9IGZyb20gXCIuL2ljb25zL2Nhci5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENoZWNrQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMvY2hlY2stY2lyY2xlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFja2FnZSB9IGZyb20gXCIuL2ljb25zL3BhY2thZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcnVjayB9IGZyb20gXCIuL2ljb25zL3RydWNrLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/__barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Cdriver%5Cregister%5Cvehicle%5Cpage.tsx&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Cdriver%5Cregister%5Cvehicle%5Cpage.tsx&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/driver/register/vehicle/page.tsx */ \"(ssr)/./src/app/driver/register/vehicle/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0VsaXNlZSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNNaWVudGlvciUyMGxpdnJhaXNvbiUyMGFwcCU1Q2FwcHMlNUNjbGllbnQtd2ViJTVDc3JjJTVDYXBwJTVDZHJpdmVyJTVDcmVnaXN0ZXIlNUN2ZWhpY2xlJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8/YzRhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEVsaXNlZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxNaWVudGlvciBsaXZyYWlzb24gYXBwXFxcXGFwcHNcXFxcY2xpZW50LXdlYlxcXFxzcmNcXFxcYXBwXFxcXGRyaXZlclxcXFxyZWdpc3RlclxcXFx2ZWhpY2xlXFxcXHBhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Cdriver%5Cregister%5Cvehicle%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Ccomponents%5Cmaps%5CGoogleMapsProvider.tsx&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Csonner%5Cdist%5Cindex.mjs&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Ccomponents%5Cmaps%5CGoogleMapsProvider.tsx&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Csonner%5Cdist%5Cindex.mjs&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/maps/GoogleMapsProvider.tsx */ \"(ssr)/./src/components/maps/GoogleMapsProvider.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/sonner/dist/index.mjs */ \"(ssr)/../../node_modules/sonner/dist/index.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0VsaXNlZSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNNaWVudGlvciUyMGxpdnJhaXNvbiUyMGFwcCU1Q2FwcHMlNUNjbGllbnQtd2ViJTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1DJTNBJTVDVXNlcnMlNUNFbGlzZWUlNUNEb2N1bWVudHMlNUNhdWdtZW50LXByb2plY3RzJTVDTWllbnRpb3IlMjBsaXZyYWlzb24lMjBhcHAlNUNhcHBzJTVDY2xpZW50LXdlYiU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNtYXBzJTVDR29vZ2xlTWFwc1Byb3ZpZGVyLnRzeCZtb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0VsaXNlZSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNNaWVudGlvciUyMGxpdnJhaXNvbiUyMGFwcCU1Q25vZGVfbW9kdWxlcyU1Q3Nvbm5lciU1Q2Rpc3QlNUNpbmRleC5tanMmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUE0SztBQUM1SyIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvPzY5YzQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxFbGlzZWVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcTWllbnRpb3IgbGl2cmFpc29uIGFwcFxcXFxhcHBzXFxcXGNsaWVudC13ZWJcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcbWFwc1xcXFxHb29nbGVNYXBzUHJvdmlkZXIudHN4XCIpO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxFbGlzZWVcXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcTWllbnRpb3IgbGl2cmFpc29uIGFwcFxcXFxub2RlX21vZHVsZXNcXFxcc29ubmVyXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Ccomponents%5Cmaps%5CGoogleMapsProvider.tsx&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Csonner%5Cdist%5Cindex.mjs&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/driver/register/vehicle/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/driver/register/vehicle/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DriverVehicleRegistrationPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(ssr)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/__barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/bike.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/driver-verification */ \"(ssr)/./src/lib/driver-verification.ts\");\n/* harmony import */ var _hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useDriver */ \"(ssr)/./src/hooks/useDriver.ts\");\n/* harmony import */ var _hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useWaliAuth */ \"(ssr)/./src/hooks/useWaliAuth.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DriverVehicleRegistrationPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isDriver } = (0,_hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__.useWaliAuth)();\n    const { createProfile, isLoading } = (0,_hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__.useDriver)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.MOTO,\n        brand: \"\",\n        model: \"\",\n        year: new Date().getFullYear(),\n        color: \"\",\n        registrationNumber: \"\",\n        engineNumber: \"\",\n        chassisNumber: \"\"\n    });\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Redirection si pas livreur\n    if (user && !isDriver) {\n        router.push(\"/dashboard\");\n        return null;\n    }\n    const vehicleTypeOptions = [\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.MOTO,\n            label: \"Motocyclette\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Motorcycle,\n            description: \"Id\\xe9al pour les livraisons rapides en ville\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.SCOOTER,\n            label: \"Scooter\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Motorcycle,\n            description: \"Parfait pour les courtes distances\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.VELO,\n            label: \"V\\xe9lo\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: \"\\xc9cologique et \\xe9conomique\",\n            requiresLicense: false\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.VOITURE,\n            label: \"Voiture\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: \"Pour les gros colis et longues distances\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.TRICYCLE,\n            label: \"Tricycle\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Capacit\\xe9 de charge importante\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.CAMIONNETTE,\n            label: \"Camionnette\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Pour les livraisons volumineuses\",\n            requiresLicense: true\n        }\n    ];\n    const validateField = (field, value)=>{\n        const errors = {\n            ...validationErrors\n        };\n        switch(field){\n            case \"brand\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.brand = \"La marque est obligatoire\";\n                } else {\n                    delete errors.brand;\n                }\n                break;\n            case \"model\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.model = \"Le mod\\xe8le est obligatoire\";\n                } else {\n                    delete errors.model;\n                }\n                break;\n            case \"color\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.color = \"La couleur est obligatoire\";\n                } else {\n                    delete errors.color;\n                }\n                break;\n            case \"registrationNumber\":\n                if (!value || typeof value === \"string\" && value.trim().length === 0) {\n                    errors.registrationNumber = \"Le num\\xe9ro d'immatriculation est obligatoire\";\n                } else if (typeof value === \"string\" && !(0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.validateIvorianPlateNumber)(value)) {\n                    errors.registrationNumber = \"Format invalide (ex: 1234 CI 01)\";\n                } else {\n                    delete errors.registrationNumber;\n                }\n                break;\n            case \"year\":\n                const currentYear = new Date().getFullYear();\n                if (typeof value === \"number\" && (value < 1990 || value > currentYear)) {\n                    errors.year = `L'année doit être entre 1990 et ${currentYear}`;\n                } else {\n                    delete errors.year;\n                }\n                break;\n        }\n        setValidationErrors(errors);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        validateField(field, value);\n    };\n    const handleVehicleTypeChange = (type)=>{\n        setFormData((prev)=>({\n                ...prev,\n                type,\n                brand: \"\",\n                model: \"\"\n            }));\n        // Clear brand/model errors when type changes\n        const errors = {\n            ...validationErrors\n        };\n        delete errors.brand;\n        delete errors.model;\n        setValidationErrors(errors);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation finale\n        const requiredFields = [\n            \"brand\",\n            \"model\",\n            \"color\",\n            \"registrationNumber\"\n        ];\n        const missingFields = requiredFields.filter((field)=>!formData[field] || String(formData[field]).trim().length === 0);\n        if (missingFields.length > 0) {\n            alert(\"Veuillez remplir tous les champs obligatoires\");\n            return;\n        }\n        if (Object.keys(validationErrors).length > 0) {\n            alert(\"Veuillez corriger les erreurs dans le formulaire\");\n            return;\n        }\n        try {\n            await createProfile(formData);\n            // Redirection selon le type de véhicule\n            if ((0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.requiresLicense)(formData.type)) {\n                router.push(\"/driver/register/documents\");\n            } else {\n                router.push(\"/driver/dashboard\");\n            }\n        } catch (error) {\n        // L'erreur est déjà gérée par le hook\n        }\n    };\n    const availableBrands = _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VEHICLE_BRANDS[formData.type] || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-2xl font-bold text-gray-900\",\n                                    children: \"WALI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Inscription Livreur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"\\xc9tape 1 : Informations sur votre v\\xe9hicule\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Progression\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_8__.Progress, {\n                            value: 33,\n                            className: \"h-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Informations du v\\xe9hicule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Renseignez les d\\xe9tails de votre v\\xe9hicule de livraison\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Type de v\\xe9hicule *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\",\n                                                            children: vehicleTypeOptions.map((option)=>{\n                                                                const IconComponent = option.icon;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: `p-4 border rounded-lg cursor-pointer transition-all ${formData.type === option.value ? \"border-blue-500 bg-blue-50 ring-2 ring-blue-200\" : \"border-gray-200 hover:border-gray-300\"}`,\n                                                                    onClick: ()=>handleVehicleTypeChange(option.value),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"h-6 w-6 text-blue-600 mt-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: option.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                                        children: option.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 265,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    option.requiresLicense && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-orange-600 mt-1\",\n                                                                                        children: \"⚠️ Permis de conduire requis\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 267,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 27\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"brand\",\n                                                            children: \"Marque *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.brand,\n                                                            onValueChange: (value)=>handleInputChange(\"brand\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    className: validationErrors.brand ? \"border-red-500\" : \"\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"S\\xe9lectionnez la marque\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: availableBrands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.brand\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"model\",\n                                                                    children: \"Mod\\xe8le *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"model\",\n                                                                    placeholder: \"Ex: CB 125, Corolla, etc.\",\n                                                                    value: formData.model,\n                                                                    onChange: (e)=>handleInputChange(\"model\", e.target.value),\n                                                                    className: validationErrors.model ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                validationErrors.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-1\",\n                                                                    children: validationErrors.model\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"year\",\n                                                                    children: \"Ann\\xe9e\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"year\",\n                                                                    type: \"number\",\n                                                                    min: \"1990\",\n                                                                    max: new Date().getFullYear(),\n                                                                    value: formData.year,\n                                                                    onChange: (e)=>handleInputChange(\"year\", parseInt(e.target.value)),\n                                                                    className: validationErrors.year ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                validationErrors.year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-1\",\n                                                                    children: validationErrors.year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"color\",\n                                                            children: \"Couleur *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"color\",\n                                                            placeholder: \"Ex: Rouge, Bleu, Noir, etc.\",\n                                                            value: formData.color,\n                                                            onChange: (e)=>handleInputChange(\"color\", e.target.value),\n                                                            className: validationErrors.color ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.color && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.color\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"registrationNumber\",\n                                                            children: \"Num\\xe9ro d'immatriculation *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"registrationNumber\",\n                                                            placeholder: \"Ex: 1234 CI 01\",\n                                                            value: formData.registrationNumber,\n                                                            onChange: (e)=>handleInputChange(\"registrationNumber\", e.target.value.toUpperCase()),\n                                                            className: validationErrors.registrationNumber ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.registrationNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.registrationNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"Format ivoirien : 4 chiffres + CI + 2 chiffres\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"engineNumber\",\n                                                                    children: \"Num\\xe9ro de moteur (optionnel)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"engineNumber\",\n                                                                    placeholder: \"Num\\xe9ro grav\\xe9 sur le moteur\",\n                                                                    value: formData.engineNumber,\n                                                                    onChange: (e)=>handleInputChange(\"engineNumber\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"chassisNumber\",\n                                                                    children: \"Num\\xe9ro de ch\\xe2ssis (optionnel)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"chassisNumber\",\n                                                                    placeholder: \"Num\\xe9ro d'identification du v\\xe9hicule\",\n                                                                    value: formData.chassisNumber,\n                                                                    onChange: (e)=>handleInputChange(\"chassisNumber\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                                href: \"/auth/register\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Retour\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"submit\",\n                                                            disabled: isLoading,\n                                                            children: isLoading ? \"Enregistrement...\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    \"Continuer\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-blue-900\",\n                                                children: \"\\uD83D\\uDCCB Documents requis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: (0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.requiresLicense)(formData.type) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Permis de conduire valide\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Assurance v\\xe9hicule\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Carte grise\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Aucun document requis pour les v\\xe9los\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"\\uD83D\\uDCA1 Conseils\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"text-sm space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• V\\xe9rifiez que votre v\\xe9hicule est en bon \\xe9tat\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Assurez-vous d'avoir tous les documents \\xe0 jour\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Les informations doivent correspondre exactement aux documents officiels\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/driver/register/vehicle/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/maps/GoogleMapsProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/maps/GoogleMapsProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleMapsProvider: () => (/* binding */ GoogleMapsProvider),\n/* harmony export */   useGoogleMaps: () => (/* binding */ useGoogleMaps)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/script */ \"(ssr)/../../node_modules/next/script.js\");\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_script__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useGoogleMaps,GoogleMapsProvider auto */ \n\n\nconst GoogleMapsContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isLoaded: false,\n    loadError: null\n});\nconst useGoogleMaps = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(GoogleMapsContext);\n    if (!context) {\n        throw new Error(\"useGoogleMaps must be used within a GoogleMapsProvider\");\n    }\n    return context;\n};\nconst GoogleMapsProvider = ({ children })=>{\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loadError, setLoadError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scriptLoaded, setScriptLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const apiKey = \"AIzaSyBgNfqhT6lk47eh3gA0Oc9uxsB16r5lTMY\";\n    // Vérifier si Google Maps est déjà chargé\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (window.google?.maps) {\n            setIsLoaded(true);\n            setScriptLoaded(true);\n        }\n    }, []);\n    const handleScriptLoad = ()=>{\n        console.log(\"✅ Google Maps API loaded successfully\");\n        setScriptLoaded(true);\n        // Vérifier que l'API est vraiment disponible avec un délai\n        setTimeout(()=>{\n            if (window.google?.maps) {\n                setIsLoaded(true);\n                setLoadError(null);\n            } else {\n                setLoadError(\"Google Maps API loaded but not available\");\n            }\n        }, 100);\n    };\n    const handleScriptError = (error)=>{\n        console.error(\"❌ Failed to load Google Maps API:\", error);\n        setLoadError(\"Failed to load Google Maps API\");\n        setIsLoaded(false);\n    };\n    if (!apiKey) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleMapsContext.Provider, {\n            value: {\n                isLoaded: false,\n                loadError: \"Google Maps API key not configured\"\n            },\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\GoogleMapsProvider.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleMapsContext.Provider, {\n        value: {\n            isLoaded,\n            loadError\n        },\n        children: [\n            !scriptLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_script__WEBPACK_IMPORTED_MODULE_2___default()), {\n                src: `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places,marker&language=fr&region=CI&loading=async`,\n                strategy: \"afterInteractive\",\n                onLoad: handleScriptLoad,\n                onError: handleScriptError\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\GoogleMapsProvider.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\GoogleMapsProvider.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/maps/GoogleMapsProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 11,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBS2hDLE1BQU1FLHNCQUFRRiw2Q0FBZ0IsQ0FDNUIsQ0FBQyxFQUFFSSxTQUFTLEVBQUVDLElBQUksRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlCLHFCQUNFLDhEQUFDQztRQUNDSCxNQUFNQTtRQUNORCxXQUFXSCw4Q0FBRUEsQ0FDWCxnV0FDQUc7UUFFRkcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixNQUFNTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi9zcmMvY29tcG9uZW50cy91aS9pbnB1dC50c3g/Yzk4MyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgSW5wdXRQcm9wc1xuICBleHRlbmRzIFJlYWN0LklucHV0SFRNTEF0dHJpYnV0ZXM8SFRNTElucHV0RWxlbWVudD4ge31cblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIElucHV0UHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtc20gcmluZy1vZmZzZXQtYmFja2dyb3VuZCBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgZGlzYWJsZWQ6b3BhY2l0eS01MFwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/label.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/label.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/../../node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/../../node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 16,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9sYWJlbC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3lCO0FBQ1U7QUFFakM7QUFFaEMsTUFBTUksZ0JBQWdCRiw2REFBR0EsQ0FDdkI7QUFHRixNQUFNRyxzQkFBUUwsNkNBQWdCLENBSTVCLENBQUMsRUFBRU8sU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDUix1REFBbUI7UUFDbEJRLEtBQUtBO1FBQ0xGLFdBQVdKLDhDQUFFQSxDQUFDQyxpQkFBaUJHO1FBQzlCLEdBQUdDLEtBQUs7Ozs7OztBQUdiSCxNQUFNTSxXQUFXLEdBQUdWLHVEQUFtQixDQUFDVSxXQUFXO0FBRW5DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8uL3NyYy9jb21wb25lbnRzL3VpL2xhYmVsLnRzeD8xM2ViIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgKiBhcyBMYWJlbFByaW1pdGl2ZSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LWxhYmVsXCJcbmltcG9ydCB7IGN2YSwgdHlwZSBWYXJpYW50UHJvcHMgfSBmcm9tIFwiY2xhc3MtdmFyaWFuY2UtYXV0aG9yaXR5XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBsYWJlbFZhcmlhbnRzID0gY3ZhKFxuICBcInRleHQtc20gZm9udC1tZWRpdW0gbGVhZGluZy1ub25lIHBlZXItZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIHBlZXItZGlzYWJsZWQ6b3BhY2l0eS03MFwiXG4pXG5cbmNvbnN0IExhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgTGFiZWxQcmltaXRpdmUuUm9vdD4gJlxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgbGFiZWxWYXJpYW50cz5cbj4oKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgPExhYmVsUHJpbWl0aXZlLlJvb3RcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKGxhYmVsVmFyaWFudHMoKSwgY2xhc3NOYW1lKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuTGFiZWwuZGlzcGxheU5hbWUgPSBMYWJlbFByaW1pdGl2ZS5Sb290LmRpc3BsYXlOYW1lXG5cbmV4cG9ydCB7IExhYmVsIH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsUHJpbWl0aXZlIiwiY3ZhIiwiY24iLCJsYWJlbFZhcmlhbnRzIiwiTGFiZWwiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJSb290IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/progress.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/progress.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-progress */ \"(ssr)/../../node_modules/@radix-ui/react-progress/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Progress auto */ \n\n\n\nconst Progress = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, value, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: \"h-full w-full flex-1 bg-primary transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\progress.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nProgress.displayName = _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/select.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/select.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-select */ \"(ssr)/../../node_modules/@radix-ui/react-select/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,ChevronUp!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst Select = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst SelectGroup = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst SelectValue = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Value;\nconst SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"h-4 w-4 opacity-50\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 26,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 17,\n        columnNumber: 3\n    }, undefined));\nSelectTrigger.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 45,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 37,\n        columnNumber: 3\n    }, undefined));\nSelectScrollUpButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollUpButton.displayName;\nconst SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default items-center justify-center py-1\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 62,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 54,\n        columnNumber: 3\n    }, undefined));\nSelectScrollDownButton.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ScrollDownButton.displayName;\nconst SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, position = \"popper\", ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", position === \"popper\" && \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\", className),\n            position: position,\n            ...props,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollUpButton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Viewport, {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-1\", position === \"popper\" && \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SelectScrollDownButton, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n            lineNumber: 73,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 72,\n        columnNumber: 3\n    }, undefined));\nSelectContent.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 104,\n        columnNumber: 3\n    }, undefined));\nSelectLabel.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_ChevronUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 124,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.ItemText, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n                lineNumber: 130,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 116,\n        columnNumber: 3\n    }, undefined));\nSelectItem.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\ui\\\\select.tsx\",\n        lineNumber: 139,\n        columnNumber: 3\n    }, undefined));\nSelectSeparator.displayName = _radix_ui_react_select__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/select.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useDriver.ts":
/*!********************************!*\
  !*** ./src/hooks/useDriver.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDriver: () => (/* binding */ useDriver)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/driver-verification */ \"(ssr)/./src/lib/driver-verification.ts\");\n/* harmony import */ var _services_driver_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/driver.service */ \"(ssr)/./src/services/driver.service.ts\");\n/* harmony import */ var _useWaliAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useWaliAuth */ \"(ssr)/./src/hooks/useWaliAuth.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! sonner */ \"(ssr)/../../node_modules/sonner/dist/index.mjs\");\n\n\n\n\n\nconst useDriver = ()=>{\n    const { user } = (0,_useWaliAuth__WEBPACK_IMPORTED_MODULE_3__.useWaliAuth)();\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Charger le profil livreur\n    const loadProfile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!user?.id) return;\n        try {\n            setIsLoading(true);\n            const driverProfile = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.getDriverProfileByUserId(user.id);\n            setProfile(driverProfile);\n            if (driverProfile) {\n                const driverStats = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.getDriverStats(driverProfile.id);\n                setStats(driverStats);\n            }\n        } catch (err) {\n            setError(err.message || \"Erreur lors du chargement du profil\");\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        user?.id\n    ]);\n    // Charger le profil au montage\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        loadProfile();\n    }, [\n        loadProfile\n    ]);\n    // Créer un profil livreur\n    const createProfile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (vehicleInfo)=>{\n        if (!user?.id) {\n            throw new Error(\"Utilisateur non connect\\xe9\");\n        }\n        setIsLoading(true);\n        setError(null);\n        try {\n            const newProfile = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.createDriverProfile(user.id, vehicleInfo);\n            setProfile(newProfile);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Profil livreur cr\\xe9\\xe9 avec succ\\xe8s !\");\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur lors de la cr\\xe9ation du profil\";\n            setError(errorMessage);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        user?.id\n    ]);\n    // Mettre à jour le profil\n    const updateProfile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (updates)=>{\n        if (!profile) {\n            throw new Error(\"Aucun profil livreur trouv\\xe9\");\n        }\n        setIsLoading(true);\n        setError(null);\n        try {\n            const updatedProfile = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.updateDriverProfile(profile.id, updates);\n            setProfile(updatedProfile);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Profil mis \\xe0 jour avec succ\\xe8s !\");\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur lors de la mise \\xe0 jour\";\n            setError(errorMessage);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        profile\n    ]);\n    // Mettre à jour le permis de conduire\n    const updateDrivingLicense = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (license)=>{\n        if (!profile) {\n            throw new Error(\"Aucun profil livreur trouv\\xe9\");\n        }\n        setIsLoading(true);\n        setError(null);\n        try {\n            const updatedProfile = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.updateDrivingLicense(profile.id, license);\n            setProfile(updatedProfile);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Informations de permis mises \\xe0 jour !\");\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur lors de la mise \\xe0 jour du permis\";\n            setError(errorMessage);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        profile\n    ]);\n    // Mettre à jour l'assurance\n    const updateInsurance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (insurance)=>{\n        if (!profile) {\n            throw new Error(\"Aucun profil livreur trouv\\xe9\");\n        }\n        setIsLoading(true);\n        setError(null);\n        try {\n            const updatedProfile = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.updateInsurance(profile.id, insurance);\n            setProfile(updatedProfile);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Informations d'assurance mises \\xe0 jour !\");\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur lors de la mise \\xe0 jour de l'assurance\";\n            setError(errorMessage);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        profile\n    ]);\n    // Mettre à jour la carte grise\n    const updateRegistration = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (registration)=>{\n        if (!profile) {\n            throw new Error(\"Aucun profil livreur trouv\\xe9\");\n        }\n        setIsLoading(true);\n        setError(null);\n        try {\n            const updatedProfile = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.updateRegistration(profile.id, registration);\n            setProfile(updatedProfile);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Informations de carte grise mises \\xe0 jour !\");\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur lors de la mise \\xe0 jour de la carte grise\";\n            setError(errorMessage);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n            throw err;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        profile\n    ]);\n    // Uploader un document\n    const uploadDocument = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (documentType, file)=>{\n        if (!profile) {\n            throw new Error(\"Aucun profil livreur trouv\\xe9\");\n        }\n        setError(null);\n        try {\n            const url = await _services_driver_service__WEBPACK_IMPORTED_MODULE_2__.driverService.uploadDocument(profile.id, documentType, file);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Document upload\\xe9 avec succ\\xe8s !\");\n            return url;\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur lors de l'upload du document\";\n            setError(errorMessage);\n            sonner__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n            throw err;\n        }\n    }, [\n        profile\n    ]);\n    // Rafraîchir le profil\n    const refreshProfile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        await loadProfile();\n    }, [\n        loadProfile\n    ]);\n    // Effacer l'erreur\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    // Calculer si le livreur peut accepter des commandes\n    const canAcceptOrders = profile?.status === _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.DriverStatus.VERIFIED;\n    // Message de statut\n    const statusMessage = profile ? (0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.getStatusMessage)(profile.status) : \"\";\n    // Documents manquants\n    const missingDocuments = profile ? getMissingDocuments(profile) : [];\n    // Progression des documents (pourcentage)\n    const documentsProgress = profile ? calculateDocumentsProgress(profile) : 0;\n    return {\n        // État\n        profile,\n        isLoading,\n        error,\n        stats,\n        // Actions\n        createProfile,\n        updateProfile,\n        updateDrivingLicense,\n        updateInsurance,\n        updateRegistration,\n        uploadDocument,\n        refreshProfile,\n        clearError,\n        // Utilitaires\n        canAcceptOrders,\n        statusMessage,\n        missingDocuments,\n        documentsProgress\n    };\n};\n// Utilitaires privés\nfunction getMissingDocuments(profile) {\n    const missing = [];\n    // Vérifier selon le type de véhicule\n    const vehicleType = profile.vehicle.type;\n    if (vehicleType !== \"VELO\") {\n        if (!profile.drivingLicense || profile.drivingLicense.status === _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.NOT_UPLOADED) {\n            missing.push(\"Permis de conduire\");\n        }\n        if (!profile.insurance || profile.insurance.status === _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.NOT_UPLOADED) {\n            missing.push(\"Assurance v\\xe9hicule\");\n        }\n        if (!profile.registration || profile.registration.status === _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.NOT_UPLOADED) {\n            missing.push(\"Carte grise\");\n        }\n    }\n    return missing;\n}\nfunction calculateDocumentsProgress(profile) {\n    const vehicleType = profile.vehicle.type;\n    if (vehicleType === \"VELO\") {\n        return 100; // Pas de documents requis pour les vélos\n    }\n    let totalDocuments = 3; // permis, assurance, carte grise\n    let completedDocuments = 0;\n    if (profile.drivingLicense && profile.drivingLicense.status === _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.APPROVED) {\n        completedDocuments++;\n    }\n    if (profile.insurance && profile.insurance.status === _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.APPROVED) {\n        completedDocuments++;\n    }\n    if (profile.registration && profile.registration.status === _lib_driver_verification__WEBPACK_IMPORTED_MODULE_1__.DocumentStatus.APPROVED) {\n        completedDocuments++;\n    }\n    return Math.round(completedDocuments / totalDocuments * 100);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useDriver.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useWaliAuth.ts":
/*!**********************************!*\
  !*** ./src/hooks/useWaliAuth.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWaliAuth: () => (/* binding */ useWaliAuth),\n/* harmony export */   useWaliPermissions: () => (/* binding */ useWaliPermissions),\n/* harmony export */   useWaliUser: () => (/* binding */ useWaliUser)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n/* harmony import */ var _services_auth_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/auth.service */ \"(ssr)/./src/services/auth.service.ts\");\n\n\n\nconst useWaliAuth = ()=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialisation - vérifier si l'utilisateur est connecté\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const initAuth = async ()=>{\n            try {\n                if (_services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.isAuthenticated()) {\n                    const currentUser = _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.getCurrentUser();\n                    setUser(currentUser);\n                }\n            } catch (err) {\n                console.error(\"Erreur d'initialisation auth:\", err);\n                setError(\"Erreur d'initialisation\");\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        initAuth();\n    }, []);\n    // Connexion\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (credentials)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.login(credentials);\n            setUser(response.user);\n            // Notification de succès\n            if (false) {}\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur de connexion\";\n            setError(errorMessage);\n            throw new Error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    // Inscription\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.register(data);\n            setUser(response.user);\n            // Notification de succès\n            if (false) {}\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur d'inscription\";\n            setError(errorMessage);\n            throw new Error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    // Déconnexion\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.logout();\n            setUser(null);\n            // Notification de déconnexion\n            if (false) {}\n        } catch (err) {\n            console.error(\"Erreur de d\\xe9connexion:\", err);\n            // Même en cas d'erreur, on déconnecte localement\n            setUser(null);\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    // Mise à jour du profil\n    const updateProfile = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (updates)=>{\n        if (!user) {\n            throw new Error(\"Utilisateur non connect\\xe9\");\n        }\n        setIsLoading(true);\n        setError(null);\n        try {\n            const updatedUser = await _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.updateProfile(updates);\n            setUser(updatedUser);\n            // Notification de mise à jour\n            if (false) {}\n        } catch (err) {\n            const errorMessage = err.message || \"Erreur de mise \\xe0 jour\";\n            setError(errorMessage);\n            throw new Error(errorMessage);\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        user\n    ]);\n    // Effacer l'erreur\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    // Vérifier le rôle\n    const hasRole = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((role)=>{\n        return user?.role === role;\n    }, [\n        user\n    ]);\n    // Utilitaires de rôle\n    const isClient = hasRole(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.UserRole.CLIENT);\n    const isDriver = hasRole(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.UserRole.DRIVER);\n    const isAdmin = hasRole(_lib_auth__WEBPACK_IMPORTED_MODULE_1__.UserRole.ADMIN);\n    // État d'authentification\n    const isAuthenticated = user !== null && _services_auth_service__WEBPACK_IMPORTED_MODULE_2__.authService.isAuthenticated();\n    return {\n        // État\n        user,\n        isAuthenticated,\n        isLoading,\n        error,\n        // Actions\n        login,\n        register,\n        logout,\n        updateProfile,\n        clearError,\n        // Utilitaires\n        hasRole,\n        isClient,\n        isDriver,\n        isAdmin\n    };\n};\n// Hook pour les données utilisateur uniquement (sans actions)\nconst useWaliUser = ()=>{\n    const { user, isAuthenticated, isLoading } = useWaliAuth();\n    return {\n        user,\n        isAuthenticated,\n        isLoading\n    };\n};\n// Hook pour vérifier les permissions\nconst useWaliPermissions = ()=>{\n    const { user, hasRole, isClient, isDriver, isAdmin } = useWaliAuth();\n    const canCreateOrder = isClient || isAdmin;\n    const canAcceptOrder = isDriver || isAdmin;\n    const canViewAllOrders = isAdmin;\n    const canManageUsers = isAdmin;\n    return {\n        user,\n        hasRole,\n        isClient,\n        isDriver,\n        isAdmin,\n        canCreateOrder,\n        canAcceptOrder,\n        canViewAllOrders,\n        canManageUsers\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvaG9va3MvdXNlV2FsaUF1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF5RDtBQU9yQztBQUNrQztBQXVCL0MsTUFBTUssY0FBYztJQUN6QixNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR1AsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDUSxXQUFXQyxhQUFhLEdBQUdULCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ1UsT0FBT0MsU0FBUyxHQUFHWCwrQ0FBUUEsQ0FBZ0I7SUFFbEQsMERBQTBEO0lBQzFEQyxnREFBU0EsQ0FBQztRQUNSLE1BQU1XLFdBQVc7WUFDZixJQUFJO2dCQUNGLElBQUlSLCtEQUFXQSxDQUFDUyxlQUFlLElBQUk7b0JBQ2pDLE1BQU1DLGNBQWNWLCtEQUFXQSxDQUFDVyxjQUFjO29CQUM5Q1IsUUFBUU87Z0JBQ1Y7WUFDRixFQUFFLE9BQU9FLEtBQUs7Z0JBQ1pDLFFBQVFQLEtBQUssQ0FBQyxpQ0FBa0NNO2dCQUNoREwsU0FBUztZQUNYLFNBQVU7Z0JBQ1JGLGFBQWE7WUFDZjtRQUNGO1FBRUFHO0lBQ0YsR0FBRyxFQUFFO0lBRUwsWUFBWTtJQUNaLE1BQU1NLFFBQVFoQixrREFBV0EsQ0FBQyxPQUFPaUI7UUFDL0JWLGFBQWE7UUFDYkUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNUyxXQUF5QixNQUFNaEIsK0RBQVdBLENBQUNjLEtBQUssQ0FBQ0M7WUFDdkRaLFFBQVFhLFNBQVNkLElBQUk7WUFFckIseUJBQXlCO1lBQ3pCLElBQUksS0FBa0IsRUFBYSxFQUtsQztRQUNILEVBQUUsT0FBT1UsS0FBVTtZQUNqQixNQUFNVyxlQUFlWCxJQUFJUSxPQUFPLElBQUk7WUFDcENiLFNBQVNnQjtZQUNULE1BQU0sSUFBSUMsTUFBTUQ7UUFDbEIsU0FBVTtZQUNSbEIsYUFBYTtRQUNmO0lBQ0YsR0FBRyxFQUFFO0lBRUwsY0FBYztJQUNkLE1BQU1vQixXQUFXM0Isa0RBQVdBLENBQUMsT0FBTzRCO1FBQ2xDckIsYUFBYTtRQUNiRSxTQUFTO1FBRVQsSUFBSTtZQUNGLE1BQU1TLFdBQXlCLE1BQU1oQiwrREFBV0EsQ0FBQ3lCLFFBQVEsQ0FBQ0M7WUFDMUR2QixRQUFRYSxTQUFTZCxJQUFJO1lBRXJCLHlCQUF5QjtZQUN6QixJQUFJLEtBQWtCLEVBQWEsRUFLbEM7UUFDSCxFQUFFLE9BQU9VLEtBQVU7WUFDakIsTUFBTVcsZUFBZVgsSUFBSVEsT0FBTyxJQUFJO1lBQ3BDYixTQUFTZ0I7WUFDVCxNQUFNLElBQUlDLE1BQU1EO1FBQ2xCLFNBQVU7WUFDUmxCLGFBQWE7UUFDZjtJQUNGLEdBQUcsRUFBRTtJQUVMLGNBQWM7SUFDZCxNQUFNc0IsU0FBUzdCLGtEQUFXQSxDQUFDO1FBQ3pCTyxhQUFhO1FBQ2JFLFNBQVM7UUFFVCxJQUFJO1lBQ0YsTUFBTVAsK0RBQVdBLENBQUMyQixNQUFNO1lBQ3hCeEIsUUFBUTtZQUVSLDhCQUE4QjtZQUM5QixJQUFJLEtBQWtCLEVBQWEsRUFHbEM7UUFDSCxFQUFFLE9BQU9TLEtBQVU7WUFDakJDLFFBQVFQLEtBQUssQ0FBQyw2QkFBMEJNO1lBQ3hDLGlEQUFpRDtZQUNqRFQsUUFBUTtRQUNWLFNBQVU7WUFDUkUsYUFBYTtRQUNmO0lBQ0YsR0FBRyxFQUFFO0lBRUwsd0JBQXdCO0lBQ3hCLE1BQU11QixnQkFBZ0I5QixrREFBV0EsQ0FBQyxPQUFPK0I7UUFDdkMsSUFBSSxDQUFDM0IsTUFBTTtZQUNULE1BQU0sSUFBSXNCLE1BQU07UUFDbEI7UUFFQW5CLGFBQWE7UUFDYkUsU0FBUztRQUVULElBQUk7WUFDRixNQUFNdUIsY0FBYyxNQUFNOUIsK0RBQVdBLENBQUM0QixhQUFhLENBQUNDO1lBQ3BEMUIsUUFBUTJCO1lBRVIsOEJBQThCO1lBQzlCLElBQUksS0FBa0IsRUFBYSxFQUtsQztRQUNILEVBQUUsT0FBT2xCLEtBQVU7WUFDakIsTUFBTVcsZUFBZVgsSUFBSVEsT0FBTyxJQUFJO1lBQ3BDYixTQUFTZ0I7WUFDVCxNQUFNLElBQUlDLE1BQU1EO1FBQ2xCLFNBQVU7WUFDUmxCLGFBQWE7UUFDZjtJQUNGLEdBQUc7UUFBQ0g7S0FBSztJQUVULG1CQUFtQjtJQUNuQixNQUFNNkIsYUFBYWpDLGtEQUFXQSxDQUFDO1FBQzdCUyxTQUFTO0lBQ1gsR0FBRyxFQUFFO0lBRUwsbUJBQW1CO0lBQ25CLE1BQU15QixVQUFVbEMsa0RBQVdBLENBQUMsQ0FBQ21DO1FBQzNCLE9BQU8vQixNQUFNK0IsU0FBU0E7SUFDeEIsR0FBRztRQUFDL0I7S0FBSztJQUVULHNCQUFzQjtJQUN0QixNQUFNZ0MsV0FBV0YsUUFBUWpDLCtDQUFRQSxDQUFDb0MsTUFBTTtJQUN4QyxNQUFNQyxXQUFXSixRQUFRakMsK0NBQVFBLENBQUNzQyxNQUFNO0lBQ3hDLE1BQU1DLFVBQVVOLFFBQVFqQywrQ0FBUUEsQ0FBQ3dDLEtBQUs7SUFFdEMsMEJBQTBCO0lBQzFCLE1BQU05QixrQkFBa0JQLFNBQVMsUUFBUUYsK0RBQVdBLENBQUNTLGVBQWU7SUFFcEUsT0FBTztRQUNMLE9BQU87UUFDUFA7UUFDQU87UUFDQUw7UUFDQUU7UUFFQSxVQUFVO1FBQ1ZRO1FBQ0FXO1FBQ0FFO1FBQ0FDO1FBQ0FHO1FBRUEsY0FBYztRQUNkQztRQUNBRTtRQUNBRTtRQUNBRTtJQUNGO0FBQ0YsRUFBRTtBQUVGLDhEQUE4RDtBQUN2RCxNQUFNRSxjQUFjO0lBQ3pCLE1BQU0sRUFBRXRDLElBQUksRUFBRU8sZUFBZSxFQUFFTCxTQUFTLEVBQUUsR0FBR0g7SUFDN0MsT0FBTztRQUFFQztRQUFNTztRQUFpQkw7SUFBVTtBQUM1QyxFQUFFO0FBRUYscUNBQXFDO0FBQzlCLE1BQU1xQyxxQkFBcUI7SUFDaEMsTUFBTSxFQUFFdkMsSUFBSSxFQUFFOEIsT0FBTyxFQUFFRSxRQUFRLEVBQUVFLFFBQVEsRUFBRUUsT0FBTyxFQUFFLEdBQUdyQztJQUV2RCxNQUFNeUMsaUJBQWlCUixZQUFZSTtJQUNuQyxNQUFNSyxpQkFBaUJQLFlBQVlFO0lBQ25DLE1BQU1NLG1CQUFtQk47SUFDekIsTUFBTU8saUJBQWlCUDtJQUV2QixPQUFPO1FBQ0xwQztRQUNBOEI7UUFDQUU7UUFDQUU7UUFDQUU7UUFDQUk7UUFDQUM7UUFDQUM7UUFDQUM7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2FsaS9jbGllbnQtd2ViLy4vc3JjL2hvb2tzL3VzZVdhbGlBdXRoLnRzPzc3MjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBcbiAgVXNlciwgXG4gIExvZ2luQ3JlZGVudGlhbHMsIFxuICBSZWdpc3RlckRhdGEsIFxuICBBdXRoUmVzcG9uc2UsXG4gIFVzZXJSb2xlIFxufSBmcm9tICdAL2xpYi9hdXRoJztcbmltcG9ydCB7IGF1dGhTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9hdXRoLnNlcnZpY2UnO1xuXG5pbnRlcmZhY2UgVXNlV2FsaUF1dGhSZXR1cm4ge1xuICAvLyDDiXRhdFxuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgaXNBdXRoZW50aWNhdGVkOiBib29sZWFuO1xuICBpc0xvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuXG4gIC8vIEFjdGlvbnNcbiAgbG9naW46IChjcmVkZW50aWFsczogTG9naW5DcmVkZW50aWFscykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgcmVnaXN0ZXI6IChkYXRhOiBSZWdpc3RlckRhdGEpID0+IFByb21pc2U8dm9pZD47XG4gIGxvZ291dDogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgdXBkYXRlUHJvZmlsZTogKHVwZGF0ZXM6IFBhcnRpYWw8VXNlcj4pID0+IFByb21pc2U8dm9pZD47XG4gIGNsZWFyRXJyb3I6ICgpID0+IHZvaWQ7XG5cbiAgLy8gVXRpbGl0YWlyZXNcbiAgaGFzUm9sZTogKHJvbGU6IFVzZXJSb2xlKSA9PiBib29sZWFuO1xuICBpc0NsaWVudDogYm9vbGVhbjtcbiAgaXNEcml2ZXI6IGJvb2xlYW47XG4gIGlzQWRtaW46IGJvb2xlYW47XG59XG5cbmV4cG9ydCBjb25zdCB1c2VXYWxpQXV0aCA9ICgpOiBVc2VXYWxpQXV0aFJldHVybiA9PiB7XG4gIGNvbnN0IFt1c2VyLCBzZXRVc2VyXSA9IHVzZVN0YXRlPFVzZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gIC8vIEluaXRpYWxpc2F0aW9uIC0gdsOpcmlmaWVyIHNpIGwndXRpbGlzYXRldXIgZXN0IGNvbm5lY3TDqVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGluaXRBdXRoID0gYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgaWYgKGF1dGhTZXJ2aWNlLmlzQXV0aGVudGljYXRlZCgpKSB7XG4gICAgICAgICAgY29uc3QgY3VycmVudFVzZXIgPSBhdXRoU2VydmljZS5nZXRDdXJyZW50VXNlcigpO1xuICAgICAgICAgIHNldFVzZXIoY3VycmVudFVzZXIpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyZXVyIGRcXCdpbml0aWFsaXNhdGlvbiBhdXRoOicsIGVycik7XG4gICAgICAgIHNldEVycm9yKCdFcnJldXIgZFxcJ2luaXRpYWxpc2F0aW9uJyk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpbml0QXV0aCgpO1xuICB9LCBbXSk7XG5cbiAgLy8gQ29ubmV4aW9uXG4gIGNvbnN0IGxvZ2luID0gdXNlQ2FsbGJhY2soYXN5bmMgKGNyZWRlbnRpYWxzOiBMb2dpbkNyZWRlbnRpYWxzKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlOiBBdXRoUmVzcG9uc2UgPSBhd2FpdCBhdXRoU2VydmljZS5sb2dpbihjcmVkZW50aWFscyk7XG4gICAgICBzZXRVc2VyKHJlc3BvbnNlLnVzZXIpO1xuICAgICAgXG4gICAgICAvLyBOb3RpZmljYXRpb24gZGUgc3VjY8Ooc1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIGNvbnN0IGV2ZW50ID0gbmV3IEN1c3RvbUV2ZW50KCdhdXRoOmxvZ2luJywgeyBcbiAgICAgICAgICBkZXRhaWw6IHsgdXNlcjogcmVzcG9uc2UudXNlciwgbWVzc2FnZTogcmVzcG9uc2UubWVzc2FnZSB9IFxuICAgICAgICB9KTtcbiAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIubWVzc2FnZSB8fCAnRXJyZXVyIGRlIGNvbm5leGlvbic7XG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gSW5zY3JpcHRpb25cbiAgY29uc3QgcmVnaXN0ZXIgPSB1c2VDYWxsYmFjayhhc3luYyAoZGF0YTogUmVnaXN0ZXJEYXRhKSA9PiB7XG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlOiBBdXRoUmVzcG9uc2UgPSBhd2FpdCBhdXRoU2VydmljZS5yZWdpc3RlcihkYXRhKTtcbiAgICAgIHNldFVzZXIocmVzcG9uc2UudXNlcik7XG4gICAgICBcbiAgICAgIC8vIE5vdGlmaWNhdGlvbiBkZSBzdWNjw6hzXG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgY29uc3QgZXZlbnQgPSBuZXcgQ3VzdG9tRXZlbnQoJ2F1dGg6cmVnaXN0ZXInLCB7IFxuICAgICAgICAgIGRldGFpbDogeyB1c2VyOiByZXNwb25zZS51c2VyLCBtZXNzYWdlOiByZXNwb25zZS5tZXNzYWdlIH0gXG4gICAgICAgIH0pO1xuICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChldmVudCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyci5tZXNzYWdlIHx8ICdFcnJldXIgZFxcJ2luc2NyaXB0aW9uJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvLyBEw6ljb25uZXhpb25cbiAgY29uc3QgbG9nb3V0ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBhd2FpdCBhdXRoU2VydmljZS5sb2dvdXQoKTtcbiAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICBcbiAgICAgIC8vIE5vdGlmaWNhdGlvbiBkZSBkw6ljb25uZXhpb25cbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgICAgICBjb25zdCBldmVudCA9IG5ldyBDdXN0b21FdmVudCgnYXV0aDpsb2dvdXQnKTtcbiAgICAgICAgd2luZG93LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJldXIgZGUgZMOpY29ubmV4aW9uOicsIGVycik7XG4gICAgICAvLyBNw6ptZSBlbiBjYXMgZCdlcnJldXIsIG9uIGTDqWNvbm5lY3RlIGxvY2FsZW1lbnRcbiAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gTWlzZSDDoCBqb3VyIGR1IHByb2ZpbFxuICBjb25zdCB1cGRhdGVQcm9maWxlID0gdXNlQ2FsbGJhY2soYXN5bmMgKHVwZGF0ZXM6IFBhcnRpYWw8VXNlcj4pID0+IHtcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXRpbGlzYXRldXIgbm9uIGNvbm5lY3TDqScpO1xuICAgIH1cblxuICAgIHNldElzTG9hZGluZyh0cnVlKTtcbiAgICBzZXRFcnJvcihudWxsKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB1cGRhdGVkVXNlciA9IGF3YWl0IGF1dGhTZXJ2aWNlLnVwZGF0ZVByb2ZpbGUodXBkYXRlcyk7XG4gICAgICBzZXRVc2VyKHVwZGF0ZWRVc2VyKTtcbiAgICAgIFxuICAgICAgLy8gTm90aWZpY2F0aW9uIGRlIG1pc2Ugw6Agam91clxuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgIGNvbnN0IGV2ZW50ID0gbmV3IEN1c3RvbUV2ZW50KCdhdXRoOnByb2ZpbGUtdXBkYXRlZCcsIHsgXG4gICAgICAgICAgZGV0YWlsOiB7IHVzZXI6IHVwZGF0ZWRVc2VyIH0gXG4gICAgICAgIH0pO1xuICAgICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChldmVudCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyOiBhbnkpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyci5tZXNzYWdlIHx8ICdFcnJldXIgZGUgbWlzZSDDoCBqb3VyJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFt1c2VyXSk7XG5cbiAgLy8gRWZmYWNlciBsJ2VycmV1clxuICBjb25zdCBjbGVhckVycm9yID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldEVycm9yKG51bGwpO1xuICB9LCBbXSk7XG5cbiAgLy8gVsOpcmlmaWVyIGxlIHLDtGxlXG4gIGNvbnN0IGhhc1JvbGUgPSB1c2VDYWxsYmFjaygocm9sZTogVXNlclJvbGUpOiBib29sZWFuID0+IHtcbiAgICByZXR1cm4gdXNlcj8ucm9sZSA9PT0gcm9sZTtcbiAgfSwgW3VzZXJdKTtcblxuICAvLyBVdGlsaXRhaXJlcyBkZSByw7RsZVxuICBjb25zdCBpc0NsaWVudCA9IGhhc1JvbGUoVXNlclJvbGUuQ0xJRU5UKTtcbiAgY29uc3QgaXNEcml2ZXIgPSBoYXNSb2xlKFVzZXJSb2xlLkRSSVZFUik7XG4gIGNvbnN0IGlzQWRtaW4gPSBoYXNSb2xlKFVzZXJSb2xlLkFETUlOKTtcblxuICAvLyDDiXRhdCBkJ2F1dGhlbnRpZmljYXRpb25cbiAgY29uc3QgaXNBdXRoZW50aWNhdGVkID0gdXNlciAhPT0gbnVsbCAmJiBhdXRoU2VydmljZS5pc0F1dGhlbnRpY2F0ZWQoKTtcblxuICByZXR1cm4ge1xuICAgIC8vIMOJdGF0XG4gICAgdXNlcixcbiAgICBpc0F1dGhlbnRpY2F0ZWQsXG4gICAgaXNMb2FkaW5nLFxuICAgIGVycm9yLFxuXG4gICAgLy8gQWN0aW9uc1xuICAgIGxvZ2luLFxuICAgIHJlZ2lzdGVyLFxuICAgIGxvZ291dCxcbiAgICB1cGRhdGVQcm9maWxlLFxuICAgIGNsZWFyRXJyb3IsXG5cbiAgICAvLyBVdGlsaXRhaXJlc1xuICAgIGhhc1JvbGUsXG4gICAgaXNDbGllbnQsXG4gICAgaXNEcml2ZXIsXG4gICAgaXNBZG1pbixcbiAgfTtcbn07XG5cbi8vIEhvb2sgcG91ciBsZXMgZG9ubsOpZXMgdXRpbGlzYXRldXIgdW5pcXVlbWVudCAoc2FucyBhY3Rpb25zKVxuZXhwb3J0IGNvbnN0IHVzZVdhbGlVc2VyID0gKCkgPT4ge1xuICBjb25zdCB7IHVzZXIsIGlzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nIH0gPSB1c2VXYWxpQXV0aCgpO1xuICByZXR1cm4geyB1c2VyLCBpc0F1dGhlbnRpY2F0ZWQsIGlzTG9hZGluZyB9O1xufTtcblxuLy8gSG9vayBwb3VyIHbDqXJpZmllciBsZXMgcGVybWlzc2lvbnNcbmV4cG9ydCBjb25zdCB1c2VXYWxpUGVybWlzc2lvbnMgPSAoKSA9PiB7XG4gIGNvbnN0IHsgdXNlciwgaGFzUm9sZSwgaXNDbGllbnQsIGlzRHJpdmVyLCBpc0FkbWluIH0gPSB1c2VXYWxpQXV0aCgpO1xuICBcbiAgY29uc3QgY2FuQ3JlYXRlT3JkZXIgPSBpc0NsaWVudCB8fCBpc0FkbWluO1xuICBjb25zdCBjYW5BY2NlcHRPcmRlciA9IGlzRHJpdmVyIHx8IGlzQWRtaW47XG4gIGNvbnN0IGNhblZpZXdBbGxPcmRlcnMgPSBpc0FkbWluO1xuICBjb25zdCBjYW5NYW5hZ2VVc2VycyA9IGlzQWRtaW47XG4gIFxuICByZXR1cm4ge1xuICAgIHVzZXIsXG4gICAgaGFzUm9sZSxcbiAgICBpc0NsaWVudCxcbiAgICBpc0RyaXZlcixcbiAgICBpc0FkbWluLFxuICAgIGNhbkNyZWF0ZU9yZGVyLFxuICAgIGNhbkFjY2VwdE9yZGVyLFxuICAgIGNhblZpZXdBbGxPcmRlcnMsXG4gICAgY2FuTWFuYWdlVXNlcnMsXG4gIH07XG59O1xuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJVc2VyUm9sZSIsImF1dGhTZXJ2aWNlIiwidXNlV2FsaUF1dGgiLCJ1c2VyIiwic2V0VXNlciIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJpbml0QXV0aCIsImlzQXV0aGVudGljYXRlZCIsImN1cnJlbnRVc2VyIiwiZ2V0Q3VycmVudFVzZXIiLCJlcnIiLCJjb25zb2xlIiwibG9naW4iLCJjcmVkZW50aWFscyIsInJlc3BvbnNlIiwiZXZlbnQiLCJDdXN0b21FdmVudCIsImRldGFpbCIsIm1lc3NhZ2UiLCJ3aW5kb3ciLCJkaXNwYXRjaEV2ZW50IiwiZXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJyZWdpc3RlciIsImRhdGEiLCJsb2dvdXQiLCJ1cGRhdGVQcm9maWxlIiwidXBkYXRlcyIsInVwZGF0ZWRVc2VyIiwiY2xlYXJFcnJvciIsImhhc1JvbGUiLCJyb2xlIiwiaXNDbGllbnQiLCJDTElFTlQiLCJpc0RyaXZlciIsIkRSSVZFUiIsImlzQWRtaW4iLCJBRE1JTiIsInVzZVdhbGlVc2VyIiwidXNlV2FsaVBlcm1pc3Npb25zIiwiY2FuQ3JlYXRlT3JkZXIiLCJjYW5BY2NlcHRPcmRlciIsImNhblZpZXdBbGxPcmRlcnMiLCJjYW5NYW5hZ2VVc2VycyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useWaliAuth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JWT_CONFIG: () => (/* binding */ JWT_CONFIG),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   formatIvorianPhone: () => (/* binding */ formatIvorianPhone),\n/* harmony export */   getTokenPayload: () => (/* binding */ getTokenPayload),\n/* harmony export */   isTokenExpired: () => (/* binding */ isTokenExpired),\n/* harmony export */   secureStorage: () => (/* binding */ secureStorage),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePassword: () => (/* binding */ validatePassword),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n// Types d'authentification pour WALI Livraison\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"CLIENT\"] = \"CLIENT\";\n    UserRole[\"DRIVER\"] = \"DRIVER\";\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n})(UserRole || (UserRole = {}));\n// Configuration JWT\nconst JWT_CONFIG = {\n    ACCESS_TOKEN_EXPIRY: 15 * 60 * 1000,\n    REFRESH_TOKEN_EXPIRY: 7 * 24 * 60 * 60 * 1000,\n    STORAGE_KEYS: {\n        ACCESS_TOKEN: \"wali_access_token\",\n        REFRESH_TOKEN: \"wali_refresh_token\",\n        USER: \"wali_user\"\n    }\n};\n// Utilitaires de validation\nconst validateEmail = (email)=>{\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n};\nconst validatePhone = (phone)=>{\n    // Format ivoirien : +225 XX XX XX XX XX ou 0X XX XX XX XX\n    const phoneRegex = /^(\\+225|0)[0-9]{10}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n};\nconst validatePassword = (password)=>{\n    const errors = [];\n    if (password.length < 8) {\n        errors.push(\"Le mot de passe doit contenir au moins 8 caract\\xe8res\");\n    }\n    if (!/[A-Z]/.test(password)) {\n        errors.push(\"Le mot de passe doit contenir au moins une majuscule\");\n    }\n    if (!/[a-z]/.test(password)) {\n        errors.push(\"Le mot de passe doit contenir au moins une minuscule\");\n    }\n    if (!/[0-9]/.test(password)) {\n        errors.push(\"Le mot de passe doit contenir au moins un chiffre\");\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n};\n// Formatage du numéro de téléphone ivoirien\nconst formatIvorianPhone = (phone)=>{\n    const cleaned = phone.replace(/\\D/g, \"\");\n    if (cleaned.startsWith(\"225\")) {\n        return `+${cleaned}`;\n    }\n    if (cleaned.startsWith(\"0\")) {\n        return `+225${cleaned.substring(1)}`;\n    }\n    if (cleaned.length === 10) {\n        return `+225${cleaned}`;\n    }\n    return phone;\n};\n// Gestion du stockage sécurisé\nconst secureStorage = {\n    setItem: (key, value)=>{\n        if (false) {}\n    },\n    getItem: (key)=>{\n        if (false) {}\n        return null;\n    },\n    removeItem: (key)=>{\n        if (false) {}\n    },\n    clear: ()=>{\n        if (false) {}\n    }\n};\n// Vérification de l'expiration du token\nconst isTokenExpired = (token)=>{\n    try {\n        const payload = JSON.parse(atob(token.split(\".\")[1]));\n        return Date.now() >= payload.exp * 1000;\n    } catch  {\n        return true;\n    }\n};\n// Extraction des informations du token\nconst getTokenPayload = (token)=>{\n    try {\n        return JSON.parse(atob(token.split(\".\")[1]));\n    } catch  {\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/driver-verification.ts":
/*!****************************************!*\
  !*** ./src/lib/driver-verification.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOCUMENT_REQUIREMENTS: () => (/* binding */ DOCUMENT_REQUIREMENTS),\n/* harmony export */   DocumentStatus: () => (/* binding */ DocumentStatus),\n/* harmony export */   DriverStatus: () => (/* binding */ DriverStatus),\n/* harmony export */   INSURANCE_COMPANIES: () => (/* binding */ INSURANCE_COMPANIES),\n/* harmony export */   InsuranceCoverage: () => (/* binding */ InsuranceCoverage),\n/* harmony export */   LicenseCategory: () => (/* binding */ LicenseCategory),\n/* harmony export */   VEHICLE_BRANDS: () => (/* binding */ VEHICLE_BRANDS),\n/* harmony export */   VehicleType: () => (/* binding */ VehicleType),\n/* harmony export */   calculateDriverStatus: () => (/* binding */ calculateDriverStatus),\n/* harmony export */   getStatusMessage: () => (/* binding */ getStatusMessage),\n/* harmony export */   getVehicleTypeLabel: () => (/* binding */ getVehicleTypeLabel),\n/* harmony export */   requiresInsurance: () => (/* binding */ requiresInsurance),\n/* harmony export */   requiresLicense: () => (/* binding */ requiresLicense),\n/* harmony export */   validateIvorianLicenseNumber: () => (/* binding */ validateIvorianLicenseNumber),\n/* harmony export */   validateIvorianPlateNumber: () => (/* binding */ validateIvorianPlateNumber)\n/* harmony export */ });\n// Types pour l'inscription et vérification des livreurs - Côte d'Ivoire\nvar VehicleType;\n(function(VehicleType) {\n    VehicleType[\"MOTO\"] = \"MOTO\";\n    VehicleType[\"VOITURE\"] = \"VOITURE\";\n    VehicleType[\"SCOOTER\"] = \"SCOOTER\";\n    VehicleType[\"VELO\"] = \"VELO\";\n    VehicleType[\"TRICYCLE\"] = \"TRICYCLE\";\n    VehicleType[\"CAMIONNETTE\"] = \"CAMIONNETTE\";\n})(VehicleType || (VehicleType = {}));\nvar DriverStatus;\n(function(DriverStatus) {\n    DriverStatus[\"PENDING_VERIFICATION\"] = \"PENDING_VERIFICATION\";\n    DriverStatus[\"VERIFIED\"] = \"VERIFIED\";\n    DriverStatus[\"SUSPENDED\"] = \"SUSPENDED\";\n    DriverStatus[\"REJECTED\"] = \"REJECTED\";\n    DriverStatus[\"INCOMPLETE\"] = \"INCOMPLETE\";\n})(DriverStatus || (DriverStatus = {}));\nvar DocumentStatus;\n(function(DocumentStatus) {\n    DocumentStatus[\"NOT_UPLOADED\"] = \"NOT_UPLOADED\";\n    DocumentStatus[\"PENDING_REVIEW\"] = \"PENDING_REVIEW\";\n    DocumentStatus[\"APPROVED\"] = \"APPROVED\";\n    DocumentStatus[\"REJECTED\"] = \"REJECTED\";\n    DocumentStatus[\"EXPIRED\"] = \"EXPIRED\";\n})(DocumentStatus || (DocumentStatus = {}));\nvar LicenseCategory;\n(function(LicenseCategory) {\n    LicenseCategory[\"A\"] = \"A\";\n    LicenseCategory[\"A1\"] = \"A1\";\n    LicenseCategory[\"B\"] = \"B\";\n    LicenseCategory[\"C\"] = \"C\";\n    LicenseCategory[\"D\"] = \"D\";\n    LicenseCategory[\"E\"] = \"E\";\n})(LicenseCategory || (LicenseCategory = {}));\nvar InsuranceCoverage;\n(function(InsuranceCoverage) {\n    InsuranceCoverage[\"THIRD_PARTY\"] = \"THIRD_PARTY\";\n    InsuranceCoverage[\"COMPREHENSIVE\"] = \"COMPREHENSIVE\";\n    InsuranceCoverage[\"THIRD_PARTY_FIRE_THEFT\"] = \"THIRD_PARTY_FIRE_THEFT\";\n})(InsuranceCoverage || (InsuranceCoverage = {}));\n// Marques de véhicules populaires en Côte d'Ivoire\nconst VEHICLE_BRANDS = {\n    MOTO: [\n        \"Honda\",\n        \"Yamaha\",\n        \"Suzuki\",\n        \"Kawasaki\",\n        \"TVS\",\n        \"Bajaj\",\n        \"Hero\",\n        \"Haojue\",\n        \"Dayun\",\n        \"Lifan\",\n        \"Zongshen\"\n    ],\n    VOITURE: [\n        \"Toyota\",\n        \"Honda\",\n        \"Nissan\",\n        \"Hyundai\",\n        \"Kia\",\n        \"Volkswagen\",\n        \"Peugeot\",\n        \"Renault\",\n        \"Ford\",\n        \"Chevrolet\",\n        \"Mitsubishi\"\n    ],\n    SCOOTER: [\n        \"Honda\",\n        \"Yamaha\",\n        \"Peugeot\",\n        \"Piaggio\",\n        \"Sym\",\n        \"Kymco\"\n    ],\n    VELO: [\n        \"Giant\",\n        \"Trek\",\n        \"Specialized\",\n        \"Cannondale\",\n        \"Scott\",\n        \"Autre\"\n    ],\n    TRICYCLE: [\n        \"Bajaj\",\n        \"TVS\",\n        \"Piaggio\",\n        \"Mahindra\",\n        \"Autre\"\n    ],\n    CAMIONNETTE: [\n        \"Toyota\",\n        \"Nissan\",\n        \"Isuzu\",\n        \"Mitsubishi\",\n        \"Ford\",\n        \"Hyundai\"\n    ]\n};\n// Compagnies d'assurance en Côte d'Ivoire\nconst INSURANCE_COMPANIES = [\n    \"NSIA Assurances\",\n    \"Saham Assurance\",\n    \"Allianz C\\xf4te d'Ivoire\",\n    \"AXA Assurances C\\xf4te d'Ivoire\",\n    \"Sunu Assurances\",\n    \"Colina Assurance\",\n    \"Loyale Assurance\",\n    \"Atlantique Assurance\",\n    \"SONAR\",\n    \"Autre\"\n];\n// Validation des documents selon les réglementations ivoiriennes\nconst DOCUMENT_REQUIREMENTS = {\n    [\"MOTO\"]: {\n        drivingLicense: {\n            required: true,\n            categories: [\n                \"A\",\n                \"A1\"\n            ]\n        },\n        insurance: {\n            required: true\n        },\n        registration: {\n            required: true\n        }\n    },\n    [\"SCOOTER\"]: {\n        drivingLicense: {\n            required: true,\n            categories: [\n                \"A\",\n                \"A1\"\n            ]\n        },\n        insurance: {\n            required: true\n        },\n        registration: {\n            required: true\n        }\n    },\n    [\"VOITURE\"]: {\n        drivingLicense: {\n            required: true,\n            categories: [\n                \"B\"\n            ]\n        },\n        insurance: {\n            required: true\n        },\n        registration: {\n            required: true\n        }\n    },\n    [\"CAMIONNETTE\"]: {\n        drivingLicense: {\n            required: true,\n            categories: [\n                \"B\",\n                \"C\"\n            ]\n        },\n        insurance: {\n            required: true\n        },\n        registration: {\n            required: true\n        }\n    },\n    [\"TRICYCLE\"]: {\n        drivingLicense: {\n            required: true,\n            categories: [\n                \"A\",\n                \"B\"\n            ]\n        },\n        insurance: {\n            required: true\n        },\n        registration: {\n            required: true\n        }\n    },\n    [\"VELO\"]: {\n        drivingLicense: {\n            required: false,\n            categories: []\n        },\n        insurance: {\n            required: false\n        },\n        registration: {\n            required: false\n        }\n    }\n};\n// Validation du numéro d'immatriculation ivoirien\nconst validateIvorianPlateNumber = (plateNumber)=>{\n    // Format ivoirien : XXXX CI YY (4 chiffres + CI + 2 chiffres)\n    // Exemple : 1234 CI 01, 5678 CI 15\n    const plateRegex = /^\\d{4}\\s*CI\\s*\\d{2}$/i;\n    return plateRegex.test(plateNumber.trim());\n};\n// Validation du numéro de permis ivoirien\nconst validateIvorianLicenseNumber = (licenseNumber)=>{\n    // Format approximatif (à adapter selon le format officiel)\n    // Généralement 8-12 caractères alphanumériques\n    const licenseRegex = /^[A-Z0-9]{8,12}$/i;\n    return licenseRegex.test(licenseNumber.trim());\n};\n// Vérification si un véhicule nécessite un permis\nconst requiresLicense = (vehicleType)=>{\n    return DOCUMENT_REQUIREMENTS[vehicleType].drivingLicense.required;\n};\n// Vérification si un véhicule nécessite une assurance\nconst requiresInsurance = (vehicleType)=>{\n    return DOCUMENT_REQUIREMENTS[vehicleType].insurance.required;\n};\n// Calcul du statut global du profil livreur\nconst calculateDriverStatus = (profile)=>{\n    const vehicleType = profile.vehicle.type;\n    const requirements = DOCUMENT_REQUIREMENTS[vehicleType];\n    // Vérifier si les documents obligatoires sont présents et approuvés\n    if (requirements.drivingLicense.required) {\n        if (!profile.drivingLicense || profile.drivingLicense.status !== \"APPROVED\") {\n            return \"PENDING_VERIFICATION\";\n        }\n    }\n    if (requirements.insurance.required) {\n        if (!profile.insurance || profile.insurance.status !== \"APPROVED\") {\n            return \"PENDING_VERIFICATION\";\n        }\n    }\n    if (requirements.registration.required) {\n        if (!profile.registration || profile.registration.status !== \"APPROVED\") {\n            return \"PENDING_VERIFICATION\";\n        }\n    }\n    // Vérifier les dates d'expiration\n    const now = new Date();\n    if (profile.drivingLicense && new Date(profile.drivingLicense.expiryDate) < now) {\n        return \"SUSPENDED\";\n    }\n    if (profile.insurance && new Date(profile.insurance.expiryDate) < now) {\n        return \"SUSPENDED\";\n    }\n    return \"VERIFIED\";\n};\n// Messages de statut en français\nconst getStatusMessage = (status)=>{\n    switch(status){\n        case \"PENDING_VERIFICATION\":\n            return \"En attente de v\\xe9rification des documents\";\n        case \"VERIFIED\":\n            return \"Compte v\\xe9rifi\\xe9 - Vous pouvez accepter des livraisons\";\n        case \"SUSPENDED\":\n            return \"Compte suspendu - Documents expir\\xe9s ou non conformes\";\n        case \"REJECTED\":\n            return \"Inscription rejet\\xe9e - Contactez le support\";\n        case \"INCOMPLETE\":\n            return \"Inscription incompl\\xe8te - Veuillez compl\\xe9ter votre profil\";\n        default:\n            return \"Statut inconnu\";\n    }\n};\n// Messages pour les types de véhicules\nconst getVehicleTypeLabel = (type)=>{\n    switch(type){\n        case \"MOTO\":\n            return \"Motocyclette\";\n        case \"VOITURE\":\n            return \"Voiture particuli\\xe8re\";\n        case \"SCOOTER\":\n            return \"Scooter\";\n        case \"VELO\":\n            return \"V\\xe9lo\";\n        case \"TRICYCLE\":\n            return \"Tricycle\";\n        case \"CAMIONNETTE\":\n            return \"Camionnette\";\n        default:\n            return \"V\\xe9hicule\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/driver-verification.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/../../node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/../../node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi9zcmMvbGliL3V0aWxzLnRzPzdjMWMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/auth.service.ts":
/*!**************************************!*\
  !*** ./src/services/auth.service.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authService: () => (/* binding */ authService)\n/* harmony export */ });\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(ssr)/./src/lib/auth.ts\");\n\nclass AuthService {\n    // Connexion\n    async login(credentials) {\n        try {\n            // Tentative d'appel API réel\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/auth/login`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(credentials)\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    this.storeTokens(data.tokens);\n                    this.storeUser(data.user);\n                    return data;\n                }\n            }\n        } catch (error) {\n            console.log(\"Backend indisponible, utilisation du mode hors ligne\");\n        }\n        // Mode hors ligne - simulation\n        return this.simulateLogin(credentials);\n    }\n    // Inscription\n    async register(data) {\n        try {\n            // Tentative d'appel API réel\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/auth/register`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify(data)\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    this.storeTokens(result.tokens);\n                    this.storeUser(result.user);\n                    return result;\n                }\n            }\n        } catch (error) {\n            console.log(\"Backend indisponible, utilisation du mode hors ligne\");\n        }\n        // Mode hors ligne - simulation\n        return this.simulateRegister(data);\n    }\n    // Déconnexion\n    async logout() {\n        try {\n            const refreshToken = this.getRefreshToken();\n            if (refreshToken && this.isOnline) {\n                await fetch(`${this.baseUrl}/auth/logout`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.getAccessToken()}`\n                    },\n                    body: JSON.stringify({\n                        refreshToken\n                    })\n                });\n            }\n        } catch (error) {\n            console.log(\"Erreur lors de la d\\xe9connexion:\", error);\n        } finally{\n            this.clearTokens();\n        }\n    }\n    // Rafraîchissement du token\n    async refreshToken() {\n        const refreshToken = this.getRefreshToken();\n        if (!refreshToken) return null;\n        try {\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/auth/refresh`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        refreshToken\n                    })\n                });\n                if (response.ok) {\n                    const tokens = await response.json();\n                    this.storeTokens(tokens);\n                    return tokens;\n                }\n            }\n        } catch (error) {\n            console.log(\"Erreur de rafra\\xeechissement:\", error);\n        }\n        // Mode hors ligne - générer de nouveaux tokens simulés\n        return this.simulateRefreshToken();\n    }\n    // Vérification de l'authentification\n    isAuthenticated() {\n        const token = this.getAccessToken();\n        return token !== null && !(0,_lib_auth__WEBPACK_IMPORTED_MODULE_0__.isTokenExpired)(token);\n    }\n    // Obtenir l'utilisateur actuel\n    getCurrentUser() {\n        const userStr = _lib_auth__WEBPACK_IMPORTED_MODULE_0__.secureStorage.getItem(_lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.STORAGE_KEYS.USER);\n        if (userStr) {\n            try {\n                return JSON.parse(userStr);\n            } catch  {\n                return null;\n            }\n        }\n        return null;\n    }\n    // Mise à jour du profil\n    async updateProfile(updates) {\n        try {\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/auth/profile`, {\n                    method: \"PATCH\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.getAccessToken()}`\n                    },\n                    body: JSON.stringify(updates)\n                });\n                if (response.ok) {\n                    const user = await response.json();\n                    this.storeUser(user);\n                    return user;\n                }\n            }\n        } catch (error) {\n            console.log(\"Erreur de mise \\xe0 jour:\", error);\n        }\n        // Mode hors ligne - simulation\n        const currentUser = this.getCurrentUser();\n        if (currentUser) {\n            const updatedUser = {\n                ...currentUser,\n                ...updates\n            };\n            this.storeUser(updatedUser);\n            return updatedUser;\n        }\n        throw new Error(\"Utilisateur non trouv\\xe9\");\n    }\n    // Méthodes privées\n    storeTokens(tokens) {\n        _lib_auth__WEBPACK_IMPORTED_MODULE_0__.secureStorage.setItem(_lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.STORAGE_KEYS.ACCESS_TOKEN, tokens.accessToken);\n        _lib_auth__WEBPACK_IMPORTED_MODULE_0__.secureStorage.setItem(_lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.STORAGE_KEYS.REFRESH_TOKEN, tokens.refreshToken);\n    }\n    storeUser(user) {\n        _lib_auth__WEBPACK_IMPORTED_MODULE_0__.secureStorage.setItem(_lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.STORAGE_KEYS.USER, JSON.stringify(user));\n    }\n    getAccessToken() {\n        return _lib_auth__WEBPACK_IMPORTED_MODULE_0__.secureStorage.getItem(_lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.STORAGE_KEYS.ACCESS_TOKEN);\n    }\n    getRefreshToken() {\n        return _lib_auth__WEBPACK_IMPORTED_MODULE_0__.secureStorage.getItem(_lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.STORAGE_KEYS.REFRESH_TOKEN);\n    }\n    clearTokens() {\n        _lib_auth__WEBPACK_IMPORTED_MODULE_0__.secureStorage.clear();\n    }\n    // Simulations pour le mode hors ligne\n    async simulateLogin(credentials) {\n        await new Promise((resolve)=>setTimeout(resolve, 1000)); // Simulation délai réseau\n        const user = this.mockUsers.find((u)=>u.email === credentials.email || u.phone === credentials.phone);\n        if (!user || credentials.password !== \"password123\") {\n            throw new Error(\"Identifiants incorrects\");\n        }\n        const tokens = this.generateMockTokens(user);\n        return {\n            user,\n            tokens,\n            message: \"Connexion r\\xe9ussie (mode hors ligne)\"\n        };\n    }\n    async simulateRegister(data) {\n        await new Promise((resolve)=>setTimeout(resolve, 1500)); // Simulation délai réseau\n        // Vérifier si l'utilisateur existe déjà\n        const existingUser = this.mockUsers.find((u)=>u.email === data.email || u.phone === data.phone);\n        if (existingUser) {\n            throw new Error(\"Un compte existe d\\xe9j\\xe0 avec cet email ou num\\xe9ro de t\\xe9l\\xe9phone\");\n        }\n        const newUser = {\n            id: Date.now().toString(),\n            email: data.email,\n            phone: data.phone,\n            firstName: data.firstName,\n            lastName: data.lastName,\n            role: data.role,\n            isVerified: false,\n            addresses: [],\n            preferences: {\n                language: \"fr\",\n                notifications: {\n                    email: true,\n                    sms: true,\n                    push: true\n                },\n                paymentMethod: \"cash\"\n            },\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        this.mockUsers.push(newUser);\n        const tokens = this.generateMockTokens(newUser);\n        return {\n            user: newUser,\n            tokens,\n            message: \"Inscription r\\xe9ussie (mode hors ligne)\"\n        };\n    }\n    simulateRefreshToken() {\n        const user = this.getCurrentUser();\n        if (!user) throw new Error(\"Utilisateur non trouv\\xe9\");\n        return this.generateMockTokens(user);\n    }\n    generateMockTokens(user) {\n        const now = Date.now();\n        const accessToken = btoa(JSON.stringify({\n            sub: user.id,\n            email: user.email,\n            role: user.role,\n            exp: Math.floor((now + _lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.ACCESS_TOKEN_EXPIRY) / 1000)\n        }));\n        const refreshToken = btoa(JSON.stringify({\n            sub: user.id,\n            type: \"refresh\",\n            exp: Math.floor((now + _lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.REFRESH_TOKEN_EXPIRY) / 1000)\n        }));\n        return {\n            accessToken: `mock.${accessToken}.signature`,\n            refreshToken: `mock.${refreshToken}.signature`,\n            expiresAt: now + _lib_auth__WEBPACK_IMPORTED_MODULE_0__.JWT_CONFIG.ACCESS_TOKEN_EXPIRY\n        };\n    }\n    constructor(){\n        this.baseUrl = \"http://localhost:3001/api/v1\" || 0;\n        this.isOnline = true;\n        // Simulation de données pour le mode hors ligne\n        this.mockUsers = [\n            {\n                id: \"1\",\n                email: \"<EMAIL>\",\n                phone: \"+22507123456\",\n                firstName: \"Kouame\",\n                lastName: \"Yao\",\n                role: \"CLIENT\",\n                isVerified: true,\n                addresses: [\n                    {\n                        id: \"1\",\n                        label: \"Domicile\",\n                        street: \"Rue des Jardins\",\n                        city: \"Abidjan\",\n                        district: \"Plateau\",\n                        coordinates: {\n                            lat: 5.3364,\n                            lng: -4.0267\n                        },\n                        isDefault: true\n                    }\n                ],\n                preferences: {\n                    language: \"fr\",\n                    notifications: {\n                        email: true,\n                        sms: true,\n                        push: true\n                    },\n                    paymentMethod: \"orange_money\"\n                },\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            },\n            {\n                id: \"2\",\n                email: \"<EMAIL>\",\n                phone: \"+22501987654\",\n                firstName: \"Mamadou\",\n                lastName: \"Traore\",\n                role: \"DRIVER\",\n                isVerified: true,\n                addresses: [],\n                preferences: {\n                    language: \"fr\",\n                    notifications: {\n                        email: true,\n                        sms: true,\n                        push: true\n                    },\n                    paymentMethod: \"mtn_money\"\n                },\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            }\n        ];\n    }\n}\nconst authService = new AuthService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/auth.service.ts\n");

/***/ }),

/***/ "(ssr)/./src/services/driver.service.ts":
/*!****************************************!*\
  !*** ./src/services/driver.service.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   driverService: () => (/* binding */ driverService)\n/* harmony export */ });\n/* harmony import */ var _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/driver-verification */ \"(ssr)/./src/lib/driver-verification.ts\");\n\nclass DriverService {\n    // Créer un profil livreur\n    async createDriverProfile(userId, vehicleInfo) {\n        try {\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/drivers/profile`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.getAccessToken()}`\n                    },\n                    body: JSON.stringify({\n                        userId,\n                        vehicle: vehicleInfo\n                    })\n                });\n                if (response.ok) {\n                    return await response.json();\n                }\n            }\n        } catch (error) {\n            console.log(\"Backend indisponible, utilisation du mode hors ligne\");\n        }\n        // Mode hors ligne - simulation\n        return this.simulateCreateDriverProfile(userId, vehicleInfo);\n    }\n    // Mettre à jour le profil livreur\n    async updateDriverProfile(profileId, updates) {\n        try {\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/drivers/profile/${profileId}`, {\n                    method: \"PATCH\",\n                    headers: {\n                        \"Content-Type\": \"application/json\",\n                        \"Authorization\": `Bearer ${this.getAccessToken()}`\n                    },\n                    body: JSON.stringify(updates)\n                });\n                if (response.ok) {\n                    return await response.json();\n                }\n            }\n        } catch (error) {\n            console.log(\"Backend indisponible, utilisation du mode hors ligne\");\n        }\n        // Mode hors ligne - simulation\n        return this.simulateUpdateDriverProfile(profileId, updates);\n    }\n    // Obtenir le profil livreur par ID utilisateur\n    async getDriverProfileByUserId(userId) {\n        try {\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/drivers/profile/user/${userId}`, {\n                    headers: {\n                        \"Authorization\": `Bearer ${this.getAccessToken()}`\n                    }\n                });\n                if (response.ok) {\n                    return await response.json();\n                }\n            }\n        } catch (error) {\n            console.log(\"Backend indisponible, utilisation du mode hors ligne\");\n        }\n        // Mode hors ligne - simulation\n        return this.simulateGetDriverProfileByUserId(userId);\n    }\n    // Uploader un document\n    async uploadDocument(profileId, documentType, file) {\n        try {\n            if (this.isOnline) {\n                const formData = new FormData();\n                formData.append(\"document\", file);\n                formData.append(\"type\", documentType);\n                const response = await fetch(`${this.baseUrl}/drivers/profile/${profileId}/documents`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Authorization\": `Bearer ${this.getAccessToken()}`\n                    },\n                    body: formData\n                });\n                if (response.ok) {\n                    const result = await response.json();\n                    return result.url;\n                }\n            }\n        } catch (error) {\n            console.log(\"Backend indisponible, utilisation du mode hors ligne\");\n        }\n        // Mode hors ligne - simulation\n        return this.simulateUploadDocument(file);\n    }\n    // Mettre à jour les informations de permis\n    async updateDrivingLicense(profileId, license) {\n        const updates = {\n            drivingLicense: license\n        };\n        return this.updateDriverProfile(profileId, updates);\n    }\n    // Mettre à jour les informations d'assurance\n    async updateInsurance(profileId, insurance) {\n        const updates = {\n            insurance\n        };\n        return this.updateDriverProfile(profileId, updates);\n    }\n    // Mettre à jour les informations de carte grise\n    async updateRegistration(profileId, registration) {\n        const updates = {\n            registration\n        };\n        return this.updateDriverProfile(profileId, updates);\n    }\n    // Obtenir les statistiques du livreur\n    async getDriverStats(profileId) {\n        try {\n            if (this.isOnline) {\n                const response = await fetch(`${this.baseUrl}/drivers/profile/${profileId}/stats`, {\n                    headers: {\n                        \"Authorization\": `Bearer ${this.getAccessToken()}`\n                    }\n                });\n                if (response.ok) {\n                    return await response.json();\n                }\n            }\n        } catch (error) {\n            console.log(\"Backend indisponible, utilisation du mode hors ligne\");\n        }\n        // Mode hors ligne - simulation\n        return this.simulateGetDriverStats();\n    }\n    // Méthodes privées pour les simulations\n    async simulateCreateDriverProfile(userId, vehicleInfo) {\n        await new Promise((resolve)=>setTimeout(resolve, 1000));\n        const newProfile = {\n            id: Date.now().toString(),\n            userId,\n            status: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DriverStatus.INCOMPLETE,\n            vehicle: vehicleInfo,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        this.mockDriverProfiles.push(newProfile);\n        return newProfile;\n    }\n    async simulateUpdateDriverProfile(profileId, updates) {\n        await new Promise((resolve)=>setTimeout(resolve, 800));\n        const profileIndex = this.mockDriverProfiles.findIndex((p)=>p.id === profileId);\n        if (profileIndex === -1) {\n            throw new Error(\"Profil livreur non trouv\\xe9\");\n        }\n        const updatedProfile = {\n            ...this.mockDriverProfiles[profileIndex],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        // Recalculer le statut\n        updatedProfile.status = (0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.calculateDriverStatus)(updatedProfile);\n        this.mockDriverProfiles[profileIndex] = updatedProfile;\n        return updatedProfile;\n    }\n    async simulateGetDriverProfileByUserId(userId) {\n        await new Promise((resolve)=>setTimeout(resolve, 500));\n        return this.mockDriverProfiles.find((p)=>p.userId === userId) || null;\n    }\n    async simulateUploadDocument(file) {\n        await new Promise((resolve)=>setTimeout(resolve, 2000));\n        // Simuler une URL de document uploadé\n        const timestamp = Date.now();\n        const extension = file.name.split(\".\").pop();\n        return `https://storage.wali.ci/documents/${timestamp}.${extension}`;\n    }\n    async simulateGetDriverStats() {\n        await new Promise((resolve)=>setTimeout(resolve, 600));\n        return {\n            totalDeliveries: 156,\n            completedDeliveries: 148,\n            cancelledDeliveries: 8,\n            averageRating: 4.7,\n            totalEarnings: 125000,\n            thisMonthEarnings: 28000,\n            documentsStatus: {\n                license: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.APPROVED,\n                insurance: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.APPROVED,\n                registration: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.APPROVED\n            }\n        };\n    }\n    getAccessToken() {\n        if (false) {}\n        return null;\n    }\n    constructor(){\n        this.baseUrl = \"http://localhost:3001/api/v1\" || 0;\n        this.isOnline = true;\n        // Données simulées pour le développement\n        this.mockDriverProfiles = [\n            {\n                id: \"1\",\n                userId: \"2\",\n                status: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DriverStatus.VERIFIED,\n                vehicle: {\n                    type: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.VehicleType.MOTO,\n                    brand: \"Honda\",\n                    model: \"CB 125\",\n                    year: 2020,\n                    color: \"Rouge\",\n                    registrationNumber: \"1234 CI 01\",\n                    engineNumber: \"CB125E123456\",\n                    chassisNumber: \"JH2CB1234567890\"\n                },\n                drivingLicense: {\n                    number: \"CI123456789\",\n                    category: \"A\",\n                    issueDate: \"2020-01-15\",\n                    expiryDate: \"2025-01-15\",\n                    issuingAuthority: \"Pr\\xe9fecture d'Abidjan\",\n                    status: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.APPROVED\n                },\n                insurance: {\n                    policyNumber: \"NSIA2024001234\",\n                    insurer: \"NSIA Assurances\",\n                    startDate: \"2024-01-01\",\n                    expiryDate: \"2024-12-31\",\n                    coverage: \"THIRD_PARTY\",\n                    status: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.APPROVED\n                },\n                registration: {\n                    certificateNumber: \"CG123456789\",\n                    registrationDate: \"2020-02-01\",\n                    ownerName: \"Mamadou Traore\",\n                    isOwner: true,\n                    status: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_0__.DocumentStatus.APPROVED\n                },\n                experienceYears: 3,\n                emergencyContact: {\n                    name: \"Fatou Traore\",\n                    phone: \"+22507654321\",\n                    relationship: \"\\xc9pouse\"\n                },\n                verifiedAt: \"2024-01-15T10:00:00Z\",\n                verifiedBy: \"admin1\",\n                createdAt: \"2024-01-10T08:00:00Z\",\n                updatedAt: \"2024-01-15T10:00:00Z\"\n            }\n        ];\n    }\n}\nconst driverService = new DriverService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/driver.service.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8ce88c7bc62a\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/ZWI0OSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhjZTg4YzdiYzYyYVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/driver/register/vehicle/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/driver/register/vehicle/page.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Mientior livraison app\apps\client-web\src\app\driver\register\vehicle\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(rsc)/../../node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_maps_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/maps/GoogleMapsProvider */ \"(rsc)/./src/components/maps/GoogleMapsProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"WALI Livraison - Plateforme de Livraison en C\\xf4te d'Ivoire\",\n    description: \"Plateforme de livraison multi-services en C\\xf4te d'Ivoire avec paiement mobile int\\xe9gr\\xe9\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        className: \"h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"h-full bg-background text-foreground antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_maps_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_3__.GoogleMapsProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n                        position: \"top-right\",\n                        richColors: true,\n                        closeButton: true,\n                        duration: 4000\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBc0I7QUFDVTtBQUN5QztBQUVsRSxNQUFNRSxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0MsV0FBVTtrQkFDeEIsNEVBQUNDO1lBQUtELFdBQVU7c0JBQ2QsNEVBQUNSLG1GQUFrQkE7O29CQUNoQks7a0NBQ0QsOERBQUNOLDJDQUFPQTt3QkFDTlcsVUFBUzt3QkFDVEMsVUFBVTt3QkFDVkMsV0FBVzt3QkFDWEMsVUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU10QiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAnc29ubmVyJ1xuaW1wb3J0IHsgR29vZ2xlTWFwc1Byb3ZpZGVyIH0gZnJvbSAnQC9jb21wb25lbnRzL21hcHMvR29vZ2xlTWFwc1Byb3ZpZGVyJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnV0FMSSBMaXZyYWlzb24gLSBQbGF0ZWZvcm1lIGRlIExpdnJhaXNvbiBlbiBDw7R0ZSBkXFwnSXZvaXJlJyxcbiAgZGVzY3JpcHRpb246ICdQbGF0ZWZvcm1lIGRlIGxpdnJhaXNvbiBtdWx0aS1zZXJ2aWNlcyBlbiBDw7R0ZSBkXFwnSXZvaXJlIGF2ZWMgcGFpZW1lbnQgbW9iaWxlIGludMOpZ3LDqScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJmclwiIGNsYXNzTmFtZT1cImgtZnVsbFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiaC1mdWxsIGJnLWJhY2tncm91bmQgdGV4dC1mb3JlZ3JvdW5kIGFudGlhbGlhc2VkXCI+XG4gICAgICAgIDxHb29nbGVNYXBzUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDxUb2FzdGVyXG4gICAgICAgICAgICBwb3NpdGlvbj1cInRvcC1yaWdodFwiXG4gICAgICAgICAgICByaWNoQ29sb3JzXG4gICAgICAgICAgICBjbG9zZUJ1dHRvblxuICAgICAgICAgICAgZHVyYXRpb249ezQwMDB9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9Hb29nbGVNYXBzUHJvdmlkZXI+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsiVG9hc3RlciIsIkdvb2dsZU1hcHNQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiY2xhc3NOYW1lIiwiYm9keSIsInBvc2l0aW9uIiwicmljaENvbG9ycyIsImNsb3NlQnV0dG9uIiwiZHVyYXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/maps/GoogleMapsProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/maps/GoogleMapsProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GoogleMapsProvider: () => (/* binding */ e1),
/* harmony export */   useGoogleMaps: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Mientior livraison app\apps\client-web\src\components\maps\GoogleMapsProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Mientior livraison app\apps\client-web\src\components\maps\GoogleMapsProvider.tsx#useGoogleMaps`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Mientior livraison app\apps\client-web\src\components\maps\GoogleMapsProvider.tsx#GoogleMapsProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/sonner","vendor-chunks/@swc","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/clsx","vendor-chunks/@floating-ui","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/react-style-singleton","vendor-chunks/get-nonce","vendor-chunks/detect-node-es"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdriver%2Fregister%2Fvehicle%2Fpage&page=%2Fdriver%2Fregister%2Fvehicle%2Fpage&appPaths=%2Fdriver%2Fregister%2Fvehicle%2Fpage&pagePath=private-next-app-dir%2Fdriver%2Fregister%2Fvehicle%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();