export interface Order {
    id: string;
    orderNumber: string;
    clientId: string;
    driverId?: string;
    type: OrderType;
    status: OrderStatus;
    pickupAddress: string;
    pickupLatitude: number;
    pickupLongitude: number;
    deliveryAddress: string;
    deliveryLatitude: number;
    deliveryLongitude: number;
    basePrice: number;
    deliveryFee: number;
    totalAmount: number;
    estimatedDuration?: number;
    scheduledAt?: Date;
    pickedUpAt?: Date;
    deliveredAt?: Date;
    notes?: string;
    proofOfDelivery?: string;
    createdAt: Date;
    updatedAt: Date;
    items?: OrderItem[];
    client?: {
        id: string;
        firstName: string;
        lastName: string;
        phone: string;
    };
    driver?: {
        id: string;
        firstName: string;
        lastName: string;
        phone: string;
    };
    trackingEvents?: TrackingEvent[];
}
export declare enum OrderType {
    DELIVERY = "DELIVERY",
    FOOD = "FOOD",
    SHOPPING = "SHOPPING"
}
export declare enum OrderStatus {
    PENDING = "PENDING",
    CONFIRMED = "CONFIRMED",
    ASSIGNED = "ASSIGNED",
    PICKED_UP = "PICKED_UP",
    IN_TRANSIT = "IN_TRANSIT",
    DELIVERED = "DELIVERED",
    CANCELLED = "CANCELLED",
    FAILED = "FAILED"
}
export interface OrderItem {
    id: string;
    orderId: string;
    productId?: string;
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
}
export interface TrackingEvent {
    id: string;
    orderId: string;
    status: OrderStatus;
    latitude?: number;
    longitude?: number;
    description?: string;
    createdAt: Date;
}
export interface CreateOrderRequest {
    type: OrderType;
    pickupAddress: string;
    pickupLatitude: number;
    pickupLongitude: number;
    deliveryAddress: string;
    deliveryLatitude: number;
    deliveryLongitude: number;
    notes?: string;
    scheduledAt?: Date;
    items: CreateOrderItemRequest[];
}
export interface CreateOrderItemRequest {
    name: string;
    description?: string;
    quantity: number;
    unitPrice: number;
}
export interface UpdateOrderRequest {
    status?: OrderStatus;
    driverId?: string;
    notes?: string;
    scheduledAt?: Date;
    proofOfDelivery?: string;
}
export interface PriceCalculationRequest {
    type: OrderType;
    pickupLatitude: number;
    pickupLongitude: number;
    deliveryLatitude: number;
    deliveryLongitude: number;
    items?: CreateOrderItemRequest[];
}
export interface PriceCalculationResult {
    distance: number;
    estimatedDuration: number;
    basePrice: number;
    deliveryFee: number;
    totalAmount: number;
    breakdown: {
        distanceFee: number;
        timeFee: number;
        typeFee: number;
        itemsFee: number;
    };
}
export declare const PRICING_CONFIG: {
    readonly BASE_PRICES: {
        readonly DELIVERY: 1000;
        readonly FOOD: 1500;
        readonly SHOPPING: 2000;
    };
    readonly PRICE_PER_KM: 200;
    readonly PRICE_PER_MINUTE: 50;
    readonly FREE_DISTANCE: 2;
    readonly NIGHT_SURCHARGE: 0.5;
    readonly WEEKEND_SURCHARGE: 0.3;
    readonly RAIN_SURCHARGE: 0.2;
    readonly MIN_ORDER_AMOUNT: 500;
    readonly MAX_DISTANCE: 50;
};
export declare const ORDER_STATUS_MESSAGES: {
    readonly PENDING: "Commande en attente de confirmation";
    readonly CONFIRMED: "Commande confirmée, recherche d'un livreur";
    readonly ASSIGNED: "Livreur assigné, préparation en cours";
    readonly PICKED_UP: "Commande récupérée, en route vers vous";
    readonly IN_TRANSIT: "Commande en transit";
    readonly DELIVERED: "Commande livrée avec succès";
    readonly CANCELLED: "Commande annulée";
    readonly FAILED: "Échec de la livraison";
};
export declare const ORDER_TYPE_LABELS: {
    readonly DELIVERY: "Livraison Express";
    readonly FOOD: "Livraison de Repas";
    readonly SHOPPING: "Courses et Achats";
};
export interface TrackingEvent {
    id: string;
    orderId: string;
    status: OrderStatus;
    latitude?: number;
    longitude?: number;
    description?: string;
    createdAt: Date;
}
