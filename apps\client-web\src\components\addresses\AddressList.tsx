'use client';

import { useState } from 'react';
import { Address } from '@wali/shared';
import { useAddresses } from '@/hooks/useAddresses';

interface AddressListProps {
  addresses: Address[];
  onEdit?: (address: Address) => void;
  onRefresh?: () => void;
}

export default function AddressList({ addresses, onEdit, onRefresh }: AddressListProps) {
  const { deleteAddress, setDefaultAddress, isLoading } = useAddresses();
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [settingDefaultId, setSettingDefaultId] = useState<string | null>(null);

  const handleDelete = async (address: Address) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer l'adresse "${address.label || address.street}" ?`)) {
      return;
    }

    setDeletingId(address.id);
    try {
      await deleteAddress(address.id);
      onRefresh?.();
    } catch (error) {
      console.error('Erreur suppression adresse:', error);
    } finally {
      setDeletingId(null);
    }
  };

  const handleSetDefault = async (address: Address) => {
    if (address.isDefault) return;

    setSettingDefaultId(address.id);
    try {
      await setDefaultAddress(address.id);
      onRefresh?.();
    } catch (error) {
      console.error('Erreur définition adresse par défaut:', error);
    } finally {
      setSettingDefaultId(null);
    }
  };

  const formatAddress = (address: Address): string => {
    const parts = [address.street];
    if (address.district) parts.push(address.district);
    parts.push(address.city);
    return parts.join(', ');
  };

  if (addresses.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📍</div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Aucune adresse</h3>
        <p className="text-gray-600 mb-6">
          Vous n'avez pas encore ajouté d'adresse de livraison
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {addresses.map((address) => (
        <div
          key={address.id}
          className={`bg-white rounded-lg shadow-md p-6 border-l-4 ${
            address.isDefault ? 'border-orange-500' : 'border-gray-200'
          }`}
        >
          <div className="flex justify-between items-start">
            <div className="flex-1">
              {/* En-tête avec libellé et badge par défaut */}
              <div className="flex items-center space-x-3 mb-2">
                {address.label && (
                  <h3 className="text-lg font-semibold text-gray-900">
                    {address.label}
                  </h3>
                )}
                {address.isDefault && (
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    Par défaut
                  </span>
                )}
              </div>

              {/* Adresse complète */}
              <p className="text-gray-700 mb-2">
                {formatAddress(address)}
              </p>

              {/* Point de repère */}
              {address.landmark && (
                <p className="text-sm text-gray-500 mb-2">
                  📍 {address.landmark}
                </p>
              )}

              {/* Coordonnées GPS */}
              <p className="text-xs text-gray-400">
                GPS: {address.latitude.toFixed(6)}, {address.longitude.toFixed(6)}
              </p>
            </div>

            {/* Actions */}
            <div className="flex flex-col space-y-2 ml-4">
              {/* Bouton Modifier */}
              <button
                onClick={() => onEdit?.(address)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium"
              >
                Modifier
              </button>

              {/* Bouton Définir par défaut */}
              {!address.isDefault && (
                <button
                  onClick={() => handleSetDefault(address)}
                  disabled={settingDefaultId === address.id}
                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50"
                >
                  {settingDefaultId === address.id ? 'Définition...' : 'Par défaut'}
                </button>
              )}

              {/* Bouton Supprimer */}
              <button
                onClick={() => handleDelete(address)}
                disabled={deletingId === address.id}
                className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50"
              >
                {deletingId === address.id ? 'Suppression...' : 'Supprimer'}
              </button>
            </div>
          </div>

          {/* Carte miniature (placeholder) */}
          <div className="mt-4 h-32 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-2xl mb-1">🗺️</div>
              <p className="text-sm">Carte interactive</p>
              <p className="text-xs">
                {address.city} - {address.district || 'Centre-ville'}
              </p>
            </div>
          </div>

          {/* Actions rapides */}
          <div className="mt-4 flex space-x-4 text-sm">
            <button
              onClick={() => {
                const url = `https://www.google.com/maps?q=${address.latitude},${address.longitude}`;
                window.open(url, '_blank');
              }}
              className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
            >
              <span>🌍</span>
              <span>Voir sur Google Maps</span>
            </button>

            <button
              onClick={() => {
                const addressText = formatAddress(address);
                navigator.clipboard.writeText(addressText);
                alert('Adresse copiée dans le presse-papiers');
              }}
              className="text-gray-600 hover:text-gray-800 flex items-center space-x-1"
            >
              <span>📋</span>
              <span>Copier l'adresse</span>
            </button>
          </div>
        </div>
      ))}
    </div>
  );
}
