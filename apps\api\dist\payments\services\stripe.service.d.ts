import { ConfigService } from '@nestjs/config';
import Strip<PERSON> from 'stripe';
export declare class StripeService {
    private configService;
    private readonly logger;
    private stripe;
    constructor(configService: ConfigService);
    createPaymentIntent(amount: number, currency?: string, metadata?: Record<string, string>): Promise<Stripe.PaymentIntent>;
    confirmPaymentIntent(paymentIntentId: string, paymentMethodId: string): Promise<Stripe.PaymentIntent>;
    retrievePaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent>;
    createRefund(paymentIntentId: string, amount?: number, reason?: string): Promise<Stripe.Refund>;
    constructWebhookEvent(payload: string | Buffer, signature: string): Promise<Stripe.Event>;
    createCustomer(email?: string, phone?: string, name?: string, metadata?: Record<string, string>): Promise<Stripe.Customer>;
    attachPaymentMethod(paymentMethodId: string, customerId: string): Promise<Stripe.PaymentMethod>;
    listCustomerPaymentMethods(customerId: string, type?: string): Promise<Stripe.PaymentMethod[]>;
    createSetupIntent(customerId: string, metadata?: Record<string, string>): Promise<Stripe.SetupIntent>;
}
