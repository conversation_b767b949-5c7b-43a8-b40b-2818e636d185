import './globals.css'
import { Toaster } from 'sonner'
import Script from 'next/script'

export const metadata = {
  title: 'WALI Livraison - Plateforme de Livraison en Côte d\'Ivoire',
  description: 'Plateforme de livraison multi-services en Côte d\'Ivoire avec paiement mobile intégré',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr" className="h-full">
      <body className="h-full bg-background text-foreground antialiased">
        {children}
        <Toaster
          position="top-right"
          richColors
          closeButton
          duration={4000}
        />

        {/* Google Maps API Script */}
        <Script
          src={`https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places&language=fr&region=CI`}
          strategy="beforeInteractive"
          onLoad={() => {
            console.log('Google Maps API loaded successfully');
          }}
          onError={(e) => {
            console.error('Failed to load Google Maps API:', e);
          }}
        />
      </body>
    </html>
  )
}
