"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AddressesService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AddressesService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const geocoding_service_1 = require("./geocoding.service");
const shared_1 = require("../../../../packages/shared/src");
let AddressesService = AddressesService_1 = class AddressesService {
    constructor(prisma, geocodingService) {
        this.prisma = prisma;
        this.geocodingService = geocodingService;
        this.logger = new common_1.Logger(AddressesService_1.name);
    }
    async getUserAddresses(userId) {
        return this.prisma.address.findMany({
            where: { userId },
            orderBy: [
                { isDefault: 'desc' },
                { createdAt: 'desc' },
            ],
        });
    }
    async getAddressById(addressId, userId) {
        const address = await this.prisma.address.findFirst({
            where: {
                id: addressId,
                userId,
            },
        });
        if (!address) {
            throw new common_1.NotFoundException('Adresse non trouvée');
        }
        return address;
    }
    async createAddress(userId, createAddressDto) {
        const { latitude, longitude, city, district, isDefault, ...addressData } = createAddressDto;
        if (!this.geocodingService.isWithinIvoryCoast(latitude, longitude)) {
            throw new common_1.BadRequestException('Les coordonnées doivent être situées en Côte d\'Ivoire');
        }
        if (city === 'Abidjan' && district && !shared_1.ABIDJAN_DISTRICTS.includes(district)) {
            throw new common_1.BadRequestException('Le quartier spécifié n\'est pas valide pour Abidjan');
        }
        const existingAddresses = await this.prisma.address.count({
            where: { userId },
        });
        const shouldBeDefault = isDefault || existingAddresses === 0;
        if (shouldBeDefault) {
            await this.prisma.address.updateMany({
                where: {
                    userId,
                    isDefault: true,
                },
                data: { isDefault: false },
            });
        }
        const address = await this.prisma.address.create({
            data: {
                ...addressData,
                city,
                district,
                latitude,
                longitude,
                isDefault: shouldBeDefault,
                userId,
            },
        });
        this.logger.log(`Nouvelle adresse créée pour l'utilisateur ${userId}: ${address.id}`);
        return address;
    }
    async updateAddress(addressId, userId, updateAddressDto) {
        const existingAddress = await this.getAddressById(addressId, userId);
        const { latitude, longitude, city, district, isDefault, ...addressData } = updateAddressDto;
        if (latitude !== undefined && longitude !== undefined) {
            if (!this.geocodingService.isWithinIvoryCoast(latitude, longitude)) {
                throw new common_1.BadRequestException('Les coordonnées doivent être situées en Côte d\'Ivoire');
            }
        }
        if (city === 'Abidjan' && district && !shared_1.ABIDJAN_DISTRICTS.includes(district)) {
            throw new common_1.BadRequestException('Le quartier spécifié n\'est pas valide pour Abidjan');
        }
        if (isDefault === true) {
            await this.prisma.address.updateMany({
                where: {
                    userId,
                    isDefault: true,
                    id: { not: addressId },
                },
                data: { isDefault: false },
            });
        }
        const updatedAddress = await this.prisma.address.update({
            where: { id: addressId },
            data: {
                ...addressData,
                ...(city !== undefined && { city }),
                ...(district !== undefined && { district }),
                ...(latitude !== undefined && { latitude }),
                ...(longitude !== undefined && { longitude }),
                ...(isDefault !== undefined && { isDefault }),
            },
        });
        this.logger.log(`Adresse mise à jour: ${addressId}`);
        return updatedAddress;
    }
    async deleteAddress(addressId, userId) {
        const existingAddress = await this.getAddressById(addressId, userId);
        await this.prisma.address.delete({
            where: { id: addressId },
        });
        if (existingAddress.isDefault) {
            const firstAddress = await this.prisma.address.findFirst({
                where: { userId },
                orderBy: { createdAt: 'asc' },
            });
            if (firstAddress) {
                await this.prisma.address.update({
                    where: { id: firstAddress.id },
                    data: { isDefault: true },
                });
            }
        }
        this.logger.log(`Adresse supprimée: ${addressId}`);
        return {
            message: 'Adresse supprimée avec succès',
        };
    }
    async setDefaultAddress(addressId, userId) {
        await this.getAddressById(addressId, userId);
        await this.prisma.address.updateMany({
            where: {
                userId,
                isDefault: true,
            },
            data: { isDefault: false },
        });
        const updatedAddress = await this.prisma.address.update({
            where: { id: addressId },
            data: { isDefault: true },
        });
        this.logger.log(`Adresse définie comme par défaut: ${addressId}`);
        return updatedAddress;
    }
    async searchNearbyAddresses(userId, latitude, longitude, radiusKm = 5) {
        const userAddresses = await this.getUserAddresses(userId);
        const nearbyAddresses = userAddresses
            .map(address => ({
            ...address,
            distance: this.geocodingService.calculateDistance(latitude, longitude, address.latitude, address.longitude),
        }))
            .filter(address => address.distance <= radiusKm)
            .sort((a, b) => a.distance - b.distance);
        return nearbyAddresses;
    }
    async geocodeAddress(address) {
        const result = await this.geocodingService.geocodeAddress(address);
        if (!result) {
            throw new common_1.BadRequestException('Impossible de géolocaliser cette adresse');
        }
        return result;
    }
    async reverseGeocode(latitude, longitude) {
        if (!this.geocodingService.isWithinIvoryCoast(latitude, longitude)) {
            throw new common_1.BadRequestException('Les coordonnées doivent être situées en Côte d\'Ivoire');
        }
        const result = await this.geocodingService.reverseGeocode(latitude, longitude);
        if (!result) {
            throw new common_1.BadRequestException('Impossible de trouver une adresse pour ces coordonnées');
        }
        return result;
    }
};
exports.AddressesService = AddressesService;
exports.AddressesService = AddressesService = AddressesService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        geocoding_service_1.GeocodingService])
], AddressesService);
//# sourceMappingURL=addresses.service.js.map