import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { PaymentsService } from './payments.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GetUser } from '../auth/decorators/get-user.decorator';
import {
  ProcessPaymentDto,
  CreateStripeIntentDto,
  MobileMoneyPaymentDto,
  RefundPaymentDto,
} from './dto';

@ApiTags('Payments')
@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @UseGuards(JwtAuthGuard)
  @Post('process')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Traitement d\'un paiement' })
  @ApiResponse({ status: 200, description: 'Paiement traité avec succès' })
  @ApiResponse({ status: 400, description: 'Données de paiement invalides' })
  @ApiResponse({ status: 402, description: 'Paiement échoué' })
  async processPayment(
    @Body() processPaymentDto: ProcessPaymentDto,
    @GetUser() user: any,
  ) {
    return this.paymentsService.processPayment(processPaymentDto, user.id);
  }

  @Get('methods')
  @ApiOperation({ summary: 'Méthodes de paiement disponibles' })
  @ApiResponse({ status: 200, description: 'Liste des méthodes récupérée' })
  async getPaymentMethods() {
    return this.paymentsService.getAvailablePaymentMethods();
  }

  @UseGuards(JwtAuthGuard)
  @Post('stripe/intent')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Création d\'un PaymentIntent Stripe' })
  @ApiResponse({ status: 200, description: 'PaymentIntent créé avec succès' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  async createStripeIntent(
    @Body() createIntentDto: CreateStripeIntentDto,
    @GetUser() user: any,
  ) {
    return this.paymentsService.createStripePaymentIntent(createIntentDto, user.id);
  }

  @UseGuards(JwtAuthGuard)
  @Post('mobile-money')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Paiement via Mobile Money' })
  @ApiResponse({ status: 200, description: 'Paiement Mobile Money initié' })
  @ApiResponse({ status: 400, description: 'Données invalides' })
  async mobileMoneyPayment(
    @Body() mobileMoneyDto: MobileMoneyPaymentDto,
    @GetUser() user: any,
  ) {
    return this.paymentsService.processMobileMoneyPayment(mobileMoneyDto, user.id);
  }

  @Get(':id/status')
  @ApiOperation({ summary: 'Statut d\'un paiement' })
  @ApiResponse({ status: 200, description: 'Statut récupéré avec succès' })
  @ApiResponse({ status: 404, description: 'Paiement non trouvé' })
  async getPaymentStatus(@Param('id') id: string) {
    return this.paymentsService.getPaymentStatus(id);
  }

  @UseGuards(JwtAuthGuard)
  @Post(':id/refund')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Remboursement d\'un paiement' })
  @ApiResponse({ status: 200, description: 'Remboursement traité avec succès' })
  @ApiResponse({ status: 400, description: 'Remboursement impossible' })
  @ApiResponse({ status: 404, description: 'Paiement non trouvé' })
  async refundPayment(
    @Param('id') id: string,
    @Body() refundDto: RefundPaymentDto,
    @GetUser() user: any,
  ) {
    return this.paymentsService.refundPayment(id, refundDto, user.id);
  }

  // Webhooks pour les providers de paiement
  @Post('webhooks/stripe')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Webhook Stripe' })
  async stripeWebhook(@Body() payload: any) {
    return this.paymentsService.handleStripeWebhook(payload);
  }

  @Post('webhooks/orange-money')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Webhook Orange Money' })
  async orangeMoneyWebhook(@Body() payload: any) {
    return this.paymentsService.handleOrangeMoneyWebhook(payload);
  }

  @Post('webhooks/mtn-money')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Webhook MTN Mobile Money' })
  async mtnMoneyWebhook(@Body() payload: any) {
    return this.paymentsService.handleMtnMoneyWebhook(payload);
  }

  @Post('webhooks/wave')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Webhook Wave' })
  async waveWebhook(@Body() payload: any) {
    return this.paymentsService.handleWaveWebhook(payload);
  }
}
