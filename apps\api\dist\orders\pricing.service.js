"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var PricingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PricingService = void 0;
const common_1 = require("@nestjs/common");
const shared_1 = require("@wali/shared");
let PricingService = PricingService_1 = class PricingService {
    constructor() {
        this.logger = new common_1.Logger(PricingService_1.name);
    }
    async calculatePrice(request) {
        const { type, pickupLatitude, pickupLongitude, deliveryLatitude, deliveryLongitude, items = [] } = request;
        const distance = this.calculateDistance(pickupLatitude, pickupLongitude, deliveryLatitude, deliveryLongitude);
        const estimatedDuration = Math.max(15, Math.round((distance / 25) * 60));
        const basePrice = shared_1.PRICING_CONFIG.BASE_PRICES[type];
        const chargeableDistance = Math.max(0, distance - shared_1.PRICING_CONFIG.FREE_DISTANCE);
        const distanceFee = chargeableDistance * shared_1.PRICING_CONFIG.PRICE_PER_KM;
        const timeFee = estimatedDuration > 30 ? (estimatedDuration - 30) * shared_1.PRICING_CONFIG.PRICE_PER_MINUTE : 0;
        const typeFee = this.calculateTypeFee(type, items);
        const itemsFee = this.calculateItemsFee(type, items);
        let subtotal = basePrice + distanceFee + timeFee + typeFee + itemsFee;
        const surcharges = this.calculateSurcharges(subtotal);
        const deliveryFee = distanceFee + timeFee + typeFee + itemsFee + surcharges;
        const totalAmount = Math.max(basePrice + deliveryFee, shared_1.PRICING_CONFIG.MIN_ORDER_AMOUNT);
        this.logger.log(`Prix calculé pour ${type}: ${totalAmount} FCFA (distance: ${distance}km, durée: ${estimatedDuration}min)`);
        return {
            distance: Math.round(distance * 100) / 100,
            estimatedDuration,
            basePrice,
            deliveryFee: Math.round(deliveryFee),
            totalAmount: Math.round(totalAmount),
            breakdown: {
                distanceFee: Math.round(distanceFee),
                timeFee: Math.round(timeFee),
                typeFee: Math.round(typeFee),
                itemsFee: Math.round(itemsFee),
            },
        };
    }
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371;
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    calculateTypeFee(type, items) {
        switch (type) {
            case shared_1.OrderType.FOOD:
                return 300;
            case shared_1.OrderType.SHOPPING:
                return items.length > 5 ? 500 : 200;
            case shared_1.OrderType.DELIVERY:
            default:
                return 0;
        }
    }
    calculateItemsFee(type, items) {
        if (type !== shared_1.OrderType.SHOPPING) {
            return 0;
        }
        const itemCount = items.reduce((sum, item) => sum + (item.quantity || 1), 0);
        if (itemCount > 20)
            return 1000;
        if (itemCount > 10)
            return 500;
        if (itemCount > 5)
            return 200;
        return 0;
    }
    calculateSurcharges(baseAmount) {
        const now = new Date();
        const hour = now.getHours();
        const isWeekend = now.getDay() === 0 || now.getDay() === 6;
        let surchargeRate = 0;
        if (hour >= 22 || hour < 6) {
            surchargeRate += shared_1.PRICING_CONFIG.NIGHT_SURCHARGE;
        }
        if (isWeekend) {
            surchargeRate += shared_1.PRICING_CONFIG.WEEKEND_SURCHARGE;
        }
        return Math.round(baseAmount * surchargeRate);
    }
    validateOrder(request) {
        const errors = [];
        const distance = this.calculateDistance(request.pickupLatitude, request.pickupLongitude, request.deliveryLatitude, request.deliveryLongitude);
        if (distance > shared_1.PRICING_CONFIG.MAX_DISTANCE) {
            errors.push(`Distance trop importante: ${distance.toFixed(1)}km (max: ${shared_1.PRICING_CONFIG.MAX_DISTANCE}km)`);
        }
        if (!this.isWithinIvoryCoast(request.pickupLatitude, request.pickupLongitude)) {
            errors.push('L\'adresse de récupération doit être en Côte d\'Ivoire');
        }
        if (!this.isWithinIvoryCoast(request.deliveryLatitude, request.deliveryLongitude)) {
            errors.push('L\'adresse de livraison doit être en Côte d\'Ivoire');
        }
        if (request.type === shared_1.OrderType.SHOPPING && (!request.items || request.items.length === 0)) {
            errors.push('Les commandes de courses doivent contenir au moins un article');
        }
        return {
            valid: errors.length === 0,
            errors,
        };
    }
    isWithinIvoryCoast(latitude, longitude) {
        const bounds = {
            north: 10.74,
            south: 4.34,
            east: -2.49,
            west: -8.60
        };
        return latitude >= bounds.south &&
            latitude <= bounds.north &&
            longitude >= bounds.west &&
            longitude <= bounds.east;
    }
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    estimateDeliveryTime(pickupLat, pickupLon, deliveryLat, deliveryLon) {
        const distance = this.calculateDistance(pickupLat, pickupLon, deliveryLat, deliveryLon);
        let avgSpeed = 25;
        if (this.isInPlateau(pickupLat, pickupLon) || this.isInPlateau(deliveryLat, deliveryLon)) {
            avgSpeed = 15;
        }
        if (distance > 15) {
            avgSpeed = 35;
        }
        const travelTime = (distance / avgSpeed) * 60;
        const stopTime = 10;
        return Math.round(travelTime + stopTime);
    }
    isInPlateau(latitude, longitude) {
        return latitude >= 5.31 && latitude <= 5.33 &&
            longitude >= -4.03 && longitude <= -4.00;
    }
};
exports.PricingService = PricingService;
exports.PricingService = PricingService = PricingService_1 = __decorate([
    (0, common_1.Injectable)()
], PricingService);
//# sourceMappingURL=pricing.service.js.map