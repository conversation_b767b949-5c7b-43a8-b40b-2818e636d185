"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const orders_service_1 = require("./orders.service");
const dto_1 = require("./dto");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let OrdersController = class OrdersController {
    constructor(ordersService) {
        this.ordersService = ordersService;
    }
    async calculatePrice(priceCalculationDto) {
        return this.ordersService.calculatePrice(priceCalculationDto);
    }
    async createOrder(user, createOrderDto) {
        return this.ordersService.createOrder(user.id, createOrderDto);
    }
    async getUserOrders(user, page, limit) {
        return this.ordersService.getUserOrders(user.id, page ? Number(page) : 1, limit ? Number(limit) : 10);
    }
    async getOrderById(id, user) {
        return this.ordersService.getOrderById(id, user.id);
    }
    async updateOrder(id, user, updateOrderDto) {
        return this.ordersService.updateOrder(id, user.id, updateOrderDto);
    }
    async cancelOrder(id, user, reason) {
        return this.ordersService.cancelOrder(id, user.id, reason);
    }
    async getOrderTracking(id, user) {
        const order = await this.ordersService.getOrderById(id, user.id);
        return {
            order,
            currentLocation: null,
            estimatedArrival: null,
            trackingEvents: order.trackingEvents || [],
        };
    }
    async rateOrder(id, user, rating, comment) {
        return {
            message: 'Note enregistrée avec succès',
            rating,
            comment,
        };
    }
};
exports.OrdersController = OrdersController;
__decorate([
    (0, common_1.Post)('calculate-price'),
    (0, swagger_1.ApiOperation)({
        summary: 'Calculer le prix d\'une commande',
        description: 'Calcule le prix d\'une commande basé sur la distance réelle (Google Maps) et le type de livraison'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Prix calculé avec succès.',
        schema: {
            type: 'object',
            properties: {
                distance: { type: 'number', example: 5.2, description: 'Distance en kilomètres' },
                estimatedDuration: { type: 'number', example: 25, description: 'Durée estimée en minutes' },
                basePrice: { type: 'number', example: 1500, description: 'Prix de base en FCFA' },
                deliveryFee: { type: 'number', example: 1200, description: 'Frais de livraison en FCFA' },
                totalAmount: { type: 'number', example: 2700, description: 'Montant total en FCFA' },
                breakdown: {
                    type: 'object',
                    properties: {
                        distanceFee: { type: 'number', example: 640 },
                        timeFee: { type: 'number', example: 0 },
                        typeFee: { type: 'number', example: 300 },
                        itemsFee: { type: 'number', example: 260 }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Données invalides ou distance trop importante'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.PriceCalculationDto]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "calculatePrice", null);
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Créer une nouvelle commande',
        description: 'Crée une nouvelle commande de livraison avec calcul automatique du prix'
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Commande créée avec succès.',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'cuid123456789' },
                orderNumber: { type: 'string', example: 'WL24011500001' },
                status: { type: 'string', example: 'PENDING' },
                type: { type: 'string', example: 'FOOD' },
                totalAmount: { type: 'number', example: 2700 },
                estimatedDuration: { type: 'number', example: 25 },
                createdAt: { type: 'string', format: 'date-time' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Données invalides'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.CreateOrderDto]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "createOrder", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Liste des commandes de l\'utilisateur',
        description: 'Récupère toutes les commandes de l\'utilisateur connecté avec pagination'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        description: 'Numéro de page',
        example: 1,
        required: false,
        type: 'number'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: 'Nombre d\'éléments par page',
        example: 10,
        required: false,
        type: 'number'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Liste des commandes récupérée avec succès.',
        schema: {
            type: 'object',
            properties: {
                orders: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            orderNumber: { type: 'string' },
                            status: { type: 'string' },
                            type: { type: 'string' },
                            totalAmount: { type: 'number' },
                            createdAt: { type: 'string', format: 'date-time' }
                        }
                    }
                },
                total: { type: 'number', example: 25 }
            }
        }
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Number, Number]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "getUserOrders", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Détails d\'une commande',
        description: 'Récupère les détails complets d\'une commande avec suivi'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de la commande',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Commande récupérée avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Commande non trouvée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "getOrderById", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Mettre à jour une commande',
        description: 'Modifie une commande existante (statut, notes, etc.)'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de la commande à modifier',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Commande mise à jour avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Commande non trouvée'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Commande ne peut plus être modifiée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, dto_1.UpdateOrderDto]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "updateOrder", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Annuler une commande',
        description: 'Annule une commande si elle n\'est pas encore en cours de livraison'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de la commande à annuler',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Commande annulée avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Commande non trouvée'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Commande ne peut pas être annulée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __param(2, (0, common_1.Body)('reason')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, String]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "cancelOrder", null);
__decorate([
    (0, common_1.Get)(':id/tracking'),
    (0, swagger_1.ApiOperation)({
        summary: 'Suivi d\'une commande',
        description: 'Récupère les informations de suivi en temps réel d\'une commande'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de la commande à suivre',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Informations de suivi récupérées avec succès.',
        schema: {
            type: 'object',
            properties: {
                order: { type: 'object' },
                currentLocation: {
                    type: 'object',
                    properties: {
                        latitude: { type: 'number' },
                        longitude: { type: 'number' },
                        timestamp: { type: 'string', format: 'date-time' }
                    }
                },
                estimatedArrival: { type: 'string', format: 'date-time' },
                trackingEvents: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            status: { type: 'string' },
                            description: { type: 'string' },
                            createdAt: { type: 'string', format: 'date-time' }
                        }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Commande non trouvée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "getOrderTracking", null);
__decorate([
    (0, common_1.Post)(':id/rate'),
    (0, swagger_1.ApiOperation)({
        summary: 'Noter une commande',
        description: 'Permet au client de noter la qualité de la livraison'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de la commande à noter',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Note enregistrée avec succès.'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Commande non trouvée'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Commande ne peut pas être notée'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __param(2, (0, common_1.Body)('rating')),
    __param(3, (0, common_1.Body)('comment')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Number, String]),
    __metadata("design:returntype", Promise)
], OrdersController.prototype, "rateOrder", null);
exports.OrdersController = OrdersController = __decorate([
    (0, swagger_1.ApiTags)('Commandes'),
    (0, common_1.Controller)('orders'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [orders_service_1.OrdersService])
], OrdersController);
//# sourceMappingURL=orders.controller.js.map