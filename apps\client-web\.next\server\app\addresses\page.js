/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/addresses/page";
exports.ids = ["app/addresses/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faddresses%2Fpage&page=%2Faddresses%2Fpage&appPaths=%2Faddresses%2Fpage&pagePath=private-next-app-dir%2Faddresses%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faddresses%2Fpage&page=%2Faddresses%2Fpage&appPaths=%2Faddresses%2Fpage&pagePath=private-next-app-dir%2Faddresses%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?b784\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'addresses',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/addresses/page.tsx */ \"(rsc)/./src/app/addresses/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/addresses/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/addresses/page\",\n        pathname: \"/addresses\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faddresses%2Fpage&page=%2Faddresses%2Fpage&appPaths=%2Faddresses%2Fpage&pagePath=private-next-app-dir%2Faddresses%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Caddresses%5Cpage.tsx&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Caddresses%5Cpage.tsx&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/addresses/page.tsx */ \"(ssr)/./src/app/addresses/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPUMlM0ElNUNVc2VycyU1Q0VsaXNlZSU1Q0RvY3VtZW50cyU1Q2F1Z21lbnQtcHJvamVjdHMlNUNNaWVudGlvciUyMGxpdnJhaXNvbiUyMGFwcCU1Q2FwcHMlNUNjbGllbnQtd2ViJTVDc3JjJTVDYXBwJTVDYWRkcmVzc2VzJTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8/MzMzYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXEVsaXNlZVxcXFxEb2N1bWVudHNcXFxcYXVnbWVudC1wcm9qZWN0c1xcXFxNaWVudGlvciBsaXZyYWlzb24gYXBwXFxcXGFwcHNcXFxcY2xpZW50LXdlYlxcXFxzcmNcXFxcYXBwXFxcXGFkZHJlc3Nlc1xcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Caddresses%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/../../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**********************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/addresses/page.tsx":
/*!************************************!*\
  !*** ./src/app/addresses/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddressesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* harmony import */ var _hooks_useAddresses__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAddresses */ \"(ssr)/./src/hooks/useAddresses.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_addresses_AddressForm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/addresses/AddressForm */ \"(ssr)/./src/components/addresses/AddressForm.tsx\");\n/* harmony import */ var _components_addresses_AddressList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/addresses/AddressList */ \"(ssr)/./src/components/addresses/AddressList.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AddressesPage() {\n    const { isAuthenticated, isLoading: authLoading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { getAddresses, isLoading: addressesLoading } = (0,_hooks_useAddresses__WEBPACK_IMPORTED_MODULE_3__.useAddresses)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const [addresses, setAddresses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingAddress, setEditingAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!authLoading && !isAuthenticated) {\n            router.push(\"/auth\");\n        }\n    }, [\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isAuthenticated) {\n            loadAddresses();\n        }\n    }, [\n        isAuthenticated\n    ]);\n    const loadAddresses = async ()=>{\n        try {\n            const userAddresses = await getAddresses();\n            setAddresses(userAddresses);\n        } catch (error) {\n            console.error(\"Erreur lors du chargement des adresses:\", error);\n        }\n    };\n    const handleAddAddress = ()=>{\n        setEditingAddress(null);\n        setShowForm(true);\n    };\n    const handleEditAddress = (address)=>{\n        setEditingAddress(address);\n        setShowForm(true);\n    };\n    const handleFormSuccess = (address)=>{\n        setShowForm(false);\n        setEditingAddress(null);\n        loadAddresses(); // Recharger la liste\n    };\n    const handleFormCancel = ()=>{\n        setShowForm(false);\n        setEditingAddress(null);\n    };\n    if (authLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-gray-600\",\n                        children: \"Chargement...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return null; // Redirection en cours\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center py-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>router.push(\"/dashboard\"),\n                                        className: \"text-gray-600 hover:text-gray-900\",\n                                        children: \"← Retour au dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: \"Mes Adresses\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleAddAddress,\n                                className: \"bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-md text-sm font-medium\",\n                                children: \"+ Nouvelle adresse\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"px-4 py-6 sm:px-0\",\n                    children: [\n                        showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_addresses_AddressForm__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                address: editingAddress || undefined,\n                                onSuccess: handleFormSuccess,\n                                onCancel: handleFormCancel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        !showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"Mes adresses de livraison\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                addresses.length,\n                                                \" adresse\",\n                                                addresses.length !== 1 ? \"s\" : \"\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this),\n                                addressesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center py-12\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_addresses_AddressList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    addresses: addresses,\n                                    onEdit: handleEditAddress,\n                                    onRefresh: loadAddresses\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 17\n                                }, this),\n                                !addressesLoading && addresses.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddAddress,\n                                        className: \"bg-orange-600 hover:bg-orange-700 text-white px-6 py-3 rounded-lg text-lg font-medium\",\n                                        children: \"Ajouter ma premi\\xe8re adresse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, this),\n                        !showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-8 bg-blue-50 rounded-lg p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-blue-900 mb-4\",\n                                    children: \"\\uD83D\\uDCA1 Conseils pour vos adresses\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"\\uD83D\\uDCCD Pr\\xe9cision de localisation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Utilisez les boutons de g\\xe9olocalisation pour obtenir des coordonn\\xe9es pr\\xe9cises et faciliter la livraison.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"\\uD83C\\uDFE0 Points de rep\\xe8re\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Ajoutez des points de rep\\xe8re (pharmacie, \\xe9cole, carrefour) pour aider nos livreurs \\xe0 vous trouver facilement.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"⭐ Adresse par d\\xe9faut\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"D\\xe9finissez votre adresse principale comme par d\\xe9faut pour acc\\xe9l\\xe9rer vos commandes futures.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"\\uD83D\\uDDFA️ Zones de livraison\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Nous livrons dans toute la C\\xf4te d'Ivoire. Les coordonn\\xe9es GPS doivent \\xeatre situ\\xe9es sur le territoire ivoirien.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\addresses\\\\page.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/addresses/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/addresses/AddressForm.tsx":
/*!**************************************************!*\
  !*** ./src/components/addresses/AddressForm.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddressForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAddresses__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAddresses */ \"(ssr)/./src/hooks/useAddresses.ts\");\n/* harmony import */ var _wali_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wali/shared */ \"(ssr)/../../packages/shared/src/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AddressForm({ address, onSuccess, onCancel }) {\n    const { createAddress, updateAddress, geocodeAddress, getCurrentLocation, isLoading, error, clearError } = (0,_hooks_useAddresses__WEBPACK_IMPORTED_MODULE_2__.useAddresses)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        street: \"\",\n        city: \"Abidjan\",\n        district: \"\",\n        landmark: \"\",\n        latitude: 5.3364,\n        longitude: -4.0267,\n        isDefault: false\n    });\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isGeolocating, setIsGeolocating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialiser le formulaire avec les données de l'adresse existante\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (address) {\n            setFormData({\n                label: address.label || \"\",\n                street: address.street,\n                city: address.city,\n                district: address.district || \"\",\n                landmark: address.landmark || \"\",\n                latitude: address.latitude,\n                longitude: address.longitude,\n                isDefault: address.isDefault\n            });\n        }\n    }, [\n        address\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        clearError();\n        setValidationErrors([]);\n    };\n    const validateForm = ()=>{\n        const errors = [];\n        if (!formData.street.trim() || formData.street.length < 5) {\n            errors.push(\"La rue doit contenir au moins 5 caract\\xe8res\");\n        }\n        if (!formData.city) {\n            errors.push(\"La ville est requise\");\n        }\n        if (formData.city === \"Abidjan\" && !formData.district) {\n            errors.push(\"Le quartier est obligatoire pour Abidjan\");\n        }\n        if (formData.latitude < -90 || formData.latitude > 90) {\n            errors.push(\"La latitude doit \\xeatre comprise entre -90 et 90\");\n        }\n        if (formData.longitude < -180 || formData.longitude > 180) {\n            errors.push(\"La longitude doit \\xeatre comprise entre -180 et 180\");\n        }\n        return errors;\n    };\n    const handleGeocodeAddress = async ()=>{\n        if (!formData.street.trim() || !formData.city) {\n            setValidationErrors([\n                \"Veuillez remplir la rue et la ville avant de g\\xe9olocaliser\"\n            ]);\n            return;\n        }\n        setIsGeolocating(true);\n        try {\n            const addressString = `${formData.street}, ${formData.district ? formData.district + \", \" : \"\"}${formData.city}`;\n            const result = await geocodeAddress(addressString);\n            setFormData((prev)=>({\n                    ...prev,\n                    latitude: result.latitude,\n                    longitude: result.longitude,\n                    city: result.city,\n                    district: result.district || prev.district\n                }));\n            setValidationErrors([]);\n        } catch (error) {\n            console.error(\"Erreur g\\xe9ocodage:\", error);\n        } finally{\n            setIsGeolocating(false);\n        }\n    };\n    const handleGetCurrentLocation = async ()=>{\n        setIsGeolocating(true);\n        try {\n            const location = await getCurrentLocation();\n            setFormData((prev)=>({\n                    ...prev,\n                    latitude: location.latitude,\n                    longitude: location.longitude\n                }));\n            setValidationErrors([]);\n        } catch (error) {\n            setValidationErrors([\n                error instanceof Error ? error.message : \"Erreur de g\\xe9olocalisation\"\n            ]);\n        } finally{\n            setIsGeolocating(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        clearError();\n        // Validation côté client\n        const errors = validateForm();\n        if (errors.length > 0) {\n            setValidationErrors(errors);\n            return;\n        }\n        try {\n            let result;\n            if (address) {\n                // Mise à jour\n                const updateData = {};\n                if (formData.label !== address.label) updateData.label = formData.label;\n                if (formData.street !== address.street) updateData.street = formData.street;\n                if (formData.city !== address.city) updateData.city = formData.city;\n                if (formData.district !== address.district) updateData.district = formData.district;\n                if (formData.landmark !== address.landmark) updateData.landmark = formData.landmark;\n                if (formData.latitude !== address.latitude) updateData.latitude = formData.latitude;\n                if (formData.longitude !== address.longitude) updateData.longitude = formData.longitude;\n                if (formData.isDefault !== address.isDefault) updateData.isDefault = formData.isDefault;\n                result = await updateAddress(address.id, updateData);\n            } else {\n                // Création\n                result = await createAddress(formData);\n            }\n            onSuccess?.(result);\n        } catch (error) {\n            console.error(\"Erreur sauvegarde adresse:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: address ? \"Modifier l'adresse\" : \"Nouvelle adresse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onCancel,\n                        className: \"text-gray-500 hover:text-gray-700\",\n                        children: \"✕\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            (error || validationErrors.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 21\n                    }, this),\n                    validationErrors.map((err, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: err\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                lineNumber: 191,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"label\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Libell\\xe9 (optionnel)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"label\",\n                                value: formData.label,\n                                onChange: (e)=>handleInputChange(\"label\", e.target.value),\n                                placeholder: \"Maison, Bureau, Chez maman...\",\n                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"street\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Rue et num\\xe9ro *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"street\",\n                                value: formData.street,\n                                onChange: (e)=>handleInputChange(\"street\", e.target.value),\n                                placeholder: \"Rue des Jardins, R\\xe9sidence Les Palmiers, Villa 12\",\n                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"city\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Ville *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"city\",\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange(\"city\", e.target.value),\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                        required: true,\n                                        children: _wali_shared__WEBPACK_IMPORTED_MODULE_3__.IVORY_COAST_CITIES.map((city)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: city,\n                                                children: city\n                                            }, city, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"district\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: [\n                                            \"Quartier \",\n                                            formData.city === \"Abidjan\" && \"*\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    formData.city === \"Abidjan\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"district\",\n                                        value: formData.district,\n                                        onChange: (e)=>handleInputChange(\"district\", e.target.value),\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                        required: formData.city === \"Abidjan\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"S\\xe9lectionner un quartier\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            _wali_shared__WEBPACK_IMPORTED_MODULE_3__.ABIDJAN_DISTRICTS.map((district)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: district,\n                                                    children: district\n                                                }, district, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"district\",\n                                        value: formData.district,\n                                        onChange: (e)=>handleInputChange(\"district\", e.target.value),\n                                        placeholder: \"Quartier ou zone\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"landmark\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Point de rep\\xe8re (optionnel)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"landmark\",\n                                value: formData.landmark,\n                                onChange: (e)=>handleInputChange(\"landmark\", e.target.value),\n                                placeholder: \"Pr\\xe8s de la pharmacie du carrefour, face \\xe0 l'\\xe9cole...\",\n                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Coordonn\\xe9es GPS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"latitude\",\n                                                className: \"block text-xs text-gray-500\",\n                                                children: \"Latitude\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"latitude\",\n                                                value: formData.latitude,\n                                                onChange: (e)=>handleInputChange(\"latitude\", parseFloat(e.target.value)),\n                                                step: \"0.000001\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"longitude\",\n                                                className: \"block text-xs text-gray-500\",\n                                                children: \"Longitude\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"longitude\",\n                                                value: formData.longitude,\n                                                onChange: (e)=>handleInputChange(\"longitude\", parseFloat(e.target.value)),\n                                                step: \"0.000001\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleGeocodeAddress,\n                                        disabled: isGeolocating,\n                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium disabled:opacity-50\",\n                                        children: isGeolocating ? \"G\\xe9olocalisation...\" : \"\\uD83D\\uDCCD G\\xe9olocaliser l'adresse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleGetCurrentLocation,\n                                        disabled: isGeolocating,\n                                        className: \"flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium disabled:opacity-50\",\n                                        children: isGeolocating ? \"Localisation...\" : \"\\uD83C\\uDFAF Ma position\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"isDefault\",\n                                checked: formData.isDefault,\n                                onChange: (e)=>handleInputChange(\"isDefault\", e.target.checked),\n                                className: \"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"isDefault\",\n                                className: \"ml-2 block text-sm text-gray-900\",\n                                children: \"D\\xe9finir comme adresse par d\\xe9faut\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 353,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3\",\n                        children: [\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium\",\n                                children: \"Annuler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading,\n                                className: \"bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isLoading ? \"Enregistrement...\" : address ? \"Modifier\" : \"Cr\\xe9er\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 377,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 367,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/addresses/AddressForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/addresses/AddressList.tsx":
/*!**************************************************!*\
  !*** ./src/components/addresses/AddressList.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddressList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAddresses__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAddresses */ \"(ssr)/./src/hooks/useAddresses.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AddressList({ addresses, onEdit, onRefresh }) {\n    const { deleteAddress, setDefaultAddress, isLoading } = (0,_hooks_useAddresses__WEBPACK_IMPORTED_MODULE_2__.useAddresses)();\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [settingDefaultId, setSettingDefaultId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleDelete = async (address)=>{\n        if (!confirm(`Êtes-vous sûr de vouloir supprimer l'adresse \"${address.label || address.street}\" ?`)) {\n            return;\n        }\n        setDeletingId(address.id);\n        try {\n            await deleteAddress(address.id);\n            onRefresh?.();\n        } catch (error) {\n            console.error(\"Erreur suppression adresse:\", error);\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const handleSetDefault = async (address)=>{\n        if (address.isDefault) return;\n        setSettingDefaultId(address.id);\n        try {\n            await setDefaultAddress(address.id);\n            onRefresh?.();\n        } catch (error) {\n            console.error(\"Erreur d\\xe9finition adresse par d\\xe9faut:\", error);\n        } finally{\n            setSettingDefaultId(null);\n        }\n    };\n    const formatAddress = (address)=>{\n        const parts = [\n            address.street\n        ];\n        if (address.district) parts.push(address.district);\n        parts.push(address.city);\n        return parts.join(\", \");\n    };\n    if (addresses.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-6xl mb-4\",\n                    children: \"\\uD83D\\uDCCD\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                    children: \"Aucune adresse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 mb-6\",\n                    children: \"Vous n'avez pas encore ajout\\xe9 d'adresse de livraison\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: addresses.map((address)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `bg-white rounded-lg shadow-md p-6 border-l-4 ${address.isDefault ? \"border-orange-500\" : \"border-gray-200\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3 mb-2\",\n                                        children: [\n                                            address.label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: address.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 19\n                                            }, this),\n                                            address.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800\",\n                                                children: \"Par d\\xe9faut\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-2\",\n                                        children: formatAddress(address)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this),\n                                    address.landmark && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mb-2\",\n                                        children: [\n                                            \"\\uD83D\\uDCCD \",\n                                            address.landmark\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-gray-400\",\n                                        children: [\n                                            \"GPS: \",\n                                            address.latitude.toFixed(6),\n                                            \", \",\n                                            address.longitude.toFixed(6)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col space-y-2 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onEdit?.(address),\n                                        className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium\",\n                                        children: \"Modifier\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    !address.isDefault && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleSetDefault(address),\n                                        disabled: settingDefaultId === address.id,\n                                        className: \"bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50\",\n                                        children: settingDefaultId === address.id ? \"D\\xe9finition...\" : \"Par d\\xe9faut\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDelete(address),\n                                        disabled: deletingId === address.id,\n                                        className: \"bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium disabled:opacity-50\",\n                                        children: deletingId === address.id ? \"Suppression...\" : \"Supprimer\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 h-32 bg-gray-100 rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl mb-1\",\n                                    children: \"\\uD83D\\uDDFA️\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: \"Carte interactive\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: [\n                                        address.city,\n                                        \" - \",\n                                        address.district || \"Centre-ville\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 flex space-x-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const url = `https://www.google.com/maps?q=${address.latitude},${address.longitude}`;\n                                    window.open(url, \"_blank\");\n                                },\n                                className: \"text-blue-600 hover:text-blue-800 flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83C\\uDF0D\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Voir sur Google Maps\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    const addressText = formatAddress(address);\n                                    navigator.clipboard.writeText(addressText);\n                                    alert(\"Adresse copi\\xe9e dans le presse-papiers\");\n                                },\n                                className: \"text-gray-600 hover:text-gray-800 flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"\\uD83D\\uDCCB\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Copier l'adresse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, address.id, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n                lineNumber: 70,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressList.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/addresses/AddressList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAddresses.ts":
/*!***********************************!*\
  !*** ./src/hooks/useAddresses.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAddresses: () => (/* binding */ useAddresses)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useAuth */ \"(ssr)/./src/hooks/useAuth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAddresses auto */ \n\nconst API_BASE_URL = \"http://localhost:3001/api/v1\" || 0;\nconst useAddresses = ()=>{\n    const { authenticatedFetch } = (0,_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isLoading: false,\n        error: null\n    });\n    // Récupérer toutes les adresses de l'utilisateur\n    const getAddresses = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses`);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de la r\\xe9cup\\xe9ration des adresses\");\n            }\n            const addresses = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return addresses;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Récupérer une adresse par ID\n    const getAddressById = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (addressId)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses/${addressId}`);\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Adresse non trouv\\xe9e\");\n            }\n            const address = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return address;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Créer une nouvelle adresse\n    const createAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses`, {\n                method: \"POST\",\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de la cr\\xe9ation de l'adresse\");\n            }\n            const address = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return address;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Mettre à jour une adresse\n    const updateAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (addressId, data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses/${addressId}`, {\n                method: \"PUT\",\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de la mise \\xe0 jour de l'adresse\");\n            }\n            const address = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return address;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Supprimer une adresse\n    const deleteAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (addressId)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses/${addressId}`, {\n                method: \"DELETE\"\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de la suppression de l'adresse\");\n            }\n            const result = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Définir une adresse comme par défaut\n    const setDefaultAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (addressId)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses/${addressId}/default`, {\n                method: \"PUT\"\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de la d\\xe9finition de l'adresse par d\\xe9faut\");\n            }\n            const address = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return address;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Géocoder une adresse\n    const geocodeAddress = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (address)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses/geocode`, {\n                method: \"POST\",\n                body: JSON.stringify({\n                    address\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Impossible de g\\xe9olocaliser cette adresse\");\n            }\n            const result = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Géocodage inverse\n    const reverseGeocode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (latitude, longitude)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await authenticatedFetch(`${API_BASE_URL}/addresses/reverse-geocode`, {\n                method: \"POST\",\n                body: JSON.stringify({\n                    latitude,\n                    longitude\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Impossible de trouver une adresse pour ces coordonn\\xe9es\");\n            }\n            const result = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        authenticatedFetch\n    ]);\n    // Obtenir la position actuelle de l'utilisateur\n    const getCurrentLocation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return new Promise((resolve, reject)=>{\n            if (!navigator.geolocation) {\n                reject(new Error(\"La g\\xe9olocalisation n'est pas support\\xe9e par ce navigateur\"));\n                return;\n            }\n            navigator.geolocation.getCurrentPosition((position)=>{\n                resolve({\n                    latitude: position.coords.latitude,\n                    longitude: position.coords.longitude\n                });\n            }, (error)=>{\n                let message = \"Erreur de g\\xe9olocalisation\";\n                switch(error.code){\n                    case error.PERMISSION_DENIED:\n                        message = \"Permission de g\\xe9olocalisation refus\\xe9e\";\n                        break;\n                    case error.POSITION_UNAVAILABLE:\n                        message = \"Position non disponible\";\n                        break;\n                    case error.TIMEOUT:\n                        message = \"D\\xe9lai de g\\xe9olocalisation d\\xe9pass\\xe9\";\n                        break;\n                }\n                reject(new Error(message));\n            }, {\n                enableHighAccuracy: true,\n                timeout: 10000,\n                maximumAge: 300000\n            });\n        });\n    }, []);\n    return {\n        // État\n        ...state,\n        // Actions\n        getAddresses,\n        getAddressById,\n        createAddress,\n        updateAddress,\n        deleteAddress,\n        setDefaultAddress,\n        geocodeAddress,\n        reverseGeocode,\n        getCurrentLocation,\n        // Utilitaires\n        clearError: ()=>setState((prev)=>({\n                    ...prev,\n                    error: null\n                }))\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAddresses.ts\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \nconst API_BASE_URL = \"http://localhost:3001/api/v1\" || 0;\nconst useAuth = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        user: null,\n        accessToken: null,\n        refreshToken: null,\n        isAuthenticated: false,\n        isLoading: true,\n        error: null\n    });\n    // Charger les tokens depuis le localStorage au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const loadTokens = ()=>{\n            try {\n                const accessToken = localStorage.getItem(\"wali_access_token\");\n                const refreshToken = localStorage.getItem(\"wali_refresh_token\");\n                const userStr = localStorage.getItem(\"wali_user\");\n                if (accessToken && refreshToken && userStr) {\n                    const user = JSON.parse(userStr);\n                    setState((prev)=>({\n                            ...prev,\n                            user,\n                            accessToken,\n                            refreshToken,\n                            isAuthenticated: true,\n                            isLoading: false\n                        }));\n                } else {\n                    setState((prev)=>({\n                            ...prev,\n                            isLoading: false\n                        }));\n                }\n            } catch (error) {\n                console.error(\"Erreur lors du chargement des tokens:\", error);\n                setState((prev)=>({\n                        ...prev,\n                        isLoading: false\n                    }));\n            }\n        };\n        loadTokens();\n    }, []);\n    // Sauvegarder les tokens dans le localStorage\n    const saveTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((authResponse)=>{\n        try {\n            localStorage.setItem(\"wali_access_token\", authResponse.accessToken);\n            localStorage.setItem(\"wali_refresh_token\", authResponse.refreshToken);\n            localStorage.setItem(\"wali_user\", JSON.stringify(authResponse.user));\n            setState((prev)=>({\n                    ...prev,\n                    user: authResponse.user,\n                    accessToken: authResponse.accessToken,\n                    refreshToken: authResponse.refreshToken,\n                    isAuthenticated: true,\n                    error: null\n                }));\n        } catch (error) {\n            console.error(\"Erreur lors de la sauvegarde des tokens:\", error);\n        }\n    }, []);\n    // Supprimer les tokens\n    const clearTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        localStorage.removeItem(\"wali_access_token\");\n        localStorage.removeItem(\"wali_refresh_token\");\n        localStorage.removeItem(\"wali_user\");\n        setState({\n            user: null,\n            accessToken: null,\n            refreshToken: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null\n        });\n    }, []);\n    // Inscription\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await fetch(`${API_BASE_URL}/auth/register`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de l'inscription\");\n            }\n            const result = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, []);\n    // Connexion\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await fetch(`${API_BASE_URL}/auth/login`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de la connexion\");\n            }\n            const result = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, []);\n    // Vérification OTP\n    const verifyOtp = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await fetch(`${API_BASE_URL}/auth/verify-otp`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Code de v\\xe9rification invalide\");\n            }\n            const authResponse = await response.json();\n            saveTokens(authResponse);\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        saveTokens\n    ]);\n    // Rafraîchissement du token\n    const refreshAccessToken = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!state.refreshToken) return false;\n        try {\n            const response = await fetch(`${API_BASE_URL}/auth/refresh`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken: state.refreshToken\n                })\n            });\n            if (!response.ok) {\n                clearTokens();\n                return false;\n            }\n            const { accessToken } = await response.json();\n            localStorage.setItem(\"wali_access_token\", accessToken);\n            setState((prev)=>({\n                    ...prev,\n                    accessToken\n                }));\n            return true;\n        } catch (error) {\n            console.error(\"Erreur lors du rafra\\xeechissement du token:\", error);\n            clearTokens();\n            return false;\n        }\n    }, [\n        state.refreshToken,\n        clearTokens\n    ]);\n    // Déconnexion\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        clearTokens();\n    }, [\n        clearTokens\n    ]);\n    // Requête authentifiée\n    const authenticatedFetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (url, options = {})=>{\n        if (!state.accessToken) {\n            throw new Error(\"Token d'acc\\xe8s manquant\");\n        }\n        const response = await fetch(url, {\n            ...options,\n            headers: {\n                ...options.headers,\n                \"Authorization\": `Bearer ${state.accessToken}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Si le token a expiré, essayer de le rafraîchir\n        if (response.status === 401) {\n            const refreshed = await refreshAccessToken();\n            if (refreshed) {\n                // Retry avec le nouveau token\n                return fetch(url, {\n                    ...options,\n                    headers: {\n                        ...options.headers,\n                        \"Authorization\": `Bearer ${state.accessToken}`,\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            } else {\n                throw new Error(\"Session expir\\xe9e\");\n            }\n        }\n        return response;\n    }, [\n        state.accessToken,\n        refreshAccessToken\n    ]);\n    return {\n        // État\n        ...state,\n        // Actions\n        register,\n        login,\n        verifyOtp,\n        logout,\n        refreshAccessToken,\n        authenticatedFetch,\n        // Utilitaires\n        clearError: ()=>setState((prev)=>({\n                    ...prev,\n                    error: null\n                }))\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ABIDJAN_DISTRICTS: () => (/* reexport safe */ _types_address__WEBPACK_IMPORTED_MODULE_1__.ABIDJAN_DISTRICTS),\n/* harmony export */   API_ENDPOINTS: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.API_ENDPOINTS),\n/* harmony export */   APP_CONFIG: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.APP_CONFIG),\n/* harmony export */   BUSINESS_TYPES: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.BUSINESS_TYPES),\n/* harmony export */   BusinessType: () => (/* reexport safe */ _types_user__WEBPACK_IMPORTED_MODULE_0__.BusinessType),\n/* harmony export */   DEFAULT_COORDINATES: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.DEFAULT_COORDINATES),\n/* harmony export */   IVORY_COAST_CITIES: () => (/* reexport safe */ _types_address__WEBPACK_IMPORTED_MODULE_1__.IVORY_COAST_CITIES),\n/* harmony export */   ORDER_STATUSES: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.ORDER_STATUSES),\n/* harmony export */   OrderStatus: () => (/* reexport safe */ _types_order__WEBPACK_IMPORTED_MODULE_2__.OrderStatus),\n/* harmony export */   OrderType: () => (/* reexport safe */ _types_order__WEBPACK_IMPORTED_MODULE_2__.OrderType),\n/* harmony export */   PAYMENT_METHODS: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.PAYMENT_METHODS),\n/* harmony export */   PRICING: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.PRICING),\n/* harmony export */   PaymentMethod: () => (/* reexport safe */ _types_payment__WEBPACK_IMPORTED_MODULE_3__.PaymentMethod),\n/* harmony export */   TransactionStatus: () => (/* reexport safe */ _types_payment__WEBPACK_IMPORTED_MODULE_3__.TransactionStatus),\n/* harmony export */   TransactionType: () => (/* reexport safe */ _types_payment__WEBPACK_IMPORTED_MODULE_3__.TransactionType),\n/* harmony export */   UserRole: () => (/* reexport safe */ _types_user__WEBPACK_IMPORTED_MODULE_0__.UserRole),\n/* harmony export */   VEHICLE_TYPES: () => (/* reexport safe */ _utils_constants__WEBPACK_IMPORTED_MODULE_6__.VEHICLE_TYPES),\n/* harmony export */   VehicleType: () => (/* reexport safe */ _types_user__WEBPACK_IMPORTED_MODULE_0__.VehicleType),\n/* harmony export */   addressSchema: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.addressSchema),\n/* harmony export */   emailSchema: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.emailSchema),\n/* harmony export */   formatPhone: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.formatPhone),\n/* harmony export */   orderCreationSchema: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.orderCreationSchema),\n/* harmony export */   passwordSchema: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.passwordSchema),\n/* harmony export */   phoneSchema: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.phoneSchema),\n/* harmony export */   userRegistrationSchema: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.userRegistrationSchema),\n/* harmony export */   validateEmail: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.validateEmail),\n/* harmony export */   validatePhone: () => (/* reexport safe */ _utils_validation__WEBPACK_IMPORTED_MODULE_5__.validatePhone)\n/* harmony export */ });\n/* harmony import */ var _types_user__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types/user */ \"(ssr)/../../packages/shared/src/types/user.ts\");\n/* harmony import */ var _types_address__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types/address */ \"(ssr)/../../packages/shared/src/types/address.ts\");\n/* harmony import */ var _types_order__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./types/order */ \"(ssr)/../../packages/shared/src/types/order.ts\");\n/* harmony import */ var _types_payment__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./types/payment */ \"(ssr)/../../packages/shared/src/types/payment.ts\");\n/* harmony import */ var _types_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types/common */ \"(ssr)/../../packages/shared/src/types/common.ts\");\n/* harmony import */ var _utils_validation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/validation */ \"(ssr)/../../packages/shared/src/utils/validation.ts\");\n/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./utils/constants */ \"(ssr)/../../packages/shared/src/utils/constants.ts\");\n// Types partagés pour WALI Livraison\n\n\n\n\n\n// Utilitaires partagés\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEscUNBQXFDO0FBQ1I7QUFDRztBQUNGO0FBQ0U7QUFDRDtBQUUvQix1QkFBdUI7QUFDWTtBQUNEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzPzg2NmEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gVHlwZXMgcGFydGFnw6lzIHBvdXIgV0FMSSBMaXZyYWlzb25cbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMvdXNlcic7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzL2FkZHJlc3MnO1xuZXhwb3J0ICogZnJvbSAnLi90eXBlcy9vcmRlcic7XG5leHBvcnQgKiBmcm9tICcuL3R5cGVzL3BheW1lbnQnO1xuZXhwb3J0ICogZnJvbSAnLi90eXBlcy9jb21tb24nO1xuXG4vLyBVdGlsaXRhaXJlcyBwYXJ0YWfDqXNcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMvdmFsaWRhdGlvbic7XG5leHBvcnQgKiBmcm9tICcuL3V0aWxzL2NvbnN0YW50cyc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/types/address.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/types/address.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ABIDJAN_DISTRICTS: () => (/* binding */ ABIDJAN_DISTRICTS),\n/* harmony export */   IVORY_COAST_CITIES: () => (/* binding */ IVORY_COAST_CITIES)\n/* harmony export */ });\n// Types pour la gestion des adresses\n// Constantes pour la Côte d'Ivoire\nconst IVORY_COAST_CITIES = [\n    \"Abidjan\",\n    \"Bouak\\xe9\",\n    \"Daloa\",\n    \"Yamoussoukro\",\n    \"San-P\\xe9dro\",\n    \"Korhogo\",\n    \"Man\",\n    \"Divo\",\n    \"Gagnoa\",\n    \"Anyama\",\n    \"Abengourou\",\n    \"Agboville\",\n    \"Grand-Bassam\",\n    \"Dabou\",\n    \"Grand-Lahou\",\n    \"Issia\",\n    \"Sinfra\",\n    \"Soubr\\xe9\",\n    \"Adzop\\xe9\",\n    \"Bongouanou\"\n];\nconst ABIDJAN_DISTRICTS = [\n    \"Abobo\",\n    \"Adjam\\xe9\",\n    \"Att\\xe9coub\\xe9\",\n    \"Cocody\",\n    \"Koumassi\",\n    \"Marcory\",\n    \"Plateau\",\n    \"Port-Bou\\xebt\",\n    \"Treichville\",\n    \"Yopougon\",\n    \"Bingerville\",\n    \"Songon\",\n    \"Anyama\"\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types/address.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/types/common.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/types/common.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy90eXBlcy9jb21tb24udHMiLCJtYXBwaW5ncyI6IjtBQWtCQyIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy90eXBlcy9jb21tb24udHM/NWNkNSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgaW50ZXJmYWNlIEFwaVJlc3BvbnNlPFQgPSBhbnk+IHtcbiAgc3VjY2VzczogYm9vbGVhbjtcbiAgZGF0YT86IFQ7XG4gIG1lc3NhZ2U/OiBzdHJpbmc7XG4gIGVycm9yPzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFBhZ2luYXRlZFJlc3BvbnNlPFQgPSBhbnk+IHtcbiAgZGF0YTogVFtdO1xuICB0b3RhbDogbnVtYmVyO1xuICBwYWdlOiBudW1iZXI7XG4gIGxpbWl0OiBudW1iZXI7XG4gIHRvdGFsUGFnZXM6IG51bWJlcjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDb29yZGluYXRlcyB7XG4gIGxhdGl0dWRlOiBudW1iZXI7XG4gIGxvbmdpdHVkZTogbnVtYmVyO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types/common.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/types/order.ts":
/*!************************************************!*\
  !*** ../../packages/shared/src/types/order.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderStatus: () => (/* binding */ OrderStatus),\n/* harmony export */   OrderType: () => (/* binding */ OrderType)\n/* harmony export */ });\nvar OrderType;\n(function(OrderType) {\n    OrderType[\"DELIVERY\"] = \"DELIVERY\";\n    OrderType[\"FOOD\"] = \"FOOD\";\n    OrderType[\"SHOPPING\"] = \"SHOPPING\";\n})(OrderType || (OrderType = {}));\nvar OrderStatus;\n(function(OrderStatus) {\n    OrderStatus[\"PENDING\"] = \"PENDING\";\n    OrderStatus[\"CONFIRMED\"] = \"CONFIRMED\";\n    OrderStatus[\"ASSIGNED\"] = \"ASSIGNED\";\n    OrderStatus[\"PICKED_UP\"] = \"PICKED_UP\";\n    OrderStatus[\"IN_TRANSIT\"] = \"IN_TRANSIT\";\n    OrderStatus[\"DELIVERED\"] = \"DELIVERED\";\n    OrderStatus[\"CANCELLED\"] = \"CANCELLED\";\n    OrderStatus[\"FAILED\"] = \"FAILED\";\n})(OrderStatus || (OrderStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types/order.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/types/payment.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/types/payment.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PaymentMethod: () => (/* binding */ PaymentMethod),\n/* harmony export */   TransactionStatus: () => (/* binding */ TransactionStatus),\n/* harmony export */   TransactionType: () => (/* binding */ TransactionType)\n/* harmony export */ });\nvar TransactionType;\n(function(TransactionType) {\n    TransactionType[\"PAYMENT\"] = \"PAYMENT\";\n    TransactionType[\"REFUND\"] = \"REFUND\";\n    TransactionType[\"COMMISSION\"] = \"COMMISSION\";\n    TransactionType[\"DRIVER_PAYOUT\"] = \"DRIVER_PAYOUT\";\n    TransactionType[\"PARTNER_PAYOUT\"] = \"PARTNER_PAYOUT\";\n})(TransactionType || (TransactionType = {}));\nvar PaymentMethod;\n(function(PaymentMethod) {\n    PaymentMethod[\"CASH\"] = \"CASH\";\n    PaymentMethod[\"STRIPE\"] = \"STRIPE\";\n    PaymentMethod[\"ORANGE_MONEY\"] = \"ORANGE_MONEY\";\n    PaymentMethod[\"MTN_MONEY\"] = \"MTN_MONEY\";\n    PaymentMethod[\"WAVE\"] = \"WAVE\";\n})(PaymentMethod || (PaymentMethod = {}));\nvar TransactionStatus;\n(function(TransactionStatus) {\n    TransactionStatus[\"PENDING\"] = \"PENDING\";\n    TransactionStatus[\"COMPLETED\"] = \"COMPLETED\";\n    TransactionStatus[\"FAILED\"] = \"FAILED\";\n    TransactionStatus[\"CANCELLED\"] = \"CANCELLED\";\n    TransactionStatus[\"REFUNDED\"] = \"REFUNDED\";\n})(TransactionStatus || (TransactionStatus = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy90eXBlcy9wYXltZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7VUFlWUE7Ozs7OztHQUFBQSxvQkFBQUE7O1VBUUFDOzs7Ozs7R0FBQUEsa0JBQUFBOztVQVFBQzs7Ozs7O0dBQUFBLHNCQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy90eXBlcy9wYXltZW50LnRzPzhkNzMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGludGVyZmFjZSBUcmFuc2FjdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIG9yZGVySWQ6IHN0cmluZztcbiAgdXNlcklkOiBzdHJpbmc7XG4gIHR5cGU6IFRyYW5zYWN0aW9uVHlwZTtcbiAgbWV0aG9kOiBQYXltZW50TWV0aG9kO1xuICBhbW91bnQ6IG51bWJlcjtcbiAgY3VycmVuY3k6IHN0cmluZztcbiAgc3RhdHVzOiBUcmFuc2FjdGlvblN0YXR1cztcbiAgZXh0ZXJuYWxJZD86IHN0cmluZztcbiAgbWV0YWRhdGE/OiBSZWNvcmQ8c3RyaW5nLCBhbnk+O1xuICBjcmVhdGVkQXQ6IERhdGU7XG4gIHVwZGF0ZWRBdDogRGF0ZTtcbn1cblxuZXhwb3J0IGVudW0gVHJhbnNhY3Rpb25UeXBlIHtcbiAgUEFZTUVOVCA9ICdQQVlNRU5UJyxcbiAgUkVGVU5EID0gJ1JFRlVORCcsXG4gIENPTU1JU1NJT04gPSAnQ09NTUlTU0lPTicsXG4gIERSSVZFUl9QQVlPVVQgPSAnRFJJVkVSX1BBWU9VVCcsXG4gIFBBUlRORVJfUEFZT1VUID0gJ1BBUlRORVJfUEFZT1VUJ1xufVxuXG5leHBvcnQgZW51bSBQYXltZW50TWV0aG9kIHtcbiAgQ0FTSCA9ICdDQVNIJyxcbiAgU1RSSVBFID0gJ1NUUklQRScsXG4gIE9SQU5HRV9NT05FWSA9ICdPUkFOR0VfTU9ORVknLFxuICBNVE5fTU9ORVkgPSAnTVROX01PTkVZJyxcbiAgV0FWRSA9ICdXQVZFJ1xufVxuXG5leHBvcnQgZW51bSBUcmFuc2FjdGlvblN0YXR1cyB7XG4gIFBFTkRJTkcgPSAnUEVORElORycsXG4gIENPTVBMRVRFRCA9ICdDT01QTEVURUQnLFxuICBGQUlMRUQgPSAnRkFJTEVEJyxcbiAgQ0FOQ0VMTEVEID0gJ0NBTkNFTExFRCcsXG4gIFJFRlVOREVEID0gJ1JFRlVOREVEJ1xufVxuIl0sIm5hbWVzIjpbIlRyYW5zYWN0aW9uVHlwZSIsIlBheW1lbnRNZXRob2QiLCJUcmFuc2FjdGlvblN0YXR1cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types/payment.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/types/user.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/types/user.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessType: () => (/* binding */ BusinessType),\n/* harmony export */   UserRole: () => (/* binding */ UserRole),\n/* harmony export */   VehicleType: () => (/* binding */ VehicleType)\n/* harmony export */ });\nvar UserRole;\n(function(UserRole) {\n    UserRole[\"CLIENT\"] = \"CLIENT\";\n    UserRole[\"DRIVER\"] = \"DRIVER\";\n    UserRole[\"PARTNER\"] = \"PARTNER\";\n    UserRole[\"ADMIN\"] = \"ADMIN\";\n})(UserRole || (UserRole = {}));\nvar VehicleType;\n(function(VehicleType) {\n    VehicleType[\"MOTO\"] = \"MOTO\";\n    VehicleType[\"CAR\"] = \"CAR\";\n    VehicleType[\"TRUCK\"] = \"TRUCK\";\n    VehicleType[\"BICYCLE\"] = \"BICYCLE\";\n})(VehicleType || (VehicleType = {}));\nvar BusinessType;\n(function(BusinessType) {\n    BusinessType[\"RESTAURANT\"] = \"RESTAURANT\";\n    BusinessType[\"STORE\"] = \"STORE\";\n    BusinessType[\"PHARMACY\"] = \"PHARMACY\";\n    BusinessType[\"SUPERMARKET\"] = \"SUPERMARKET\";\n})(BusinessType || (BusinessType = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types/user.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/utils/constants.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/utils/constants.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_ENDPOINTS: () => (/* binding */ API_ENDPOINTS),\n/* harmony export */   APP_CONFIG: () => (/* binding */ APP_CONFIG),\n/* harmony export */   BUSINESS_TYPES: () => (/* binding */ BUSINESS_TYPES),\n/* harmony export */   DEFAULT_COORDINATES: () => (/* binding */ DEFAULT_COORDINATES),\n/* harmony export */   ORDER_STATUSES: () => (/* binding */ ORDER_STATUSES),\n/* harmony export */   PAYMENT_METHODS: () => (/* binding */ PAYMENT_METHODS),\n/* harmony export */   PRICING: () => (/* binding */ PRICING),\n/* harmony export */   VEHICLE_TYPES: () => (/* binding */ VEHICLE_TYPES)\n/* harmony export */ });\n// Constantes pour WALI Livraison\nconst APP_CONFIG = {\n    NAME: \"WALI Livraison\",\n    VERSION: \"1.0.0\",\n    DESCRIPTION: \"Plateforme de livraison multi-services en C\\xf4te d'Ivoire\"\n};\nconst API_ENDPOINTS = {\n    AUTH: \"/auth\",\n    USERS: \"/users\",\n    ORDERS: \"/orders\",\n    DRIVERS: \"/drivers\",\n    PAYMENTS: \"/payments\",\n    RESTAURANTS: \"/restaurants\",\n    STORES: \"/stores\",\n    PRODUCTS: \"/products\"\n};\nconst PAYMENT_METHODS = {\n    CASH: \"Esp\\xe8ces\",\n    STRIPE: \"Carte bancaire\",\n    ORANGE_MONEY: \"Orange Money\",\n    MTN_MONEY: \"MTN Mobile Money\",\n    WAVE: \"Wave\"\n};\nconst ORDER_STATUSES = {\n    PENDING: \"En attente\",\n    CONFIRMED: \"Confirm\\xe9e\",\n    ASSIGNED: \"Assign\\xe9e\",\n    PICKED_UP: \"R\\xe9cup\\xe9r\\xe9e\",\n    IN_TRANSIT: \"En transit\",\n    DELIVERED: \"Livr\\xe9e\",\n    CANCELLED: \"Annul\\xe9e\",\n    FAILED: \"\\xc9chec\"\n};\nconst VEHICLE_TYPES = {\n    MOTO: \"Moto\",\n    CAR: \"Voiture\",\n    TRUCK: \"Camion\",\n    BICYCLE: \"V\\xe9lo\"\n};\nconst BUSINESS_TYPES = {\n    RESTAURANT: \"Restaurant\",\n    STORE: \"Magasin\",\n    PHARMACY: \"Pharmacie\",\n    SUPERMARKET: \"Supermarch\\xe9\"\n};\nconst DEFAULT_COORDINATES = {\n    ABIDJAN: {\n        latitude: 5.3600,\n        longitude: -4.0083\n    }\n};\nconst PRICING = {\n    BASE_DELIVERY_PRICE: 1000,\n    PRICE_PER_KM: 200,\n    COMMISSION_RATE: 0.15 // 15%\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/utils/constants.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/utils/validation.ts":
/*!*****************************************************!*\
  !*** ../../packages/shared/src/utils/validation.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addressSchema: () => (/* binding */ addressSchema),\n/* harmony export */   emailSchema: () => (/* binding */ emailSchema),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   orderCreationSchema: () => (/* binding */ orderCreationSchema),\n/* harmony export */   passwordSchema: () => (/* binding */ passwordSchema),\n/* harmony export */   phoneSchema: () => (/* binding */ phoneSchema),\n/* harmony export */   userRegistrationSchema: () => (/* binding */ userRegistrationSchema),\n/* harmony export */   validateEmail: () => (/* binding */ validateEmail),\n/* harmony export */   validatePhone: () => (/* binding */ validatePhone)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/v3/types.js\");\n\n// Schémas de validation Zod\nconst phoneSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^\\+225[0-9]{8,10}$/, \"Num\\xe9ro de t\\xe9l\\xe9phone ivoirien invalide\");\nconst emailSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().email(\"Adresse email invalide\").optional();\nconst passwordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.string().min(8, \"Le mot de passe doit contenir au moins 8 caract\\xe8res\").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)/, \"Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre\");\nconst userRegistrationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    phone: phoneSchema,\n    email: emailSchema,\n    firstName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, \"Le pr\\xe9nom doit contenir au moins 2 caract\\xe8res\"),\n    lastName: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, \"Le nom doit contenir au moins 2 caract\\xe8res\"),\n    password: passwordSchema,\n    role: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        \"CLIENT\",\n        \"DRIVER\",\n        \"PARTNER\"\n    ]).default(\"CLIENT\")\n});\nconst addressSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    street: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(5, \"L'adresse doit contenir au moins 5 caract\\xe8res\"),\n    city: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, \"La ville doit contenir au moins 2 caract\\xe8res\"),\n    district: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    landmark: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    latitude: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-90).max(90),\n    longitude: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-180).max(180),\n    label: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional()\n});\nconst orderCreationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        \"DELIVERY\",\n        \"FOOD\",\n        \"SHOPPING\"\n    ]),\n    pickupAddress: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(5),\n    pickupLatitude: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-90).max(90),\n    pickupLongitude: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-180).max(180),\n    deliveryAddress: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(5),\n    deliveryLatitude: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-90).max(90),\n    deliveryLongitude: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(-180).max(180),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    scheduledAt: zod__WEBPACK_IMPORTED_MODULE_0__.date().optional(),\n    items: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.object({\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1),\n        description: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n        quantity: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(1),\n        unitPrice: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0)\n    })).optional()\n});\n// Fonctions utilitaires de validation\nconst validatePhone = (phone)=>{\n    return phoneSchema.safeParse(phone).success;\n};\nconst validateEmail = (email)=>{\n    return zod__WEBPACK_IMPORTED_MODULE_0__.string().email().safeParse(email).success;\n};\nconst formatPhone = (phone)=>{\n    // Formate le numéro de téléphone ivoirien\n    const cleaned = phone.replace(/\\D/g, \"\");\n    if (cleaned.startsWith(\"225\")) {\n        return `+${cleaned}`;\n    }\n    if (cleaned.length === 8 || cleaned.length === 10) {\n        return `+225${cleaned}`;\n    }\n    return phone;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/utils/validation.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/addresses/page.tsx":
/*!************************************!*\
  !*** ./src/app/addresses/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Mientior livraison app\apps\client-web\src\app\addresses\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst metadata = {\n    title: \"Next.js\",\n    description: \"Generated by Next.js\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTyxNQUFNQSxXQUFXO0lBQ3RCQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi9zcmMvYXBwL2xheW91dC50c3g/NTdhOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgbWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnTmV4dC5qcycsXG4gIGRlc2NyaXB0aW9uOiAnR2VuZXJhdGVkIGJ5IE5leHQuanMnLFxufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5PntjaGlsZHJlbn08L2JvZHk+XG4gICAgPC9odG1sPlxuICApXG59XG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJib2R5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faddresses%2Fpage&page=%2Faddresses%2Fpage&appPaths=%2Faddresses%2Fpage&pagePath=private-next-app-dir%2Faddresses%2Fpage.tsx&appDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();