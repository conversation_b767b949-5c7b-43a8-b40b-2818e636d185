export interface Address {
  id: string;
  userId?: string;
  restaurantId?: string;
  storeId?: string;
  label?: string;
  street: string;
  city: string;
  district?: string;
  landmark?: string;
  latitude: number;
  longitude: number;
  isDefault: boolean;
  createdAt: Date;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface Coordinates {
  latitude: number;
  longitude: number;
}
