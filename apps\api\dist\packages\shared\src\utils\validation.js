"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.formatPhone = exports.validateEmail = exports.validatePhone = exports.orderCreationSchema = exports.addressSchema = exports.userRegistrationSchema = exports.passwordSchema = exports.emailSchema = exports.phoneSchema = void 0;
const zod_1 = require("zod");
exports.phoneSchema = zod_1.z.string()
    .regex(/^\+225[0-9]{8,10}$/, 'Numéro de téléphone ivoirien invalide');
exports.emailSchema = zod_1.z.string()
    .email('Adresse email invalide')
    .optional();
exports.passwordSchema = zod_1.z.string()
    .min(8, 'Le mot de passe doit contenir au moins 8 caractères')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre');
exports.userRegistrationSchema = zod_1.z.object({
    phone: exports.phoneSchema,
    email: exports.emailSchema,
    firstName: zod_1.z.string().min(2, 'Le prénom doit contenir au moins 2 caractères'),
    lastName: zod_1.z.string().min(2, 'Le nom doit contenir au moins 2 caractères'),
    password: exports.passwordSchema,
    role: zod_1.z.enum(['CLIENT', 'DRIVER', 'PARTNER']).default('CLIENT')
});
exports.addressSchema = zod_1.z.object({
    street: zod_1.z.string().min(5, 'L\'adresse doit contenir au moins 5 caractères'),
    city: zod_1.z.string().min(2, 'La ville doit contenir au moins 2 caractères'),
    district: zod_1.z.string().optional(),
    landmark: zod_1.z.string().optional(),
    latitude: zod_1.z.number().min(-90).max(90),
    longitude: zod_1.z.number().min(-180).max(180),
    label: zod_1.z.string().optional()
});
exports.orderCreationSchema = zod_1.z.object({
    type: zod_1.z.enum(['DELIVERY', 'FOOD', 'SHOPPING']),
    pickupAddress: zod_1.z.string().min(5),
    pickupLatitude: zod_1.z.number().min(-90).max(90),
    pickupLongitude: zod_1.z.number().min(-180).max(180),
    deliveryAddress: zod_1.z.string().min(5),
    deliveryLatitude: zod_1.z.number().min(-90).max(90),
    deliveryLongitude: zod_1.z.number().min(-180).max(180),
    notes: zod_1.z.string().optional(),
    scheduledAt: zod_1.z.date().optional(),
    items: zod_1.z.array(zod_1.z.object({
        name: zod_1.z.string().min(1),
        description: zod_1.z.string().optional(),
        quantity: zod_1.z.number().min(1),
        unitPrice: zod_1.z.number().min(0)
    })).optional()
});
const validatePhone = (phone) => {
    return exports.phoneSchema.safeParse(phone).success;
};
exports.validatePhone = validatePhone;
const validateEmail = (email) => {
    return zod_1.z.string().email().safeParse(email).success;
};
exports.validateEmail = validateEmail;
const formatPhone = (phone) => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('225')) {
        return `+${cleaned}`;
    }
    if (cleaned.length === 8 || cleaned.length === 10) {
        return `+225${cleaned}`;
    }
    return phone;
};
exports.formatPhone = formatPhone;
//# sourceMappingURL=validation.js.map