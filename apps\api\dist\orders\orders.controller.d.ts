import { OrdersService } from './orders.service';
import { CreateOrderDto, UpdateOrderDto, PriceCalculationDto } from './dto';
import { User } from '@prisma/client';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    calculatePrice(priceCalculationDto: PriceCalculationDto): Promise<PriceCalculationResult>;
    createOrder(user: User, createOrderDto: CreateOrderDto): Promise<OrderInterface>;
    getUserOrders(user: User, page?: number, limit?: number): Promise<{
        orders: OrderInterface[];
        total: number;
    }>;
    getOrderById(id: string, user: User): Promise<OrderInterface>;
    updateOrder(id: string, user: User, updateOrderDto: UpdateOrderDto): Promise<OrderInterface>;
    cancelOrder(id: string, user: User, reason?: string): Promise<OrderInterface>;
    getOrderTracking(id: string, user: User): Promise<{
        order: OrderInterface;
        currentLocation: any;
        estimatedArrival: any;
        trackingEvents: any;
    }>;
    rateOrder(id: string, user: User, rating: number, comment?: string): Promise<{
        message: string;
        rating: number;
        comment: string;
    }>;
}
