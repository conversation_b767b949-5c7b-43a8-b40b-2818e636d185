"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const prisma_service_1 = require("../prisma/prisma.service");
const sms_service_1 = require("./sms.service");
const client_1 = require("@prisma/client");
const libphonenumber_js_1 = require("libphonenumber-js");
let AuthService = AuthService_1 = class AuthService {
    constructor(prisma, jwtService, configService, smsService) {
        this.prisma = prisma;
        this.jwtService = jwtService;
        this.configService = configService;
        this.smsService = smsService;
        this.logger = new common_1.Logger(AuthService_1.name);
    }
    async register(registerDto) {
        const { phone, email, firstName, lastName, role } = registerDto;
        const normalizedPhone = this.normalizePhoneNumber(phone);
        const existingUser = await this.prisma.user.findUnique({
            where: { phone: normalizedPhone }
        });
        if (existingUser) {
            throw new common_1.ConflictException('Un compte existe déjà avec ce numéro de téléphone');
        }
        if (email) {
            const existingEmail = await this.prisma.user.findUnique({
                where: { email }
            });
            if (existingEmail) {
                throw new common_1.ConflictException('Un compte existe déjà avec cette adresse email');
            }
        }
        await this.prisma.user.create({
            data: {
                phone: normalizedPhone,
                email,
                firstName,
                lastName,
                role: role || client_1.UserRole.CLIENT,
                isVerified: false,
                isActive: true
            }
        });
        const otp = this.smsService.generateOtp(normalizedPhone);
        const smsSent = await this.smsService.sendOtp(normalizedPhone, otp);
        if (!smsSent) {
            this.logger.warn(`Échec de l'envoi du SMS d'inscription pour ${normalizedPhone}`);
        }
        this.logger.log(`Utilisateur créé avec succès: ${normalizedPhone}`);
        return {
            message: 'Compte créé avec succès. Un code de vérification a été envoyé par SMS.',
            phone: normalizedPhone
        };
    }
    async login(loginDto) {
        const { phone } = loginDto;
        const normalizedPhone = this.normalizePhoneNumber(phone);
        const user = await this.prisma.user.findUnique({
            where: { phone: normalizedPhone }
        });
        if (!user) {
            throw new common_1.NotFoundException('Aucun compte trouvé avec ce numéro de téléphone');
        }
        if (!user.isActive) {
            throw new common_1.UnauthorizedException('Votre compte a été désactivé. Contactez le support.');
        }
        const otp = this.smsService.generateOtp(normalizedPhone);
        const smsSent = await this.smsService.sendOtp(normalizedPhone, otp);
        if (!smsSent) {
            this.logger.warn(`Échec de l'envoi du SMS de connexion pour ${normalizedPhone}`);
        }
        this.logger.log(`Code OTP envoyé pour connexion: ${normalizedPhone}`);
        return {
            message: 'Un code de vérification a été envoyé par SMS.',
            phone: normalizedPhone
        };
    }
    async verifyOtp(verifyOtpDto) {
        const { phone, otp } = verifyOtpDto;
        const normalizedPhone = this.normalizePhoneNumber(phone);
        const isValidOtp = this.smsService.verifyOtp(normalizedPhone, otp);
        if (!isValidOtp) {
            throw new common_1.UnauthorizedException('Code de vérification invalide ou expiré');
        }
        const user = await this.prisma.user.findUnique({
            where: { phone: normalizedPhone }
        });
        if (!user) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        if (!user.isVerified) {
            await this.prisma.user.update({
                where: { id: user.id },
                data: { isVerified: true }
            });
            await this.smsService.sendWelcomeSms(normalizedPhone, user.firstName);
        }
        const tokens = await this.generateTokens(user);
        this.logger.log(`Utilisateur authentifié avec succès: ${normalizedPhone}`);
        return {
            user: {
                id: user.id,
                phone: user.phone,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                avatar: user.avatar,
                role: user.role,
                isActive: user.isActive,
                isVerified: true
            },
            ...tokens
        };
    }
    async refreshToken(refreshTokenDto) {
        const { refreshToken } = refreshTokenDto;
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: this.configService.get('JWT_REFRESH_SECRET')
            });
            if (payload.type !== 'refresh') {
                throw new common_1.UnauthorizedException('Token de rafraîchissement invalide');
            }
            const user = await this.prisma.user.findUnique({
                where: { id: payload.sub }
            });
            if (!user || !user.isActive) {
                throw new common_1.UnauthorizedException('Utilisateur non trouvé ou désactivé');
            }
            const accessToken = await this.generateAccessToken(user);
            return { accessToken };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Token de rafraîchissement invalide ou expiré');
        }
    }
    async generateTokens(user) {
        const [accessToken, refreshToken] = await Promise.all([
            this.generateAccessToken(user),
            this.generateRefreshToken(user)
        ]);
        return { accessToken, refreshToken };
    }
    async generateAccessToken(user) {
        const payload = {
            sub: user.id,
            phone: user.phone,
            role: user.role,
            type: 'access'
        };
        return this.jwtService.signAsync(payload, {
            secret: this.configService.get('JWT_SECRET'),
            expiresIn: this.configService.get('JWT_EXPIRES_IN') || '15m'
        });
    }
    async generateRefreshToken(user) {
        const payload = {
            sub: user.id,
            phone: user.phone,
            role: user.role,
            type: 'refresh'
        };
        return this.jwtService.signAsync(payload, {
            secret: this.configService.get('JWT_REFRESH_SECRET'),
            expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN') || '7d'
        });
    }
    normalizePhoneNumber(phone) {
        try {
            const phoneNumber = (0, libphonenumber_js_1.parsePhoneNumber)(phone, 'CI');
            if (!phoneNumber.isValid()) {
                throw new common_1.BadRequestException('Numéro de téléphone invalide');
            }
            return phoneNumber.format('E.164');
        }
        catch (error) {
            throw new common_1.BadRequestException('Format de numéro de téléphone invalide');
        }
    }
    async validateUser(userId) {
        return this.prisma.user.findUnique({
            where: { id: userId, isActive: true }
        });
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        jwt_1.JwtService,
        config_1.ConfigService,
        sms_service_1.SmsService])
], AuthService);
//# sourceMappingURL=auth.service.js.map