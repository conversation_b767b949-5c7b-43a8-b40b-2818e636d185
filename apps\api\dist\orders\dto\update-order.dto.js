"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateOrderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const shared_1 = require("@wali/shared");
class UpdateOrderDto {
}
exports.UpdateOrderDto = UpdateOrderDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nouveau statut de la commande',
        enum: shared_1.OrderStatus,
        example: shared_1.OrderStatus.CONFIRMED,
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(shared_1.OrderStatus, {
        message: 'Le statut doit être PENDING, CONFIRMED, ASSIGNED, PICKED_UP, IN_TRANSIT, DELIVERED, CANCELLED ou FAILED'
    }),
    __metadata("design:type", typeof (_a = typeof shared_1.OrderStatus !== "undefined" && shared_1.OrderStatus) === "function" ? _a : Object)
], UpdateOrderDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID du livreur assigné',
        example: 'cuid123456789',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'L\'ID du livreur doit être une chaîne de caractères' }),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "driverId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Notes ou instructions mises à jour',
        example: 'Client a changé d\'adresse, nouvelle adresse confirmée',
        required: false,
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Les notes doivent être une chaîne de caractères' }),
    (0, class_validator_1.MaxLength)(500, { message: 'Les notes ne peuvent pas dépasser 500 caractères' }),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "notes", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nouvelle date et heure de livraison souhaitée',
        example: '2024-01-15T16:00:00Z',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)({}, { message: 'La date de livraison doit être au format ISO 8601' }),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "scheduledAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Preuve de livraison (URL de photo ou signature)',
        example: 'https://storage.wali.ci/proofs/order-123-delivered.jpg',
        required: false,
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'La preuve de livraison doit être une chaîne de caractères' }),
    (0, class_validator_1.MaxLength)(500, { message: 'La preuve de livraison ne peut pas dépasser 500 caractères' }),
    __metadata("design:type", String)
], UpdateOrderDto.prototype, "proofOfDelivery", void 0);
//# sourceMappingURL=update-order.dto.js.map