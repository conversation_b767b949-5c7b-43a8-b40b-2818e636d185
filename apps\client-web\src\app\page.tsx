'use client';

import { useState } from 'react';
import { MapPin, Package, Clock, Star } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { DeliveryForm } from '@/components/delivery-form';
import { PriceCalculator } from '@/components/price-calculator';

export default function HomePage() {
  const [activeTab, setActiveTab] = useState<'delivery' | 'food' | 'shopping'>('delivery');

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Package className="h-8 w-8 text-blue-600" />
              <span className="ml-2 text-xl font-bold text-gray-900">WALI Livraison</span>
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#" className="text-gray-500 hover:text-gray-900">Accueil</a>
              <a href="#" className="text-gray-500 hover:text-gray-900">Services</a>
              <a href="#" className="text-gray-500 hover:text-gray-900">Tarifs</a>
              <a href="#" className="text-gray-500 hover:text-gray-900">Contact</a>
            </nav>
            <div className="flex items-center space-x-4">
              <Button variant="outline">Connexion</Button>
              <Button>Inscription</Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
              Livraison rapide et fiable
              <span className="text-blue-600"> en Côte d'Ivoire</span>
            </h1>
            <p className="mt-3 max-w-md mx-auto text-base text-gray-500 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
              Envoyez vos colis, commandez vos repas ou faites vos courses. 
              WALI Livraison vous connecte avec des livreurs de confiance partout en Côte d'Ivoire.
            </p>
          </div>

          {/* Service Tabs */}
          <div className="mt-16">
            <div className="flex justify-center space-x-4 mb-8">
              <Button
                variant={activeTab === 'delivery' ? 'default' : 'outline'}
                onClick={() => setActiveTab('delivery')}
                className="flex items-center space-x-2"
              >
                <Package className="h-4 w-4" />
                <span>Envoyer un Colis</span>
              </Button>
              <Button
                variant={activeTab === 'food' ? 'default' : 'outline'}
                onClick={() => setActiveTab('food')}
                className="flex items-center space-x-2"
                disabled
              >
                <span>🍽️</span>
                <span>Commander Repas</span>
              </Button>
              <Button
                variant={activeTab === 'shopping' ? 'default' : 'outline'}
                onClick={() => setActiveTab('shopping')}
                className="flex items-center space-x-2"
                disabled
              >
                <span>🛒</span>
                <span>Faire ses Courses</span>
              </Button>
            </div>

            {/* Delivery Form */}
            {activeTab === 'delivery' && (
              <div className="max-w-4xl mx-auto">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Package className="h-5 w-5" />
                      <span>Envoyer un Colis</span>
                    </CardTitle>
                    <CardDescription>
                      Remplissez les informations ci-dessous pour calculer le prix et créer votre commande
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <DeliveryForm />
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">
              Pourquoi choisir WALI Livraison ?
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
              Une plateforme moderne adaptée aux réalités ivoiriennes
            </p>
          </div>

          <div className="mt-16">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              <div className="text-center">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto">
                  <Clock className="h-6 w-6" />
                </div>
                <h3 className="mt-6 text-lg font-medium text-gray-900">Livraison Rapide</h3>
                <p className="mt-2 text-base text-gray-500">
                  Livraison en moins de 2 heures dans Abidjan et les grandes villes
                </p>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto">
                  <MapPin className="h-6 w-6" />
                </div>
                <h3 className="mt-6 text-lg font-medium text-gray-900">Suivi en Temps Réel</h3>
                <p className="mt-2 text-base text-gray-500">
                  Suivez votre livreur en direct sur la carte jusqu'à la livraison
                </p>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white mx-auto">
                  <Star className="h-6 w-6" />
                </div>
                <h3 className="mt-6 text-lg font-medium text-gray-900">Livreurs Vérifiés</h3>
                <p className="mt-2 text-base text-gray-500">
                  Tous nos livreurs sont vérifiés et notés par la communauté
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Payment Methods */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900">
              Moyens de Paiement
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-500">
              Payez comme vous voulez, en toute sécurité
            </p>
          </div>

          <div className="mt-12 flex justify-center items-center space-x-8">
            <div className="flex items-center space-x-2 bg-white p-4 rounded-lg shadow">
              <span className="text-orange-500 font-bold">Orange Money</span>
            </div>
            <div className="flex items-center space-x-2 bg-white p-4 rounded-lg shadow">
              <span className="text-yellow-500 font-bold">MTN Mobile Money</span>
            </div>
            <div className="flex items-center space-x-2 bg-white p-4 rounded-lg shadow">
              <span className="text-blue-500 font-bold">Wave</span>
            </div>
            <div className="flex items-center space-x-2 bg-white p-4 rounded-lg shadow">
              <span className="text-purple-500 font-bold">Stripe</span>
            </div>
            <div className="flex items-center space-x-2 bg-white p-4 rounded-lg shadow">
              <span className="text-green-500 font-bold">Espèces</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center">
              <Package className="h-8 w-8 text-blue-400" />
              <span className="ml-2 text-xl font-bold text-white">WALI Livraison</span>
            </div>
            <p className="mt-4 text-gray-400">
              La plateforme de livraison de référence en Côte d'Ivoire
            </p>
            <div className="mt-8 flex justify-center space-x-6">
              <a href="#" className="text-gray-400 hover:text-gray-300">
                Conditions d'utilisation
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-300">
                Politique de confidentialité
              </a>
              <a href="#" className="text-gray-400 hover:text-gray-300">
                Support
              </a>
            </div>
            <p className="mt-8 text-gray-400">
              © 2024 WALI Livraison. Tous droits réservés.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
