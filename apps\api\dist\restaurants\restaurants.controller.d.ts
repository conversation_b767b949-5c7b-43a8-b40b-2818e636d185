import { RestaurantsService } from './restaurants.service';
import { CreateRestaurantDto, UpdateRestaurantDto, RestaurantQueryDto } from './dto';
export declare class RestaurantsController {
    private readonly restaurantsService;
    constructor(restaurantsService: RestaurantsService);
    findAll(query: RestaurantQueryDto): Promise<any>;
    findOne(id: string): Promise<any>;
    getMenu(id: string): Promise<any>;
    create(createRestaurantDto: CreateRestaurantDto, user: any): Promise<any>;
    update(id: string, updateRestaurantDto: UpdateRestaurantDto, user: any): Promise<any>;
    updateHours(id: string, openingHours: any, user: any): Promise<any>;
    remove(id: string): Promise<any>;
}
