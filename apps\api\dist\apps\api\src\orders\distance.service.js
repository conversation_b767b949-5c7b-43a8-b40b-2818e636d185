"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DistanceService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DistanceService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let DistanceService = DistanceService_1 = class DistanceService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(DistanceService_1.name);
        this.googleMapsApiKey = this.configService.get('GOOGLE_MAPS_API_KEY') || '';
    }
    async calculateDistanceAndDuration(originLat, originLng, destLat, destLng) {
        if (!this.googleMapsApiKey) {
            this.logger.warn('Google Maps API non configurée, utilisation du calcul haversine');
            return this.calculateHaversineDistance(originLat, originLng, destLat, destLng);
        }
        try {
            const origin = `${originLat},${originLng}`;
            const destination = `${destLat},${destLng}`;
            const url = `https://maps.googleapis.com/maps/api/distancematrix/json?` +
                `origins=${origin}&destinations=${destination}` +
                `&mode=driving&language=fr&region=ci` +
                `&traffic_model=best_guess&departure_time=now` +
                `&key=${this.googleMapsApiKey}`;
            const response = await fetch(url);
            const data = await response.json();
            if (data.status === 'OK' && data.rows.length > 0) {
                const element = data.rows[0].elements[0];
                if (element.status === 'OK') {
                    const distanceKm = element.distance.value / 1000;
                    const durationMin = Math.ceil(element.duration.value / 60);
                    this.logger.log(`Distance calculée via Google Maps: ${distanceKm}km, ${durationMin}min`);
                    return {
                        distance: Math.round(distanceKm * 100) / 100,
                        duration: durationMin,
                        status: 'OK',
                    };
                }
                else {
                    this.logger.warn(`Élément non trouvé: ${element.status}`);
                    return this.calculateHaversineDistance(originLat, originLng, destLat, destLng);
                }
            }
            else {
                this.logger.warn(`API Distance Matrix échouée: ${data.status}`);
                return this.calculateHaversineDistance(originLat, originLng, destLat, destLng);
            }
        }
        catch (error) {
            this.logger.error('Erreur lors du calcul de distance via Google Maps', error);
            return this.calculateHaversineDistance(originLat, originLng, destLat, destLng);
        }
    }
    calculateHaversineDistance(lat1, lon1, lat2, lon2) {
        const R = 6371;
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;
        const estimatedDuration = Math.max(15, Math.round((distance / 25) * 60));
        this.logger.log(`Distance calculée via Haversine: ${distance}km, ${estimatedDuration}min`);
        return {
            distance: Math.round(distance * 100) / 100,
            duration: estimatedDuration,
            status: 'OK',
        };
    }
    async calculateMultipleDistances(origins, destinations) {
        if (!this.googleMapsApiKey) {
            const results = [];
            for (const origin of origins) {
                const row = [];
                for (const dest of destinations) {
                    row.push(this.calculateHaversineDistance(origin.lat, origin.lng, dest.lat, dest.lng));
                }
                results.push(row);
            }
            return results;
        }
        try {
            const originsStr = origins.map(o => `${o.lat},${o.lng}`).join('|');
            const destinationsStr = destinations.map(d => `${d.lat},${d.lng}`).join('|');
            const url = `https://maps.googleapis.com/maps/api/distancematrix/json?` +
                `origins=${originsStr}&destinations=${destinationsStr}` +
                `&mode=driving&language=fr&region=ci` +
                `&key=${this.googleMapsApiKey}`;
            const response = await fetch(url);
            const data = await response.json();
            if (data.status === 'OK') {
                return data.rows.map((row) => row.elements.map((element) => {
                    if (element.status === 'OK') {
                        return {
                            distance: Math.round((element.distance.value / 1000) * 100) / 100,
                            duration: Math.ceil(element.duration.value / 60),
                            status: 'OK',
                        };
                    }
                    else {
                        return {
                            distance: 0,
                            duration: 0,
                            status: 'NOT_FOUND',
                        };
                    }
                }));
            }
            else {
                throw new Error(`API Distance Matrix failed: ${data.status}`);
            }
        }
        catch (error) {
            this.logger.error('Erreur lors du calcul de distances multiples', error);
            const results = [];
            for (const origin of origins) {
                const row = [];
                for (const dest of destinations) {
                    row.push(this.calculateHaversineDistance(origin.lat, origin.lng, dest.lat, dest.lng));
                }
                results.push(row);
            }
            return results;
        }
    }
    async findNearestDriver(pickupLat, pickupLng, availableDrivers) {
        if (availableDrivers.length === 0) {
            return null;
        }
        const origins = [{ lat: pickupLat, lng: pickupLng }];
        const destinations = availableDrivers.map(d => ({ lat: d.lat, lng: d.lng }));
        const results = await this.calculateMultipleDistances(origins, destinations);
        if (results.length === 0 || results[0].length === 0) {
            return null;
        }
        let nearestDriver = null;
        let minDistance = Infinity;
        results[0].forEach((result, index) => {
            if (result.status === 'OK' && result.distance < minDistance) {
                minDistance = result.distance;
                nearestDriver = {
                    driverId: availableDrivers[index].id,
                    distance: result.distance,
                    duration: result.duration,
                };
            }
        });
        return nearestDriver;
    }
    isWithinServiceArea(latitude, longitude) {
        const abidjanBounds = {
            north: 5.45,
            south: 5.20,
            east: -3.85,
            west: -4.15
        };
        const isInAbidjan = latitude >= abidjanBounds.south &&
            latitude <= abidjanBounds.north &&
            longitude >= abidjanBounds.west &&
            longitude <= abidjanBounds.east;
        if (isInAbidjan) {
            return true;
        }
        const ivoryCoastBounds = {
            north: 10.74,
            south: 4.34,
            east: -2.49,
            west: -8.60
        };
        return latitude >= ivoryCoastBounds.south &&
            latitude <= ivoryCoastBounds.north &&
            longitude >= ivoryCoastBounds.west &&
            longitude <= ivoryCoastBounds.east;
    }
    estimateDeliveryTimeWithTraffic(distance, hour) {
        let baseTime = (distance / 25) * 60;
        if (hour >= 7 && hour <= 9) {
            baseTime *= 1.5;
        }
        else if (hour >= 17 && hour <= 19) {
            baseTime *= 1.7;
        }
        else if (hour >= 12 && hour <= 14) {
            baseTime *= 1.2;
        }
        else if (hour >= 22 || hour <= 5) {
            baseTime *= 0.8;
        }
        return Math.max(15, Math.round(baseTime));
    }
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
};
exports.DistanceService = DistanceService;
exports.DistanceService = DistanceService = DistanceService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], DistanceService);
//# sourceMappingURL=distance.service.js.map