# 🚚 WALI LIVRAISON - Application de Livraison Multi-services

## 📋 Vue d'Ensemble

**WALI LIVRAISON** est une plateforme unifiée de livraison à la demande spécialement conçue pour le marché ivoirien. L'application gère trois types de services de livraison :

1. **📦 Colis & Marchandises** - <PERSON>raison point A vers point B
2. **🍽️ Repas** - Livraison depuis restaurants partenaires  
3. **🛒 Courses** - Livraison depuis magasins et supermarchés

## 🏗️ Architecture Technique

### Stack Technologique
- **Monorepo :** Turborepo
- **Backend :** NestJS (TypeScript) + PostgreSQL + Prisma
- **Frontend Web :** Next.js + Shadcn/ui + TanStack Query
- **Mobile :** React Native (iOS/Android)
- **Authentification :** JWT + SMS (Twilio)
- **Paiements :** Stripe, Orange Money, MTN Mobile Money, Wave

### Structure du Projet
```
wali-livraison/
├── apps/
│   ├── api/                 # Backend NestJS
│   ├── admin-panel/         # Panel d'administration
│   ├── client-web/          # Site client
│   ├── mobile-client/       # App mobile client
│   └── mobile-driver/       # App mobile livreur
├── packages/
│   ├── ui/                  # Composants UI partagés
│   ├── database/            # Schémas Prisma
│   ├── shared/              # Types et utilitaires
│   └── mobile-ui/           # Composants React Native
└── docs/                    # Documentation
```

## 👥 Personas et Fonctionnalités

### 🧑‍💼 Client (Mobile + Web)
- Inscription/Connexion par téléphone
- Double flux : "Envoyer un colis" et "Commander repas/article"
- Suivi temps réel du livreur sur carte
- Historique des commandes et évaluations
- Paiements multiples (CB, Mobile Money, Espèces)

### 🏍️ Livreur (Mobile)
- Profil avec validation de documents
- Statut En ligne/Hors ligne
- Missions disponibles avec gains estimés
- Navigation GPS intégrée
- Preuve de livraison (photo/signature)
- Portefeuille virtuel

### 🏪 Partenaire/Vendeur (Web)
- Dashboard de gestion des commandes
- Gestion du catalogue produits/menus
- Mise à jour des statuts de commande
- Rapports de ventes

### 👨‍💻 Administrateur (Web)
- Dashboard avec KPIs et analytics
- Heatmap d'activité
- Gestion CRUD complète
- Configuration des tarifs
- Support client intégré

## 🚀 Plan de Développement

### Phase 1: Architecture et Configuration ✅
- [x] Setup Turborepo et structure du projet
- [x] Configuration des outils de développement
- [x] Initialisation des applications

### Phase 2: Backend Core 🔄
- [ ] Modules Auth, Users, Geolocation
- [ ] APIs de base et authentification
- [ ] Système de tarification

### Phase 3: MVP - Livraison de Colis
- [ ] Service de livraison point A vers B
- [ ] Interface client web basique
- [ ] App mobile livreur

### Phase 4: Applications Mobiles
- [ ] App client mobile complète
- [ ] App livreur mobile avancée
- [ ] Tests sur appareils réels

### Phase 5: Panel d'Administration
- [ ] Dashboard avec analytics
- [ ] Gestion complète des utilisateurs
- [ ] Système de support

### Phase 6: Extension E-commerce
- [ ] Catalogue restaurants/magasins
- [ ] Système de commande e-commerce
- [ ] Interface partenaire

### Phase 7: Intégrations Paiement
- [ ] Stripe, Orange Money, MTN, Wave
- [ ] Portefeuille multi-devises
- [ ] Gestion des commissions

### Phase 8: Tests et Déploiement
- [ ] Tests complets (unitaires, e2e, charge)
- [ ] Déploiement production
- [ ] Publication apps mobiles

## 📊 Modèles de Données Principaux

### User (Utilisateur)
```typescript
interface User {
  id: string;
  phone: string;
  email?: string;
  firstName: string;
  lastName: string;
  role: 'CLIENT' | 'DRIVER' | 'PARTNER' | 'ADMIN';
  isActive: boolean;
  createdAt: Date;
}
```

### Order (Commande)
```typescript
interface Order {
  id: string;
  orderNumber: string;
  clientId: string;
  driverId?: string;
  type: 'DELIVERY' | 'FOOD' | 'SHOPPING';
  status: 'PENDING' | 'CONFIRMED' | 'ASSIGNED' | 'DELIVERED';
  pickupAddress: string;
  deliveryAddress: string;
  totalAmount: number;
  createdAt: Date;
}
```

## 🔌 APIs Principales

### Authentification
- `POST /auth/register` - Inscription
- `POST /auth/login` - Connexion
- `POST /auth/verify-phone` - Vérification SMS

### Commandes
- `POST /orders` - Création commande
- `GET /orders/:id/tracking` - Suivi temps réel
- `PUT /orders/:id/status` - Mise à jour statut

### Livreurs
- `GET /drivers/orders/available` - Missions disponibles
- `POST /drivers/orders/:id/accept` - Accepter mission
- `PUT /drivers/location` - Mise à jour position

### Paiements
- `POST /payments/process` - Traitement paiement
- `POST /payments/mobile-money` - Paiement mobile money
- `GET /payments/:id/status` - Statut paiement

## 💳 Intégrations Paiement

### Solutions Locales
- **Orange Money** - API REST avec authentification OAuth
- **MTN Mobile Money** - API REST avec clés d'API
- **Wave** - API REST avec webhooks
- **Espèces** - Paiement à la livraison

### Solutions Internationales
- **Stripe** - Cartes bancaires avec 3D Secure

## 📱 Fonctionnalités Mobiles

### Géolocalisation
- Suivi temps réel des livreurs
- Navigation GPS intégrée
- Calcul automatique des distances
- Géocodage d'adresses

### Notifications Push
- Mises à jour de statut de commande
- Nouvelles missions pour livreurs
- Confirmations de paiement
- Messages de support

## 🔐 Sécurité

### Authentification
- JWT avec refresh tokens
- Vérification par SMS obligatoire
- Rate limiting sur les APIs
- Chiffrement des données sensibles

### Autorisations
- Contrôle d'accès basé sur les rôles (RBAC)
- Validation des permissions par endpoint
- Audit trail des actions administratives

## 📈 Métriques et Analytics

### KPIs Principaux
- Nombre de commandes par jour/semaine/mois
- Chiffre d'affaires total et par service
- Temps moyen de livraison
- Taux de satisfaction client
- Nombre de livreurs actifs

### Analytics Avancées
- Heatmap des zones de livraison
- Analyse des pics d'activité
- Performance des livreurs
- Rentabilité par zone géographique

## 🚀 Démarrage Rapide

### Prérequis
- Node.js 18+
- PostgreSQL 14+
- Redis (pour les sessions)
- Compte Twilio (SMS)
- Comptes des providers de paiement

### Installation
```bash
# Cloner le repository
git clone https://github.com/votre-org/wali-livraison.git
cd wali-livraison

# Installer les dépendances
npm install

# Configuration de la base de données
npm run db:generate
npm run db:push

# Démarrer en mode développement
npm run dev
```

### Variables d'Environnement
```env
DATABASE_URL="postgresql://user:password@localhost:5432/wali_livraison"
JWT_SECRET="your-super-secret-key"
TWILIO_ACCOUNT_SID="your-twilio-sid"
STRIPE_SECRET_KEY="sk_test_..."
GOOGLE_MAPS_API_KEY="your-google-maps-key"
```

## 📚 Documentation

- [Plan de Développement Détaillé](./PLAN_DEVELOPPEMENT.md)
- [Schéma de Base de Données](./DATABASE_SCHEMA.md)
- [Documentation des APIs](./API_ENDPOINTS.md)
- [Configuration du Projet](./PROJECT_SETUP.md)
- [Roadmap Détaillée](./ROADMAP_DETAILLEE.md)

## 🤝 Contribution

Ce projet suit la méthodologie Agile/Scrum avec des sprints de 2 semaines. Pour contribuer :

1. Créer une branche feature depuis `develop`
2. Implémenter les fonctionnalités avec tests
3. Créer une Pull Request avec description détaillée
4. Code review obligatoire avant merge

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier [LICENSE](./LICENSE) pour plus de détails.

---

**Équipe de Développement Recommandée :** 6-8 développeurs  
**Durée Estimée :** 24-30 semaines  
**Budget Estimé :** À définir selon les ressources et l'équipe
