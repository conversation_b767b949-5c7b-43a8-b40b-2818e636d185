"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const users_service_1 = require("./users.service");
const dto_1 = require("./dto");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
let UsersController = class UsersController {
    constructor(usersService) {
        this.usersService = usersService;
    }
    async getProfile(user) {
        return this.usersService.getProfile(user.id);
    }
    async updateProfile(user, updateProfileDto) {
        return this.usersService.updateProfile(user.id, updateProfileDto);
    }
    async getUserStats(user) {
        return this.usersService.getUserStats(user.id);
    }
    async getUserById(id) {
        return this.usersService.getUserById(id);
    }
    async deleteAccount(user) {
        return this.usersService.deleteAccount(user.id);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Get)('profile'),
    (0, swagger_1.ApiOperation)({
        summary: 'Profil de l\'utilisateur connecté',
        description: 'Récupère les informations complètes du profil de l\'utilisateur actuellement connecté'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Profil utilisateur récupéré avec succès.',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string', example: 'cuid123456789' },
                phone: { type: 'string', example: '+*************' },
                email: { type: 'string', example: '<EMAIL>' },
                firstName: { type: 'string', example: 'Kouassi' },
                lastName: { type: 'string', example: 'Yao' },
                avatar: { type: 'string', example: 'https://example.com/avatar.jpg' },
                role: { type: 'string', enum: ['CLIENT', 'DRIVER', 'PARTNER', 'ADMIN'] },
                isActive: { type: 'boolean', example: true },
                isVerified: { type: 'boolean', example: true }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Token d\'authentification requis'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Utilisateur non trouvé ou compte désactivé'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Put)('profile'),
    (0, swagger_1.ApiOperation)({
        summary: 'Mise à jour du profil utilisateur',
        description: 'Met à jour les informations du profil de l\'utilisateur connecté'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Profil mis à jour avec succès.',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                phone: { type: 'string' },
                email: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                avatar: { type: 'string' },
                role: { type: 'string' },
                isActive: { type: 'boolean' },
                isVerified: { type: 'boolean' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Données de mise à jour invalides'
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Adresse email déjà utilisée'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Utilisateur non trouvé'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, dto_1.UpdateProfileDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateProfile", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({
        summary: 'Statistiques de l\'utilisateur',
        description: 'Récupère les statistiques d\'utilisation de l\'utilisateur connecté'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Statistiques récupérées avec succès.',
        schema: {
            type: 'object',
            properties: {
                totalOrders: { type: 'number', example: 12 },
                completedOrders: { type: 'number', example: 10 },
                totalSpent: { type: 'number', example: 28500 },
                averageRating: { type: 'number', example: 4.8 }
            }
        }
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getUserStats", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Profil utilisateur par ID',
        description: 'Récupère les informations publiques d\'un utilisateur par son ID'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'ID de l\'utilisateur',
        example: 'cuid123456789'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Profil utilisateur récupéré avec succès.',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                avatar: { type: 'string' },
                role: { type: 'string' },
                isVerified: { type: 'boolean' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Utilisateur non trouvé'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getUserById", null);
__decorate([
    (0, common_1.Delete)('account'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Suppression du compte utilisateur',
        description: 'Supprime définitivement le compte de l\'utilisateur connecté'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Compte supprimé avec succès.',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Votre compte a été supprimé avec succès' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Utilisateur non trouvé'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "deleteAccount", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('Utilisateurs'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map