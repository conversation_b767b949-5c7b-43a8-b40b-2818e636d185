import { ProductsService } from './products.service';
import { CreateProductDto, UpdateProductDto, ProductQueryDto } from './dto';
export declare class ProductsController {
    private readonly productsService;
    constructor(productsService: ProductsService);
    findAll(query: ProductQueryDto): Promise<any>;
    findOne(id: string): Promise<any>;
    create(createProductDto: CreateProductDto, user: any): Promise<any>;
    update(id: string, updateProductDto: UpdateProductDto, user: any): Promise<any>;
    remove(id: string, user: any): Promise<any>;
}
