
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  phone: 'phone',
  email: 'email',
  firstName: 'firstName',
  lastName: 'lastName',
  avatar: 'avatar',
  role: 'role',
  isActive: 'isActive',
  isVerified: 'isVerified',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DriverProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  licenseNumber: 'licenseNumber',
  vehicleType: 'vehicleType',
  vehiclePlate: 'vehiclePlate',
  isOnline: 'isOnline',
  isAvailable: 'isAvailable',
  currentLatitude: 'currentLatitude',
  currentLongitude: 'currentLongitude',
  documentsVerified: 'documentsVerified',
  rating: 'rating',
  totalEarnings: 'totalEarnings',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DriverDocumentScalarFieldEnum = {
  id: 'id',
  driverProfileId: 'driverProfileId',
  type: 'type',
  url: 'url',
  isVerified: 'isVerified',
  createdAt: 'createdAt'
};

exports.Prisma.PartnerProfileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  businessName: 'businessName',
  businessType: 'businessType',
  description: 'description',
  logo: 'logo',
  isVerified: 'isVerified',
  isActive: 'isActive',
  rating: 'rating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderScalarFieldEnum = {
  id: 'id',
  orderNumber: 'orderNumber',
  clientId: 'clientId',
  driverId: 'driverId',
  type: 'type',
  status: 'status',
  pickupAddress: 'pickupAddress',
  pickupLatitude: 'pickupLatitude',
  pickupLongitude: 'pickupLongitude',
  deliveryAddress: 'deliveryAddress',
  deliveryLatitude: 'deliveryLatitude',
  deliveryLongitude: 'deliveryLongitude',
  basePrice: 'basePrice',
  deliveryFee: 'deliveryFee',
  totalAmount: 'totalAmount',
  estimatedDuration: 'estimatedDuration',
  scheduledAt: 'scheduledAt',
  pickedUpAt: 'pickedUpAt',
  deliveredAt: 'deliveredAt',
  notes: 'notes',
  proofOfDelivery: 'proofOfDelivery',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.OrderItemScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  productId: 'productId',
  name: 'name',
  description: 'description',
  quantity: 'quantity',
  unitPrice: 'unitPrice',
  totalPrice: 'totalPrice'
};

exports.Prisma.RestaurantScalarFieldEnum = {
  id: 'id',
  partnerProfileId: 'partnerProfileId',
  name: 'name',
  description: 'description',
  cuisine: 'cuisine',
  logo: 'logo',
  coverImage: 'coverImage',
  isOpen: 'isOpen',
  openingHours: 'openingHours',
  deliveryFee: 'deliveryFee',
  minimumOrder: 'minimumOrder',
  rating: 'rating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StoreScalarFieldEnum = {
  id: 'id',
  partnerProfileId: 'partnerProfileId',
  name: 'name',
  description: 'description',
  type: 'type',
  logo: 'logo',
  coverImage: 'coverImage',
  isOpen: 'isOpen',
  openingHours: 'openingHours',
  deliveryFee: 'deliveryFee',
  minimumOrder: 'minimumOrder',
  rating: 'rating',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ProductScalarFieldEnum = {
  id: 'id',
  restaurantId: 'restaurantId',
  storeId: 'storeId',
  categoryId: 'categoryId',
  name: 'name',
  description: 'description',
  price: 'price',
  image: 'image',
  isAvailable: 'isAvailable',
  stock: 'stock',
  preparationTime: 'preparationTime',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CategoryScalarFieldEnum = {
  id: 'id',
  restaurantId: 'restaurantId',
  storeId: 'storeId',
  name: 'name',
  description: 'description',
  image: 'image',
  sortOrder: 'sortOrder',
  createdAt: 'createdAt'
};

exports.Prisma.AddressScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  restaurantId: 'restaurantId',
  storeId: 'storeId',
  label: 'label',
  street: 'street',
  city: 'city',
  district: 'district',
  landmark: 'landmark',
  latitude: 'latitude',
  longitude: 'longitude',
  isDefault: 'isDefault',
  createdAt: 'createdAt'
};

exports.Prisma.TransactionScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  userId: 'userId',
  type: 'type',
  method: 'method',
  amount: 'amount',
  currency: 'currency',
  status: 'status',
  externalId: 'externalId',
  metadata: 'metadata',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.TrackingEventScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  status: 'status',
  latitude: 'latitude',
  longitude: 'longitude',
  description: 'description',
  createdAt: 'createdAt'
};

exports.Prisma.RatingScalarFieldEnum = {
  id: 'id',
  orderId: 'orderId',
  fromUserId: 'fromUserId',
  toUserId: 'toUserId',
  rating: 'rating',
  comment: 'comment',
  createdAt: 'createdAt'
};

exports.Prisma.NotificationScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  type: 'type',
  title: 'title',
  message: 'message',
  data: 'data',
  isRead: 'isRead',
  createdAt: 'createdAt'
};

exports.Prisma.SupportTicketScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  orderId: 'orderId',
  subject: 'subject',
  category: 'category',
  status: 'status',
  priority: 'priority',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SupportMessageScalarFieldEnum = {
  id: 'id',
  ticketId: 'ticketId',
  message: 'message',
  isFromUser: 'isFromUser',
  createdAt: 'createdAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.UserRole = exports.$Enums.UserRole = {
  CLIENT: 'CLIENT',
  DRIVER: 'DRIVER',
  PARTNER: 'PARTNER',
  ADMIN: 'ADMIN'
};

exports.VehicleType = exports.$Enums.VehicleType = {
  MOTO: 'MOTO',
  CAR: 'CAR',
  TRUCK: 'TRUCK',
  BICYCLE: 'BICYCLE'
};

exports.DocumentType = exports.$Enums.DocumentType = {
  LICENSE: 'LICENSE',
  ID_CARD: 'ID_CARD',
  VEHICLE_REGISTRATION: 'VEHICLE_REGISTRATION',
  INSURANCE: 'INSURANCE'
};

exports.BusinessType = exports.$Enums.BusinessType = {
  RESTAURANT: 'RESTAURANT',
  STORE: 'STORE',
  PHARMACY: 'PHARMACY',
  SUPERMARKET: 'SUPERMARKET'
};

exports.OrderType = exports.$Enums.OrderType = {
  DELIVERY: 'DELIVERY',
  FOOD: 'FOOD',
  SHOPPING: 'SHOPPING'
};

exports.OrderStatus = exports.$Enums.OrderStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  ASSIGNED: 'ASSIGNED',
  PICKED_UP: 'PICKED_UP',
  IN_TRANSIT: 'IN_TRANSIT',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  FAILED: 'FAILED'
};

exports.StoreType = exports.$Enums.StoreType = {
  SUPERMARKET: 'SUPERMARKET',
  PHARMACY: 'PHARMACY',
  ELECTRONICS: 'ELECTRONICS',
  CLOTHING: 'CLOTHING',
  GENERAL: 'GENERAL'
};

exports.TransactionType = exports.$Enums.TransactionType = {
  PAYMENT: 'PAYMENT',
  REFUND: 'REFUND',
  COMMISSION: 'COMMISSION',
  DRIVER_PAYOUT: 'DRIVER_PAYOUT',
  PARTNER_PAYOUT: 'PARTNER_PAYOUT'
};

exports.PaymentMethod = exports.$Enums.PaymentMethod = {
  CASH: 'CASH',
  STRIPE: 'STRIPE',
  ORANGE_MONEY: 'ORANGE_MONEY',
  MTN_MONEY: 'MTN_MONEY',
  WAVE: 'WAVE'
};

exports.TransactionStatus = exports.$Enums.TransactionStatus = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
  CANCELLED: 'CANCELLED',
  REFUNDED: 'REFUNDED'
};

exports.NotificationType = exports.$Enums.NotificationType = {
  ORDER_UPDATE: 'ORDER_UPDATE',
  PAYMENT_SUCCESS: 'PAYMENT_SUCCESS',
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  DRIVER_ASSIGNED: 'DRIVER_ASSIGNED',
  DELIVERY_COMPLETED: 'DELIVERY_COMPLETED',
  PROMOTION: 'PROMOTION',
  SYSTEM: 'SYSTEM'
};

exports.SupportCategory = exports.$Enums.SupportCategory = {
  ORDER_ISSUE: 'ORDER_ISSUE',
  PAYMENT_ISSUE: 'PAYMENT_ISSUE',
  TECHNICAL_ISSUE: 'TECHNICAL_ISSUE',
  ACCOUNT_ISSUE: 'ACCOUNT_ISSUE',
  GENERAL_INQUIRY: 'GENERAL_INQUIRY'
};

exports.SupportTicketStatus = exports.$Enums.SupportTicketStatus = {
  OPEN: 'OPEN',
  IN_PROGRESS: 'IN_PROGRESS',
  RESOLVED: 'RESOLVED',
  CLOSED: 'CLOSED'
};

exports.SupportPriority = exports.$Enums.SupportPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
};

exports.Prisma.ModelName = {
  User: 'User',
  DriverProfile: 'DriverProfile',
  DriverDocument: 'DriverDocument',
  PartnerProfile: 'PartnerProfile',
  Order: 'Order',
  OrderItem: 'OrderItem',
  Restaurant: 'Restaurant',
  Store: 'Store',
  Product: 'Product',
  Category: 'Category',
  Address: 'Address',
  Transaction: 'Transaction',
  TrackingEvent: 'TrackingEvent',
  Rating: 'Rating',
  Notification: 'Notification',
  SupportTicket: 'SupportTicket',
  SupportMessage: 'SupportMessage'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
