import { PrismaService } from '../prisma/prisma.service';
import { GeocodingService } from './geocoding.service';
import { CreateAddressDto, UpdateAddressDto } from './dto';
import { Address } from '@prisma/client';
export declare class AddressesService {
    private readonly prisma;
    private readonly geocodingService;
    private readonly logger;
    constructor(prisma: PrismaService, geocodingService: GeocodingService);
    getUserAddresses(userId: string): Promise<Address[]>;
    getAddressById(addressId: string, userId: string): Promise<Address>;
    createAddress(userId: string, createAddressDto: CreateAddressDto): Promise<Address>;
    updateAddress(addressId: string, userId: string, updateAddressDto: UpdateAddressDto): Promise<Address>;
    deleteAddress(addressId: string, userId: string): Promise<{
        message: string;
    }>;
    setDefaultAddress(addressId: string, userId: string): Promise<Address>;
    searchNearbyAddresses(userId: string, latitude: number, longitude: number, radiusKm?: number): Promise<Array<Address & {
        distance: number;
    }>>;
    geocodeAddress(address: string): Promise<any>;
    reverseGeocode(latitude: number, longitude: number): Promise<any>;
}
