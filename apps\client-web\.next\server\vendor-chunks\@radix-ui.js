"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/../../node_modules/@radix-ui/number/dist/index.mjs":
/*!**********************************************************!*\
  !*** ../../node_modules/@radix-ui/number/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9udW1iZXIvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9udW1iZXIvZGlzdC9pbmRleC5tanM/MWY1YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL251bWJlci9zcmMvbnVtYmVyLnRzXG5mdW5jdGlvbiBjbGFtcCh2YWx1ZSwgW21pbiwgbWF4XSkge1xuICByZXR1cm4gTWF0aC5taW4obWF4LCBNYXRoLm1heChtaW4sIHZhbHVlKSk7XG59XG5leHBvcnQge1xuICBjbGFtcFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*************************************************************!*\
  !*** ../../node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsdUVBQXVFLGtDQUFrQyxJQUFJO0FBQzdHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9wcmltaXRpdmUvZGlzdC9pbmRleC5tanM/OWFlMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9jb3JlL3ByaW1pdGl2ZS9zcmMvcHJpbWl0aXZlLnRzeFxuZnVuY3Rpb24gY29tcG9zZUV2ZW50SGFuZGxlcnMob3JpZ2luYWxFdmVudEhhbmRsZXIsIG91ckV2ZW50SGFuZGxlciwgeyBjaGVja0ZvckRlZmF1bHRQcmV2ZW50ZWQgPSB0cnVlIH0gPSB7fSkge1xuICByZXR1cm4gZnVuY3Rpb24gaGFuZGxlRXZlbnQoZXZlbnQpIHtcbiAgICBvcmlnaW5hbEV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICBpZiAoY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID09PSBmYWxzZSB8fCAhZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgcmV0dXJuIG91ckV2ZW50SGFuZGxlcj8uKGV2ZW50KTtcbiAgICB9XG4gIH07XG59XG5leHBvcnQge1xuICBjb21wb3NlRXZlbnRIYW5kbGVyc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!***************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, width = 10, height = 5, ...arrowProps } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg,\n    {\n      ...arrowProps,\n      ref: forwardedRef,\n      width,\n      height,\n      viewBox: \"0 0 30 10\",\n      preserveAspectRatio: \"none\",\n      children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", { points: \"0,0 30,0 15,10\" })\n    }\n  );\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1hcnJvdy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ3VCO0FBQ2Q7QUFDeEM7QUFDQSxZQUFZLDZDQUFnQjtBQUM1QixVQUFVLGtEQUFrRDtBQUM1RCx5QkFBeUIsc0RBQUc7QUFDNUIsSUFBSSxnRUFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELHNEQUFHLGNBQWMsMEJBQTBCO0FBQ3RHO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2FsaS9jbGllbnQtd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtYXJyb3cvZGlzdC9pbmRleC5tanM/NjU4NyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvYXJyb3cudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IFByaW1pdGl2ZSB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlXCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBOQU1FID0gXCJBcnJvd1wiO1xudmFyIEFycm93ID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICBjb25zdCB7IGNoaWxkcmVuLCB3aWR0aCA9IDEwLCBoZWlnaHQgPSA1LCAuLi5hcnJvd1Byb3BzIH0gPSBwcm9wcztcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgUHJpbWl0aXZlLnN2ZyxcbiAgICB7XG4gICAgICAuLi5hcnJvd1Byb3BzLFxuICAgICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgICB3aWR0aCxcbiAgICAgIGhlaWdodCxcbiAgICAgIHZpZXdCb3g6IFwiMCAwIDMwIDEwXCIsXG4gICAgICBwcmVzZXJ2ZUFzcGVjdFJhdGlvOiBcIm5vbmVcIixcbiAgICAgIGNoaWxkcmVuOiBwcm9wcy5hc0NoaWxkID8gY2hpbGRyZW4gOiAvKiBAX19QVVJFX18gKi8ganN4KFwicG9seWdvblwiLCB7IHBvaW50czogXCIwLDAgMzAsMCAxNSwxMFwiIH0pXG4gICAgfVxuICApO1xufSk7XG5BcnJvdy5kaXNwbGF5TmFtZSA9IE5BTUU7XG52YXIgUm9vdCA9IEFycm93O1xuZXhwb3J0IHtcbiAgQXJyb3csXG4gIFJvb3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!****************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-is-hydrated */ \"(ssr)/../../node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/avatar.tsx\n\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      AvatarProvider,\n      {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, { ...avatarProps, ref: forwardedRef })\n      }\n    );\n  }\n);\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {\n    }, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)((status) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(() => {\n      if (imageLoadingStatus !== \"idle\") {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, { ...imageProps, ref: forwardedRef, src }) : null;\n  }\n);\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (delayMs !== void 0) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, { ...fallbackProps, ref: forwardedRef }) : null;\n  }\n);\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction resolveLoadingStatus(image, src) {\n  if (!image) {\n    return \"idle\";\n  }\n  if (!src) {\n    return \"error\";\n  }\n  if (image.src !== src) {\n    image.src = src;\n  }\n  return image.complete && image.naturalWidth > 0 ? \"loaded\" : \"loading\";\n}\nfunction useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {\n  const isHydrated = (0,_radix_ui_react_use_is_hydrated__WEBPACK_IMPORTED_MODULE_6__.useIsHydrated)();\n  const imageRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const image = (() => {\n    if (!isHydrated) return null;\n    if (!imageRef.current) {\n      imageRef.current = new window.Image();\n    }\n    return imageRef.current;\n  })();\n  const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\n    () => resolveLoadingStatus(image, src)\n  );\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(() => {\n    setLoadingStatus(resolveLoadingStatus(image, src));\n  }, [image, src]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(() => {\n    const updateStatus = (status) => () => {\n      setLoadingStatus(status);\n    };\n    if (!image) return;\n    const handleLoad = updateStatus(\"loaded\");\n    const handleError = updateStatus(\"error\");\n    image.addEventListener(\"load\", handleLoad);\n    image.addEventListener(\"error\", handleError);\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n    if (typeof crossOrigin === \"string\") {\n      image.crossOrigin = crossOrigin;\n    }\n    return () => {\n      image.removeEventListener(\"load\", handleLoad);\n      image.removeEventListener(\"error\", handleError);\n    };\n  }, [image, crossOrigin, referrerPolicy]);\n  return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs":
/*!******************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-checkbox/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   CheckboxIndicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Indicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Root: () => (/* binding */ Checkbox),\n/* harmony export */   createCheckboxScope: () => (/* binding */ createCheckboxScope),\n/* harmony export */   unstable_BubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxBubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxProvider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_CheckboxTrigger: () => (/* binding */ CheckboxTrigger),\n/* harmony export */   unstable_Provider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_Trigger: () => (/* binding */ CheckboxTrigger)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/../../node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/../../node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/../../node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/checkbox.tsx\n\n\n\n\n\n\n\n\n\n\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(CHECKBOX_NAME);\nvar [CheckboxProviderImpl, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nfunction CheckboxProvider(props) {\n  const {\n    __scopeCheckbox,\n    checked: checkedProp,\n    children,\n    defaultChecked,\n    disabled,\n    form,\n    name,\n    onCheckedChange,\n    required,\n    value = \"on\",\n    // @ts-expect-error\n    internal_do_not_use_render\n  } = props;\n  const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n    prop: checkedProp,\n    defaultProp: defaultChecked ?? false,\n    onChange: onCheckedChange,\n    caller: CHECKBOX_NAME\n  });\n  const [control, setControl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const [bubbleInput, setBubbleInput] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const isFormControl = control ? !!form || !!control.closest(\"form\") : (\n    // We set this to true by default so that events bubble to forms without JS (SSR)\n    true\n  );\n  const context = {\n    checked,\n    disabled,\n    setChecked,\n    control,\n    setControl,\n    name,\n    form,\n    value,\n    hasConsumerStoppedPropagationRef,\n    required,\n    defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n    isFormControl,\n    bubbleInput,\n    setBubbleInput\n  };\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    CheckboxProviderImpl,\n    {\n      scope: __scopeCheckbox,\n      ...context,\n      children: isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children\n    }\n  );\n}\nvar TRIGGER_NAME = \"CheckboxTrigger\";\nvar CheckboxTrigger = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  ({ __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }, forwardedRef) => {\n    const {\n      control,\n      value,\n      disabled,\n      checked,\n      required,\n      setControl,\n      setChecked,\n      hasConsumerStoppedPropagationRef,\n      isFormControl,\n      bubbleInput\n    } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setControl);\n    const initialCheckedStateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(checked);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const form = control?.form;\n      if (form) {\n        const reset = () => setChecked(initialCheckedStateRef.current);\n        form.addEventListener(\"reset\", reset);\n        return () => form.removeEventListener(\"reset\", reset);\n      }\n    }, [control, setChecked]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button,\n      {\n        type: \"button\",\n        role: \"checkbox\",\n        \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n        \"aria-required\": required,\n        \"data-state\": getState(checked),\n        \"data-disabled\": disabled ? \"\" : void 0,\n        disabled,\n        value,\n        ...checkboxProps,\n        ref: composedRefs,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onKeyDown, (event) => {\n          if (event.key === \"Enter\") event.preventDefault();\n        }),\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onClick, (event) => {\n          setChecked((prevChecked) => isIndeterminate(prevChecked) ? true : !prevChecked);\n          if (bubbleInput && isFormControl) {\n            hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n            if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n          }\n        })\n      }\n    );\n  }\n);\nCheckboxTrigger.displayName = TRIGGER_NAME;\nvar Checkbox = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeCheckbox,\n      name,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      value,\n      onCheckedChange,\n      form,\n      ...checkboxProps\n    } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      CheckboxProvider,\n      {\n        __scopeCheckbox,\n        checked,\n        defaultChecked,\n        disabled,\n        required,\n        onCheckedChange,\n        name,\n        form,\n        value,\n        internal_do_not_use_render: ({ isFormControl }) => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children: [\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            CheckboxTrigger,\n            {\n              ...checkboxProps,\n              ref: forwardedRef,\n              __scopeCheckbox\n            }\n          ),\n          isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            CheckboxBubbleInput,\n            {\n              __scopeCheckbox\n            }\n          )\n        ] })\n      }\n    );\n  }\n);\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence,\n      {\n        present: forceMount || isIndeterminate(context.checked) || context.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span,\n          {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: { pointerEvents: \"none\", ...props.style }\n          }\n        )\n      }\n    );\n  }\n);\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"CheckboxBubbleInput\";\nvar CheckboxBubbleInput = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  ({ __scopeCheckbox, ...props }, forwardedRef) => {\n    const {\n      control,\n      hasConsumerStoppedPropagationRef,\n      checked,\n      defaultChecked,\n      required,\n      disabled,\n      name,\n      value,\n      form,\n      bubbleInput,\n      setBubbleInput\n    } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setBubbleInput);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const input = bubbleInput;\n      if (!input) return;\n      const inputProto = window.HTMLInputElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        inputProto,\n        \"checked\"\n      );\n      const setChecked = descriptor.set;\n      const bubbles = !hasConsumerStoppedPropagationRef.current;\n      if (prevChecked !== checked && setChecked) {\n        const event = new Event(\"click\", { bubbles });\n        input.indeterminate = isIndeterminate(checked);\n        setChecked.call(input, isIndeterminate(checked) ? false : checked);\n        input.dispatchEvent(event);\n      }\n    }, [bubbleInput, prevChecked, checked, hasConsumerStoppedPropagationRef]);\n    const defaultCheckedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.input,\n      {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        required,\n        disabled,\n        name,\n        value,\n        form,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n          ...props.style,\n          ...controlSize,\n          position: \"absolute\",\n          pointerEvents: \"none\",\n          opacity: 0,\n          margin: 0,\n          // We transform because the input is absolutely positioned but we have\n          // rendered it **after** the button. This pulls it back to sit on top\n          // of the button.\n          transform: \"translateX(-100%)\"\n        }\n      }\n    );\n  }\n);\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\nfunction isIndeterminate(checked) {\n  return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n  return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-checkbox/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!********************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n  const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    { collectionRef: { current: null }, itemMap: /* @__PURE__ */ new Map() }\n  );\n  const CollectionProvider = (props) => {\n    const { scope, children } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, { scope, itemMap, collectionRef: ref, children });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n  const CollectionSlot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n  const CollectionItemSlot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n      const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        context.itemMap.set(ref, { ref, ...itemData });\n        return () => void context.itemMap.delete(ref);\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useCollection(scope) {\n    const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n    const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n      const collectionNode = context.collectionRef.current;\n      if (!collectionNode) return [];\n      const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n      const items = Array.from(context.itemMap.values());\n      const orderedItems = items.sort(\n        (a, b) => orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current)\n      );\n      return orderedItems;\n    }, [context.collectionRef, context.itemMap]);\n    return getItems;\n  }\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    useCollection,\n    createCollectionScope\n  ];\n}\n\n// src/collection.tsx\n\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n  #keys;\n  constructor(entries) {\n    super(entries);\n    this.#keys = [...super.keys()];\n    __instanciated.set(this, true);\n  }\n  set(key, value) {\n    if (__instanciated.get(this)) {\n      if (this.has(key)) {\n        this.#keys[this.#keys.indexOf(key)] = key;\n      } else {\n        this.#keys.push(key);\n      }\n    }\n    super.set(key, value);\n    return this;\n  }\n  insert(index, key, value) {\n    const has = this.has(key);\n    const length = this.#keys.length;\n    const relativeIndex = toSafeInteger(index);\n    let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n    if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n      this.set(key, value);\n      return this;\n    }\n    const size = this.size + (has ? 0 : 1);\n    if (relativeIndex < 0) {\n      actualIndex++;\n    }\n    const keys = [...this.#keys];\n    let nextValue;\n    let shouldSkip = false;\n    for (let i = actualIndex; i < size; i++) {\n      if (actualIndex === i) {\n        let nextKey = keys[i];\n        if (keys[i] === key) {\n          nextKey = keys[i + 1];\n        }\n        if (has) {\n          this.delete(key);\n        }\n        nextValue = this.get(nextKey);\n        this.set(key, value);\n      } else {\n        if (!shouldSkip && keys[i - 1] === key) {\n          shouldSkip = true;\n        }\n        const currentKey = keys[shouldSkip ? i : i - 1];\n        const currentValue = nextValue;\n        nextValue = this.get(currentKey);\n        this.delete(currentKey);\n        this.set(currentKey, currentValue);\n      }\n    }\n    return this;\n  }\n  with(index, key, value) {\n    const copy = new _OrderedDict(this);\n    copy.insert(index, key, value);\n    return copy;\n  }\n  before(key) {\n    const index = this.#keys.indexOf(key) - 1;\n    if (index < 0) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position before the given key.\n   */\n  setBefore(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index, newKey, value);\n  }\n  after(key) {\n    let index = this.#keys.indexOf(key);\n    index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n    if (index === -1) {\n      return void 0;\n    }\n    return this.entryAt(index);\n  }\n  /**\n   * Sets a new key-value pair at the position after the given key.\n   */\n  setAfter(key, newKey, value) {\n    const index = this.#keys.indexOf(key);\n    if (index === -1) {\n      return this;\n    }\n    return this.insert(index + 1, newKey, value);\n  }\n  first() {\n    return this.entryAt(0);\n  }\n  last() {\n    return this.entryAt(-1);\n  }\n  clear() {\n    this.#keys = [];\n    return super.clear();\n  }\n  delete(key) {\n    const deleted = super.delete(key);\n    if (deleted) {\n      this.#keys.splice(this.#keys.indexOf(key), 1);\n    }\n    return deleted;\n  }\n  deleteAt(index) {\n    const key = this.keyAt(index);\n    if (key !== void 0) {\n      return this.delete(key);\n    }\n    return false;\n  }\n  at(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return this.get(key);\n    }\n  }\n  entryAt(index) {\n    const key = at(this.#keys, index);\n    if (key !== void 0) {\n      return [key, this.get(key)];\n    }\n  }\n  indexOf(key) {\n    return this.#keys.indexOf(key);\n  }\n  keyAt(index) {\n    return at(this.#keys, index);\n  }\n  from(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.at(dest);\n  }\n  keyFrom(key, offset) {\n    const index = this.indexOf(key);\n    if (index === -1) {\n      return void 0;\n    }\n    let dest = index + offset;\n    if (dest < 0) dest = 0;\n    if (dest >= this.size) dest = this.size - 1;\n    return this.keyAt(dest);\n  }\n  find(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return entry;\n      }\n      index++;\n    }\n    return void 0;\n  }\n  findIndex(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return index;\n      }\n      index++;\n    }\n    return -1;\n  }\n  filter(predicate, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        entries.push(entry);\n      }\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  map(callbackfn, thisArg) {\n    const entries = [];\n    let index = 0;\n    for (const entry of this) {\n      entries.push([entry[0], Reflect.apply(callbackfn, thisArg, [entry, index, this])]);\n      index++;\n    }\n    return new _OrderedDict(entries);\n  }\n  reduce(...args) {\n    const [callbackfn, initialValue] = args;\n    let index = 0;\n    let accumulator = initialValue ?? this.at(0);\n    for (const entry of this) {\n      if (index === 0 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n      index++;\n    }\n    return accumulator;\n  }\n  reduceRight(...args) {\n    const [callbackfn, initialValue] = args;\n    let accumulator = initialValue ?? this.at(-1);\n    for (let index = this.size - 1; index >= 0; index--) {\n      const entry = this.at(index);\n      if (index === this.size - 1 && args.length === 1) {\n        accumulator = entry;\n      } else {\n        accumulator = Reflect.apply(callbackfn, this, [accumulator, entry, index, this]);\n      }\n    }\n    return accumulator;\n  }\n  toSorted(compareFn) {\n    const entries = [...this.entries()].sort(compareFn);\n    return new _OrderedDict(entries);\n  }\n  toReversed() {\n    const reversed = new _OrderedDict();\n    for (let index = this.size - 1; index >= 0; index--) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      reversed.set(key, element);\n    }\n    return reversed;\n  }\n  toSpliced(...args) {\n    const entries = [...this.entries()];\n    entries.splice(...args);\n    return new _OrderedDict(entries);\n  }\n  slice(start, end) {\n    const result = new _OrderedDict();\n    let stop = this.size - 1;\n    if (start === void 0) {\n      return result;\n    }\n    if (start < 0) {\n      start = start + this.size;\n    }\n    if (end !== void 0 && end > 0) {\n      stop = end - 1;\n    }\n    for (let index = start; index <= stop; index++) {\n      const key = this.keyAt(index);\n      const element = this.get(key);\n      result.set(key, element);\n    }\n    return result;\n  }\n  every(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (!Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return false;\n      }\n      index++;\n    }\n    return true;\n  }\n  some(predicate, thisArg) {\n    let index = 0;\n    for (const entry of this) {\n      if (Reflect.apply(predicate, thisArg, [entry, index, this])) {\n        return true;\n      }\n      index++;\n    }\n    return false;\n  }\n};\nfunction at(array, index) {\n  if (\"at\" in Array.prototype) {\n    return Array.prototype.at.call(array, index);\n  }\n  const actualIndex = toSafeIndex(array, index);\n  return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n  const length = array.length;\n  const relativeIndex = toSafeInteger(index);\n  const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n  return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n  return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n\n// src/collection.tsx\n\nfunction createCollection2(name) {\n  const PROVIDER_NAME = name + \"CollectionProvider\";\n  const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n  const [CollectionContextProvider, useCollectionContext] = createCollectionContext(\n    PROVIDER_NAME,\n    {\n      collectionElement: null,\n      collectionRef: { current: null },\n      collectionRefObject: { current: null },\n      itemMap: new OrderedDict(),\n      setItemMap: () => void 0\n    }\n  );\n  const CollectionProvider = ({ state, ...props }) => {\n    return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, { ...props, state }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, { ...props });\n  };\n  CollectionProvider.displayName = PROVIDER_NAME;\n  const CollectionInit = (props) => {\n    const state = useInitCollection();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, { ...props, state });\n  };\n  CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n  const CollectionProviderImpl = (props) => {\n    const { scope, children, state } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\n      null\n    );\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n    const [itemMap, setItemMap] = state;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (!collectionElement) return;\n      const observer = getChildListObserver(() => {\n      });\n      observer.observe(collectionElement, {\n        childList: true,\n        subtree: true\n      });\n      return () => {\n        observer.disconnect();\n      };\n    }, [collectionElement]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      CollectionContextProvider,\n      {\n        scope,\n        itemMap,\n        setItemMap,\n        collectionRef: composeRefs,\n        collectionRefObject: ref,\n        collectionElement,\n        children\n      }\n    );\n  };\n  CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n  const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n  const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n  const CollectionSlot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children } = props;\n      const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n      const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, { ref: composedRefs, children });\n    }\n  );\n  CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n  const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n  const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n  const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n  const CollectionItemSlot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n    (props, forwardedRef) => {\n      const { scope, children, ...itemData } = props;\n      const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n      const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n      const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n      const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n      const { setItemMap } = context;\n      const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n      if (!shallowEqual(itemDataRef.current, itemData)) {\n        itemDataRef.current = itemData;\n      }\n      const memoizedItemData = itemDataRef.current;\n      react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        const itemData2 = memoizedItemData;\n        setItemMap((map) => {\n          if (!element) {\n            return map;\n          }\n          if (!map.has(element)) {\n            map.set(element, { ...itemData2, element });\n            return map.toSorted(sortByDocumentPosition);\n          }\n          return map.set(element, { ...itemData2, element }).toSorted(sortByDocumentPosition);\n        });\n        return () => {\n          setItemMap((map) => {\n            if (!element || !map.has(element)) {\n              return map;\n            }\n            map.delete(element);\n            return new OrderedDict(map);\n          });\n        };\n      }, [element, memoizedItemData, setItemMap]);\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, { ...{ [ITEM_DATA_ATTR]: \"\" }, ref: composedRefs, children });\n    }\n  );\n  CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n  function useInitCollection() {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n  }\n  function useCollection(scope) {\n    const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n    return itemMap;\n  }\n  const functions = {\n    createCollectionScope,\n    useCollection,\n    useInitCollection\n  };\n  return [\n    { Provider: CollectionProvider, Slot: CollectionSlot, ItemSlot: CollectionItemSlot },\n    functions\n  ];\n}\nfunction shallowEqual(a, b) {\n  if (a === b) return true;\n  if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n  if (a == null || b == null) return false;\n  const keysA = Object.keys(a);\n  const keysB = Object.keys(b);\n  if (keysA.length !== keysB.length) return false;\n  for (const key of keysA) {\n    if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n    if (a[key] !== b[key]) return false;\n  }\n  return true;\n}\nfunction isElementPreceding(a, b) {\n  return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n  return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n  const observer = new MutationObserver((mutationsList) => {\n    for (const mutation of mutationsList) {\n      if (mutation.type === \"childList\") {\n        callback();\n        return;\n      }\n    }\n  });\n  return observer;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/compose-refs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!*******************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1kaXJlY3Rpb24vZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBQTtBQUMrQjtBQUNTO0FBQ3hDLHVCQUF1QixnREFBbUI7QUFDMUM7QUFDQSxVQUFVLGdCQUFnQjtBQUMxQix5QkFBeUIsc0RBQUcsOEJBQThCLHNCQUFzQjtBQUNoRjtBQUNBO0FBQ0Esb0JBQW9CLDZDQUFnQjtBQUNwQztBQUNBO0FBQ0E7QUFLRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8uLi8uLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcz9kYTBkIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2RpcmVjdGlvbi9zcmMvZGlyZWN0aW9uLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBEaXJlY3Rpb25Db250ZXh0ID0gUmVhY3QuY3JlYXRlQ29udGV4dCh2b2lkIDApO1xudmFyIERpcmVjdGlvblByb3ZpZGVyID0gKHByb3BzKSA9PiB7XG4gIGNvbnN0IHsgZGlyLCBjaGlsZHJlbiB9ID0gcHJvcHM7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8ganN4KERpcmVjdGlvbkNvbnRleHQuUHJvdmlkZXIsIHsgdmFsdWU6IGRpciwgY2hpbGRyZW4gfSk7XG59O1xuZnVuY3Rpb24gdXNlRGlyZWN0aW9uKGxvY2FsRGlyKSB7XG4gIGNvbnN0IGdsb2JhbERpciA9IFJlYWN0LnVzZUNvbnRleHQoRGlyZWN0aW9uQ29udGV4dCk7XG4gIHJldHVybiBsb2NhbERpciB8fCBnbG9iYWxEaXIgfHwgXCJsdHJcIjtcbn1cbnZhciBQcm92aWRlciA9IERpcmVjdGlvblByb3ZpZGVyO1xuZXhwb3J0IHtcbiAgRGlyZWN0aW9uUHJvdmlkZXIsXG4gIFByb3ZpZGVyLFxuICB1c2VEaXJlY3Rpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n  layers: /* @__PURE__ */ new Set(),\n  layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n  branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      disableOutsidePointerEvents = false,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      onFocusOutside,\n      onInteractOutside,\n      onDismiss,\n      ...layerProps\n    } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2) => setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [...context.layersWithOutsidePointerEventsDisabled].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event) => {\n      const target = event.target;\n      const isPointerDownOnBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n      onPointerDownOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event) => {\n      const target = event.target;\n      const isFocusInBranch = [...context.branches].some((branch) => branch.contains(target));\n      if (isFocusInBranch) return;\n      onFocusOutside?.(event);\n      onInteractOutside?.(event);\n      if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event) => {\n      const isHighestLayer = index === context.layers.size - 1;\n      if (!isHighestLayer) return;\n      onEscapeKeyDown?.(event);\n      if (!event.defaultPrevented && onDismiss) {\n        event.preventDefault();\n        onDismiss();\n      }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (!node) return;\n      if (disableOutsidePointerEvents) {\n        if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n          originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n          ownerDocument.body.style.pointerEvents = \"none\";\n        }\n        context.layersWithOutsidePointerEventsDisabled.add(node);\n      }\n      context.layers.add(node);\n      dispatchUpdate();\n      return () => {\n        if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n          ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n        }\n      };\n    }, [node, ownerDocument, disableOutsidePointerEvents, context]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      return () => {\n        if (!node) return;\n        context.layers.delete(node);\n        context.layersWithOutsidePointerEventsDisabled.delete(node);\n        dispatchUpdate();\n      };\n    }, [node, context]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const handleUpdate = () => force({});\n      document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n      return () => document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div,\n      {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n          pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n          ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(\n          props.onPointerDownCapture,\n          pointerDownOutside.onPointerDownCapture\n        )\n      }\n    );\n  }\n);\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      context.branches.add(node);\n      return () => {\n        context.branches.delete(node);\n      };\n    }\n  }, [context.branches]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, { ...props, ref: composedRefs });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n  const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n  const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n  });\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handlePointerDown = (event) => {\n      if (event.target && !isPointerInsideReactTreeRef.current) {\n        let handleAndDispatchPointerDownOutsideEvent2 = function() {\n          handleAndDispatchCustomEvent(\n            POINTER_DOWN_OUTSIDE,\n            handlePointerDownOutside,\n            eventDetail,\n            { discrete: true }\n          );\n        };\n        var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n        const eventDetail = { originalEvent: event };\n        if (event.pointerType === \"touch\") {\n          ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n          handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n          ownerDocument.addEventListener(\"click\", handleClickRef.current, { once: true });\n        } else {\n          handleAndDispatchPointerDownOutsideEvent2();\n        }\n      } else {\n        ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n      }\n      isPointerInsideReactTreeRef.current = false;\n    };\n    const timerId = window.setTimeout(() => {\n      ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n    }, 0);\n    return () => {\n      window.clearTimeout(timerId);\n      ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n      ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n    };\n  }, [ownerDocument, handlePointerDownOutside]);\n  return {\n    // ensures we check React component tree (not just DOM tree)\n    onPointerDownCapture: () => isPointerInsideReactTreeRef.current = true\n  };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n  const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n  const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleFocus = (event) => {\n      if (event.target && !isFocusInsideReactTreeRef.current) {\n        const eventDetail = { originalEvent: event };\n        handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n          discrete: false\n        });\n      }\n    };\n    ownerDocument.addEventListener(\"focusin\", handleFocus);\n    return () => ownerDocument.removeEventListener(\"focusin\", handleFocus);\n  }, [ownerDocument, handleFocusOutside]);\n  return {\n    onFocusCapture: () => isFocusInsideReactTreeRef.current = true,\n    onBlurCapture: () => isFocusInsideReactTreeRef.current = false\n  };\n}\nfunction dispatchUpdate() {\n  const event = new CustomEvent(CONTEXT_UPDATE);\n  document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n  const target = detail.originalEvent.target;\n  const event = new CustomEvent(name, { bubbles: false, cancelable: true, detail });\n  if (handler) target.addEventListener(name, handler, { once: true });\n  if (discrete) {\n    (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n  } else {\n    target.dispatchEvent(event);\n  }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\"use client\";\n\n// packages/react/focus-guards/src/focus-guards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n  useFocusGuards();\n  return props.children;\n}\nfunction useFocusGuards() {\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n    document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n    document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n    count++;\n    return () => {\n      if (count === 1) {\n        document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node) => node.remove());\n      }\n      count--;\n    };\n  }, []);\n}\nfunction createFocusGuard() {\n  const element = document.createElement(\"span\");\n  element.setAttribute(\"data-radix-focus-guard\", \"\");\n  element.tabIndex = 0;\n  element.style.outline = \"none\";\n  element.style.opacity = \"0\";\n  element.style.position = \"fixed\";\n  element.style.pointerEvents = \"none\";\n  return element;\n}\nvar Root = FocusGuards;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*********************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/focus-scope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const {\n    loop = false,\n    trapped = false,\n    onMountAutoFocus: onMountAutoFocusProp,\n    onUnmountAutoFocus: onUnmountAutoFocusProp,\n    ...scopeProps\n  } = props;\n  const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n  const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n  const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node) => setContainer(node));\n  const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n    paused: false,\n    pause() {\n      this.paused = true;\n    },\n    resume() {\n      this.paused = false;\n    }\n  }).current;\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (trapped) {\n      let handleFocusIn2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const target = event.target;\n        if (container.contains(target)) {\n          lastFocusedElementRef.current = target;\n        } else {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleFocusOut2 = function(event) {\n        if (focusScope.paused || !container) return;\n        const relatedTarget = event.relatedTarget;\n        if (relatedTarget === null) return;\n        if (!container.contains(relatedTarget)) {\n          focus(lastFocusedElementRef.current, { select: true });\n        }\n      }, handleMutations2 = function(mutations) {\n        const focusedElement = document.activeElement;\n        if (focusedElement !== document.body) return;\n        for (const mutation of mutations) {\n          if (mutation.removedNodes.length > 0) focus(container);\n        }\n      };\n      var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n      document.addEventListener(\"focusin\", handleFocusIn2);\n      document.addEventListener(\"focusout\", handleFocusOut2);\n      const mutationObserver = new MutationObserver(handleMutations2);\n      if (container) mutationObserver.observe(container, { childList: true, subtree: true });\n      return () => {\n        document.removeEventListener(\"focusin\", handleFocusIn2);\n        document.removeEventListener(\"focusout\", handleFocusOut2);\n        mutationObserver.disconnect();\n      };\n    }\n  }, [trapped, container, focusScope.paused]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (container) {\n      focusScopesStack.add(focusScope);\n      const previouslyFocusedElement = document.activeElement;\n      const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n      if (!hasFocusedCandidate) {\n        const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n        container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        container.dispatchEvent(mountEvent);\n        if (!mountEvent.defaultPrevented) {\n          focusFirst(removeLinks(getTabbableCandidates(container)), { select: true });\n          if (document.activeElement === previouslyFocusedElement) {\n            focus(container);\n          }\n        }\n      }\n      return () => {\n        container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n        setTimeout(() => {\n          const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n          container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          container.dispatchEvent(unmountEvent);\n          if (!unmountEvent.defaultPrevented) {\n            focus(previouslyFocusedElement ?? document.body, { select: true });\n          }\n          container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n          focusScopesStack.remove(focusScope);\n        }, 0);\n      };\n    }\n  }, [container, onMountAutoFocus, onUnmountAutoFocus, focusScope]);\n  const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (event) => {\n      if (!loop && !trapped) return;\n      if (focusScope.paused) return;\n      const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n      const focusedElement = document.activeElement;\n      if (isTabKey && focusedElement) {\n        const container2 = event.currentTarget;\n        const [first, last] = getTabbableEdges(container2);\n        const hasTabbableElementsInside = first && last;\n        if (!hasTabbableElementsInside) {\n          if (focusedElement === container2) event.preventDefault();\n        } else {\n          if (!event.shiftKey && focusedElement === last) {\n            event.preventDefault();\n            if (loop) focus(first, { select: true });\n          } else if (event.shiftKey && focusedElement === first) {\n            event.preventDefault();\n            if (loop) focus(last, { select: true });\n          }\n        }\n      }\n    },\n    [loop, trapped, focusScope.paused]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, { tabIndex: -1, ...scopeProps, ref: composedRefs, onKeyDown: handleKeyDown });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n  const previouslyFocusedElement = document.activeElement;\n  for (const candidate of candidates) {\n    focus(candidate, { select });\n    if (document.activeElement !== previouslyFocusedElement) return;\n  }\n}\nfunction getTabbableEdges(container) {\n  const candidates = getTabbableCandidates(container);\n  const first = findVisible(candidates, container);\n  const last = findVisible(candidates.reverse(), container);\n  return [first, last];\n}\nfunction getTabbableCandidates(container) {\n  const nodes = [];\n  const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (node) => {\n      const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n      if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n      return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n    }\n  });\n  while (walker.nextNode()) nodes.push(walker.currentNode);\n  return nodes;\n}\nfunction findVisible(elements, container) {\n  for (const element of elements) {\n    if (!isHidden(element, { upTo: container })) return element;\n  }\n}\nfunction isHidden(node, { upTo }) {\n  if (getComputedStyle(node).visibility === \"hidden\") return true;\n  while (node) {\n    if (upTo !== void 0 && node === upTo) return false;\n    if (getComputedStyle(node).display === \"none\") return true;\n    node = node.parentElement;\n  }\n  return false;\n}\nfunction isSelectableInput(element) {\n  return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n  if (element && element.focus) {\n    const previouslyFocusedElement = document.activeElement;\n    element.focus({ preventScroll: true });\n    if (element !== previouslyFocusedElement && isSelectableInput(element) && select)\n      element.select();\n  }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n  let stack = [];\n  return {\n    add(focusScope) {\n      const activeFocusScope = stack[0];\n      if (focusScope !== activeFocusScope) {\n        activeFocusScope?.pause();\n      }\n      stack = arrayRemove(stack, focusScope);\n      stack.unshift(focusScope);\n    },\n    remove(focusScope) {\n      stack = arrayRemove(stack, focusScope);\n      stack[0]?.resume();\n    }\n  };\n}\nfunction arrayRemove(array, item) {\n  const updatedArray = [...array];\n  const index = updatedArray.indexOf(item);\n  if (index !== -1) {\n    updatedArray.splice(index, 1);\n  }\n  return updatedArray;\n}\nfunction removeLinks(items) {\n  return items.filter((item) => item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-id/dist/index.mjs":
/*!************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-id/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useId: () => (/* binding */ useId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/id/src/id.tsx\n\n\nvar useReactId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useId \".trim().toString()] || (() => void 0);\nvar count = 0;\nfunction useId(deterministicId) {\n  const [id, setId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(useReactId());\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (!deterministicId) setId((reactId) => reactId ?? String(count++));\n  }, [deterministicId]);\n  return deterministicId || (id ? `radix-${id}` : \"\");\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1pZC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDK0I7QUFDcUM7QUFDcEUsaUJBQWlCLHlMQUFLO0FBQ3RCO0FBQ0E7QUFDQSxzQkFBc0IsMkNBQWM7QUFDcEMsRUFBRSxrRkFBZTtBQUNqQjtBQUNBLEdBQUc7QUFDSCwyQ0FBMkMsR0FBRztBQUM5QztBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2FsaS9jbGllbnQtd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtaWQvZGlzdC9pbmRleC5tanM/MjBiYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9pZC9zcmMvaWQudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHVzZUxheW91dEVmZmVjdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtdXNlLWxheW91dC1lZmZlY3RcIjtcbnZhciB1c2VSZWFjdElkID0gUmVhY3RbXCIgdXNlSWQgXCIudHJpbSgpLnRvU3RyaW5nKCldIHx8ICgoKSA9PiB2b2lkIDApO1xudmFyIGNvdW50ID0gMDtcbmZ1bmN0aW9uIHVzZUlkKGRldGVybWluaXN0aWNJZCkge1xuICBjb25zdCBbaWQsIHNldElkXSA9IFJlYWN0LnVzZVN0YXRlKHVzZVJlYWN0SWQoKSk7XG4gIHVzZUxheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFkZXRlcm1pbmlzdGljSWQpIHNldElkKChyZWFjdElkKSA9PiByZWFjdElkID8/IFN0cmluZyhjb3VudCsrKSk7XG4gIH0sIFtkZXRlcm1pbmlzdGljSWRdKTtcbiAgcmV0dXJuIGRldGVybWluaXN0aWNJZCB8fCAoaWQgPyBgcmFkaXgtJHtpZH1gIDogXCJcIik7XG59XG5leHBvcnQge1xuICB1c2VJZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-id/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-label/dist/index.mjs":
/*!***************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-label/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/label.tsx\n\n\n\nvar NAME = \"Label\";\nvar Label = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.label,\n    {\n      ...props,\n      ref: forwardedRef,\n      onMouseDown: (event) => {\n        const target = event.target;\n        if (target.closest(\"button, input, select, textarea\")) return;\n        props.onMouseDown?.(event);\n        if (!event.defaultPrevented && event.detail > 1) event.preventDefault();\n      }\n    }\n  );\n});\nLabel.displayName = NAME;\nvar Root = Label;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1sYWJlbC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBOztBQUVBO0FBQytCO0FBQ3VCO0FBQ2Q7QUFDeEM7QUFDQSxZQUFZLDZDQUFnQjtBQUM1Qix5QkFBeUIsc0RBQUc7QUFDNUIsSUFBSSxnRUFBUztBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2FsaS9jbGllbnQtd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtbGFiZWwvZGlzdC9pbmRleC5tanM/YjFiZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuLy8gc3JjL2xhYmVsLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgTkFNRSA9IFwiTGFiZWxcIjtcbnZhciBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgUHJpbWl0aXZlLmxhYmVsLFxuICAgIHtcbiAgICAgIC4uLnByb3BzLFxuICAgICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgICBvbk1vdXNlRG93bjogKGV2ZW50KSA9PiB7XG4gICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldDtcbiAgICAgICAgaWYgKHRhcmdldC5jbG9zZXN0KFwiYnV0dG9uLCBpbnB1dCwgc2VsZWN0LCB0ZXh0YXJlYVwiKSkgcmV0dXJuO1xuICAgICAgICBwcm9wcy5vbk1vdXNlRG93bj8uKGV2ZW50KTtcbiAgICAgICAgaWYgKCFldmVudC5kZWZhdWx0UHJldmVudGVkICYmIGV2ZW50LmRldGFpbCA+IDEpIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICB9XG4gICAgfVxuICApO1xufSk7XG5MYWJlbC5kaXNwbGF5TmFtZSA9IE5BTUU7XG52YXIgUm9vdCA9IExhYmVsO1xuZXhwb3J0IHtcbiAgTGFiZWwsXG4gIFJvb3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-label/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!****************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/../../node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/../../node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/../../node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\"top\", \"right\", \"bottom\", \"left\"];\nvar ALIGN_OPTIONS = [\"start\", \"center\", \"end\"];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props) => {\n  const { __scopePopper, children } = props;\n  const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, { scope: __scopePopper, anchor, onAnchorChange: setAnchor, children });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, { ...anchorProps, ref: composedRefs });\n  }\n);\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopePopper,\n      side = \"bottom\",\n      sideOffset = 0,\n      align = \"center\",\n      alignOffset = 0,\n      arrowPadding = 0,\n      avoidCollisions = true,\n      collisionBoundary = [],\n      collisionPadding: collisionPaddingProp = 0,\n      sticky = \"partial\",\n      hideWhenDetached = false,\n      updatePositionStrategy = \"optimized\",\n      onPlaced,\n      ...contentProps\n    } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node) => setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : { top: 0, right: 0, bottom: 0, left: 0, ...collisionPaddingProp };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [collisionBoundary];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n      padding: collisionPadding,\n      boundary: boundary.filter(isNotNull),\n      // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n      altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n      // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n      strategy: \"fixed\",\n      placement: desiredPlacement,\n      whileElementsMounted: (...args) => {\n        const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n          animationFrame: updatePositionStrategy === \"always\"\n        });\n        return cleanup;\n      },\n      elements: {\n        reference: context.anchor\n      },\n      middleware: [\n        (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({ mainAxis: sideOffset + arrowHeight, alignmentAxis: alignOffset }),\n        avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n          mainAxis: true,\n          crossAxis: false,\n          limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n          ...detectOverflowOptions\n        }),\n        avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({ ...detectOverflowOptions }),\n        (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n          ...detectOverflowOptions,\n          apply: ({ elements, rects, availableWidth, availableHeight }) => {\n            const { width: anchorWidth, height: anchorHeight } = rects.reference;\n            const contentStyle = elements.floating.style;\n            contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n            contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n          }\n        }),\n        arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({ element: arrow, padding: arrowPadding }),\n        transformOrigin({ arrowWidth, arrowHeight }),\n        hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({ strategy: \"referenceHidden\", ...detectOverflowOptions })\n      ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(() => {\n      if (isPositioned) {\n        handlePlaced?.();\n      }\n    }, [isPositioned, handlePlaced]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(() => {\n      if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [content]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"div\",\n      {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n          ...floatingStyles,\n          transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n          // keep off the page when measuring\n          minWidth: \"max-content\",\n          zIndex: contentZIndex,\n          [\"--radix-popper-transform-origin\"]: [\n            middlewareData.transformOrigin?.x,\n            middlewareData.transformOrigin?.y\n          ].join(\" \"),\n          // hide the content if using the hide middleware and should be hidden\n          // set visibility to hidden and disable pointer events so the UI behaves\n          // as if the PopperContent isn't there at all\n          ...middlewareData.hide?.referenceHidden && {\n            visibility: \"hidden\",\n            pointerEvents: \"none\"\n          }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          PopperContentProvider,\n          {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n              _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div,\n              {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                  ...contentProps.style,\n                  // if the PopperContent hasn't been placed yet (not all measurements done)\n                  // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                  animation: !isPositioned ? \"none\" : void 0\n                }\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n  top: \"bottom\",\n  right: \"left\",\n  bottom: \"top\",\n  left: \"right\"\n};\nvar PopperArrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n  const { __scopePopper, ...arrowProps } = props;\n  const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n  const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n  return (\n    // we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      \"span\",\n      {\n        ref: contentContext.onArrowChange,\n        style: {\n          position: \"absolute\",\n          left: contentContext.arrowX,\n          top: contentContext.arrowY,\n          [baseSide]: 0,\n          transformOrigin: {\n            top: \"\",\n            right: \"0 0\",\n            bottom: \"center 0\",\n            left: \"100% 0\"\n          }[contentContext.placedSide],\n          transform: {\n            top: \"translateY(100%)\",\n            right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n            bottom: `rotate(180deg)`,\n            left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n          }[contentContext.placedSide],\n          visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root,\n          {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n              ...arrowProps.style,\n              // ensures the element can be measured correctly (mostly for if SVG)\n              display: \"block\"\n            }\n          }\n        )\n      }\n    )\n  );\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n  return value !== null;\n}\nvar transformOrigin = (options) => ({\n  name: \"transformOrigin\",\n  options,\n  fn(data) {\n    const { placement, rects, middlewareData } = data;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const isArrowHidden = cannotCenterArrow;\n    const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n    const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const noArrowAlign = { start: \"0%\", center: \"50%\", end: \"100%\" }[placedAlign];\n    const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n    const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n    let x = \"\";\n    let y = \"\";\n    if (placedSide === \"bottom\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${-arrowHeight}px`;\n    } else if (placedSide === \"top\") {\n      x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n      y = `${rects.floating.height + arrowHeight}px`;\n    } else if (placedSide === \"right\") {\n      x = `${-arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    } else if (placedSide === \"left\") {\n      x = `${rects.floating.width + arrowHeight}px`;\n      y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n    }\n    return { data: { x, y } };\n  }\n});\nfunction getSideAndAlignFromPlacement(placement) {\n  const [side, align = \"center\"] = placement.split(\"-\");\n  return [side, align];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!****************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(() => setMounted(true), []);\n  const container = containerProp || mounted && globalThis?.document?.body;\n  return container ? react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, { ...portalProps, ref: forwardedRef }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1wb3J0YWwvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOztBQUVBO0FBQytCO0FBQ0U7QUFDcUI7QUFDYztBQUM1QjtBQUN4QztBQUNBLGFBQWEsNkNBQWdCO0FBQzdCLFVBQVUsMkNBQTJDO0FBQ3JELGdDQUFnQywyQ0FBYztBQUM5QyxFQUFFLGtGQUFlO0FBQ2pCO0FBQ0EscUJBQXFCLG1EQUFxQixpQkFBaUIsc0RBQUcsQ0FBQyxnRUFBUyxRQUFRLG1DQUFtQztBQUNuSCxDQUFDO0FBQ0Q7QUFDQTtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2FsaS9jbGllbnQtd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtcG9ydGFsL2Rpc3QvaW5kZXgubWpzP2NlMTUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbi8vIHNyYy9wb3J0YWwudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCBSZWFjdERPTSBmcm9tIFwicmVhY3QtZG9tXCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgUE9SVEFMX05BTUUgPSBcIlBvcnRhbFwiO1xudmFyIFBvcnRhbCA9IFJlYWN0LmZvcndhcmRSZWYoKHByb3BzLCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgY29uc3QgeyBjb250YWluZXI6IGNvbnRhaW5lclByb3AsIC4uLnBvcnRhbFByb3BzIH0gPSBwcm9wcztcbiAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gUmVhY3QudXNlU3RhdGUoZmFsc2UpO1xuICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4gc2V0TW91bnRlZCh0cnVlKSwgW10pO1xuICBjb25zdCBjb250YWluZXIgPSBjb250YWluZXJQcm9wIHx8IG1vdW50ZWQgJiYgZ2xvYmFsVGhpcz8uZG9jdW1lbnQ/LmJvZHk7XG4gIHJldHVybiBjb250YWluZXIgPyBSZWFjdERPTS5jcmVhdGVQb3J0YWwoLyogQF9fUFVSRV9fICovIGpzeChQcmltaXRpdmUuZGl2LCB7IC4uLnBvcnRhbFByb3BzLCByZWY6IGZvcndhcmRlZFJlZiB9KSwgY29udGFpbmVyKSA6IG51bGw7XG59KTtcblBvcnRhbC5kaXNwbGF5TmFtZSA9IFBPUlRBTF9OQU1FO1xudmFyIFJvb3QgPSBQb3J0YWw7XG5leHBvcnQge1xuICBQb3J0YWwsXG4gIFJvb3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!******************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n\"use client\";\n\n// src/presence.tsx\n\n\n\n\n// src/use-state-machine.tsx\n\nfunction useStateMachine(initialState, machine) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event) => {\n    const nextState = machine[state][event];\n    return nextState ?? state;\n  }, initialState);\n}\n\n// src/presence.tsx\nvar Presence = (props) => {\n  const { present, children } = props;\n  const presence = usePresence(present);\n  const child = typeof children === \"function\" ? children({ present: presence.isPresent }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n  const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n  const forceMount = typeof children === \"function\";\n  return forceMount || presence.isPresent ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, { ref }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n  const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n  const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n  const initialState = present ? \"mounted\" : \"unmounted\";\n  const [state, send] = useStateMachine(initialState, {\n    mounted: {\n      UNMOUNT: \"unmounted\",\n      ANIMATION_OUT: \"unmountSuspended\"\n    },\n    unmountSuspended: {\n      MOUNT: \"mounted\",\n      ANIMATION_END: \"unmounted\"\n    },\n    unmounted: {\n      MOUNT: \"mounted\"\n    }\n  });\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const currentAnimationName = getAnimationName(stylesRef.current);\n    prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n  }, [state]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(() => {\n    const styles = stylesRef.current;\n    const wasPresent = prevPresentRef.current;\n    const hasPresentChanged = wasPresent !== present;\n    if (hasPresentChanged) {\n      const prevAnimationName = prevAnimationNameRef.current;\n      const currentAnimationName = getAnimationName(styles);\n      if (present) {\n        send(\"MOUNT\");\n      } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n        send(\"UNMOUNT\");\n      } else {\n        const isAnimating = prevAnimationName !== currentAnimationName;\n        if (wasPresent && isAnimating) {\n          send(\"ANIMATION_OUT\");\n        } else {\n          send(\"UNMOUNT\");\n        }\n      }\n      prevPresentRef.current = present;\n    }\n  }, [present, send]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(() => {\n    if (node) {\n      let timeoutId;\n      const ownerWindow = node.ownerDocument.defaultView ?? window;\n      const handleAnimationEnd = (event) => {\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n        if (event.target === node && isCurrentAnimation) {\n          send(\"ANIMATION_END\");\n          if (!prevPresentRef.current) {\n            const currentFillMode = node.style.animationFillMode;\n            node.style.animationFillMode = \"forwards\";\n            timeoutId = ownerWindow.setTimeout(() => {\n              if (node.style.animationFillMode === \"forwards\") {\n                node.style.animationFillMode = currentFillMode;\n              }\n            });\n          }\n        }\n      };\n      const handleAnimationStart = (event) => {\n        if (event.target === node) {\n          prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n        }\n      };\n      node.addEventListener(\"animationstart\", handleAnimationStart);\n      node.addEventListener(\"animationcancel\", handleAnimationEnd);\n      node.addEventListener(\"animationend\", handleAnimationEnd);\n      return () => {\n        ownerWindow.clearTimeout(timeoutId);\n        node.removeEventListener(\"animationstart\", handleAnimationStart);\n        node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n        node.removeEventListener(\"animationend\", handleAnimationEnd);\n      };\n    } else {\n      send(\"ANIMATION_END\");\n    }\n  }, [node, send]);\n  return {\n    isPresent: [\"mounted\", \"unmountSuspended\"].includes(state),\n    ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2) => {\n      stylesRef.current = node2 ? getComputedStyle(node2) : null;\n      setNode(node2);\n    }, [])\n  };\n}\nfunction getAnimationName(styles) {\n  return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Presence;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!*******************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"select\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(`Primitive.${node}`);\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1wcmltaXRpdmUvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBO0FBQytCO0FBQ087QUFDWTtBQUNWO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLGdFQUFVLGNBQWMsS0FBSztBQUM1QyxlQUFlLDZDQUFnQjtBQUMvQixZQUFZLDZCQUE2QjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQixzREFBRyxTQUFTLHNDQUFzQztBQUM3RSxHQUFHO0FBQ0gsa0NBQWtDLEtBQUs7QUFDdkMsV0FBVztBQUNYLENBQUMsSUFBSTtBQUNMO0FBQ0EsY0FBYyxnREFBa0I7QUFDaEM7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2FsaS9jbGllbnQtd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzPzhlZWQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3ByaW1pdGl2ZS50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0ICogYXMgUmVhY3RET00gZnJvbSBcInJlYWN0LWRvbVwiO1xuaW1wb3J0IHsgY3JlYXRlU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgTk9ERVMgPSBbXG4gIFwiYVwiLFxuICBcImJ1dHRvblwiLFxuICBcImRpdlwiLFxuICBcImZvcm1cIixcbiAgXCJoMlwiLFxuICBcImgzXCIsXG4gIFwiaW1nXCIsXG4gIFwiaW5wdXRcIixcbiAgXCJsYWJlbFwiLFxuICBcImxpXCIsXG4gIFwibmF2XCIsXG4gIFwib2xcIixcbiAgXCJwXCIsXG4gIFwic2VsZWN0XCIsXG4gIFwic3BhblwiLFxuICBcInN2Z1wiLFxuICBcInVsXCJcbl07XG52YXIgUHJpbWl0aXZlID0gTk9ERVMucmVkdWNlKChwcmltaXRpdmUsIG5vZGUpID0+IHtcbiAgY29uc3QgU2xvdCA9IGNyZWF0ZVNsb3QoYFByaW1pdGl2ZS4ke25vZGV9YCk7XG4gIGNvbnN0IE5vZGUgPSBSZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBhc0NoaWxkLCAuLi5wcmltaXRpdmVQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgQ29tcCA9IGFzQ2hpbGQgPyBTbG90IDogbm9kZTtcbiAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgd2luZG93W1N5bWJvbC5mb3IoXCJyYWRpeC11aVwiKV0gPSB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChDb21wLCB7IC4uLnByaW1pdGl2ZVByb3BzLCByZWY6IGZvcndhcmRlZFJlZiB9KTtcbiAgfSk7XG4gIE5vZGUuZGlzcGxheU5hbWUgPSBgUHJpbWl0aXZlLiR7bm9kZX1gO1xuICByZXR1cm4geyAuLi5wcmltaXRpdmUsIFtub2RlXTogTm9kZSB9O1xufSwge30pO1xuZnVuY3Rpb24gZGlzcGF0Y2hEaXNjcmV0ZUN1c3RvbUV2ZW50KHRhcmdldCwgZXZlbnQpIHtcbiAgaWYgKHRhcmdldCkgUmVhY3RET00uZmx1c2hTeW5jKCgpID0+IHRhcmdldC5kaXNwYXRjaEV2ZW50KGV2ZW50KSk7XG59XG52YXIgUm9vdCA9IFByaW1pdGl2ZTtcbmV4cG9ydCB7XG4gIFByaW1pdGl2ZSxcbiAgUm9vdCxcbiAgZGlzcGF0Y2hEaXNjcmV0ZUN1c3RvbUV2ZW50XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-progress/dist/index.mjs":
/*!******************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-progress/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Indicator: () => (/* binding */ Indicator),\n/* harmony export */   Progress: () => (/* binding */ Progress),\n/* harmony export */   ProgressIndicator: () => (/* binding */ ProgressIndicator),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createProgressScope: () => (/* binding */ createProgressScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/progress.tsx\n\n\n\n\nvar PROGRESS_NAME = \"Progress\";\nvar DEFAULT_MAX = 100;\nvar [createProgressContext, createProgressScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROGRESS_NAME);\nvar [ProgressProvider, useProgressContext] = createProgressContext(PROGRESS_NAME);\nvar Progress = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeProgress,\n      value: valueProp = null,\n      max: maxProp,\n      getValueLabel = defaultGetValueLabel,\n      ...progressProps\n    } = props;\n    if ((maxProp || maxProp === 0) && !isValidMaxNumber(maxProp)) {\n      console.error(getInvalidMaxError(`${maxProp}`, \"Progress\"));\n    }\n    const max = isValidMaxNumber(maxProp) ? maxProp : DEFAULT_MAX;\n    if (valueProp !== null && !isValidValueNumber(valueProp, max)) {\n      console.error(getInvalidValueError(`${valueProp}`, \"Progress\"));\n    }\n    const value = isValidValueNumber(valueProp, max) ? valueProp : null;\n    const valueLabel = isNumber(value) ? getValueLabel(value, max) : void 0;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ProgressProvider, { scope: __scopeProgress, value, max, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.div,\n      {\n        \"aria-valuemax\": max,\n        \"aria-valuemin\": 0,\n        \"aria-valuenow\": isNumber(value) ? value : void 0,\n        \"aria-valuetext\": valueLabel,\n        role: \"progressbar\",\n        \"data-state\": getProgressState(value, max),\n        \"data-value\": value ?? void 0,\n        \"data-max\": max,\n        ...progressProps,\n        ref: forwardedRef\n      }\n    ) });\n  }\n);\nProgress.displayName = PROGRESS_NAME;\nvar INDICATOR_NAME = \"ProgressIndicator\";\nvar ProgressIndicator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeProgress, ...indicatorProps } = props;\n    const context = useProgressContext(INDICATOR_NAME, __scopeProgress);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.div,\n      {\n        \"data-state\": getProgressState(context.value, context.max),\n        \"data-value\": context.value ?? void 0,\n        \"data-max\": context.max,\n        ...indicatorProps,\n        ref: forwardedRef\n      }\n    );\n  }\n);\nProgressIndicator.displayName = INDICATOR_NAME;\nfunction defaultGetValueLabel(value, max) {\n  return `${Math.round(value / max * 100)}%`;\n}\nfunction getProgressState(value, maxValue) {\n  return value == null ? \"indeterminate\" : value === maxValue ? \"complete\" : \"loading\";\n}\nfunction isNumber(value) {\n  return typeof value === \"number\";\n}\nfunction isValidMaxNumber(max) {\n  return isNumber(max) && !isNaN(max) && max > 0;\n}\nfunction isValidValueNumber(value, max) {\n  return isNumber(value) && !isNaN(value) && value <= max && value >= 0;\n}\nfunction getInvalidMaxError(propValue, componentName) {\n  return `Invalid prop \\`max\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. Only numbers greater than 0 are valid max values. Defaulting to \\`${DEFAULT_MAX}\\`.`;\n}\nfunction getInvalidValueError(propValue, componentName) {\n  return `Invalid prop \\`value\\` of value \\`${propValue}\\` supplied to \\`${componentName}\\`. The \\`value\\` prop must be:\n  - a positive number\n  - less than the value passed to \\`max\\` (or ${DEFAULT_MAX} if no \\`max\\` prop is set)\n  - \\`null\\` or \\`undefined\\` if the progress is indeterminate.\n\nDefaulting to \\`null\\`.`;\n}\nvar Root = Progress;\nvar Indicator = ProgressIndicator;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-progress/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-roving-focus/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-roving-focus/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   RovingFocusGroup: () => (/* binding */ RovingFocusGroup),\n/* harmony export */   RovingFocusGroupItem: () => (/* binding */ RovingFocusGroupItem),\n/* harmony export */   createRovingFocusGroupScope: () => (/* binding */ createRovingFocusGroupScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/roving-focus-group.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar ENTRY_FOCUS = \"rovingFocusGroup.onEntryFocus\";\nvar EVENT_OPTIONS = { bubbles: false, cancelable: true };\nvar GROUP_NAME = \"RovingFocusGroup\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(GROUP_NAME);\nvar [createRovingFocusGroupContext, createRovingFocusGroupScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(\n  GROUP_NAME,\n  [createCollectionScope]\n);\nvar [RovingFocusProvider, useRovingFocusContext] = createRovingFocusGroupContext(GROUP_NAME);\nvar RovingFocusGroup = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, { scope: props.__scopeRovingFocusGroup, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RovingFocusGroupImpl, { ...props, ref: forwardedRef }) }) });\n  }\n);\nRovingFocusGroup.displayName = GROUP_NAME;\nvar RovingFocusGroupImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n  const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_5__.useDirection)(dir);\n  const [currentTabStopId, setCurrentTabStopId] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_6__.useControllableState)({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const handleEntryFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_7__.useCallbackRef)(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    RovingFocusProvider,\n    {\n      scope: __scopeRovingFocusGroup,\n      orientation,\n      dir: direction,\n      loop,\n      currentTabStopId,\n      onItemFocus: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      ),\n      onItemShiftTab: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => setIsTabbingBackOut(true), []),\n      onFocusableItemAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      ),\n      onFocusableItemRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      ),\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n        _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.div,\n        {\n          tabIndex: isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0,\n          \"data-orientation\": orientation,\n          ...groupProps,\n          ref: composedRefs,\n          style: { outline: \"none\", ...props.style },\n          onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, () => {\n            isClickFocusRef.current = true;\n          }),\n          onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, (event) => {\n            const isKeyboardFocus = !isClickFocusRef.current;\n            if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n              const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n              event.currentTarget.dispatchEvent(entryFocusEvent);\n              if (!entryFocusEvent.defaultPrevented) {\n                const items = getItems().filter((item) => item.focusable);\n                const activeItem = items.find((item) => item.active);\n                const currentItem = items.find((item) => item.id === currentTabStopId);\n                const candidateItems = [activeItem, currentItem, ...items].filter(\n                  Boolean\n                );\n                const candidateNodes = candidateItems.map((item) => item.ref.current);\n                focusFirst(candidateNodes, preventScrollOnEntryFocus);\n              }\n            }\n            isClickFocusRef.current = false;\n          }),\n          onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onBlur, () => setIsTabbingBackOut(false))\n        }\n      )\n    }\n  );\n});\nvar ITEM_NAME = \"RovingFocusGroupItem\";\nvar RovingFocusGroupItem = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_10__.useId)();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      Collection.ItemSlot,\n      {\n        scope: __scopeRovingFocusGroup,\n        id,\n        focusable,\n        active,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_8__.Primitive.span,\n          {\n            tabIndex: isCurrentTabStop ? 0 : -1,\n            \"data-orientation\": context.orientation,\n            ...itemProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onMouseDown, (event) => {\n              if (!focusable) event.preventDefault();\n              else context.onItemFocus(id);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onFocus, () => context.onItemFocus(id)),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_9__.composeEventHandlers)(props.onKeyDown, (event) => {\n              if (event.key === \"Tab\" && event.shiftKey) {\n                context.onItemShiftTab();\n                return;\n              }\n              if (event.target !== event.currentTarget) return;\n              const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n              if (focusIntent !== void 0) {\n                if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n                event.preventDefault();\n                const items = getItems().filter((item) => item.focusable);\n                let candidateNodes = items.map((item) => item.ref.current);\n                if (focusIntent === \"last\") candidateNodes.reverse();\n                else if (focusIntent === \"prev\" || focusIntent === \"next\") {\n                  if (focusIntent === \"prev\") candidateNodes.reverse();\n                  const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                  candidateNodes = context.loop ? wrapArray(candidateNodes, currentIndex + 1) : candidateNodes.slice(currentIndex + 1);\n                }\n                setTimeout(() => focusFirst(candidateNodes));\n              }\n            }),\n            children: typeof children === \"function\" ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null }) : children\n          }\n        )\n      }\n    );\n  }\n);\nRovingFocusGroupItem.displayName = ITEM_NAME;\nvar MAP_KEY_TO_FOCUS_INTENT = {\n  ArrowLeft: \"prev\",\n  ArrowUp: \"prev\",\n  ArrowRight: \"next\",\n  ArrowDown: \"next\",\n  PageUp: \"first\",\n  Home: \"first\",\n  PageDown: \"last\",\n  End: \"last\"\n};\nfunction getDirectionAwareKey(key, dir) {\n  if (dir !== \"rtl\") return key;\n  return key === \"ArrowLeft\" ? \"ArrowRight\" : key === \"ArrowRight\" ? \"ArrowLeft\" : key;\n}\nfunction getFocusIntent(event, orientation, dir) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === \"vertical\" && [\"ArrowLeft\", \"ArrowRight\"].includes(key)) return void 0;\n  if (orientation === \"horizontal\" && [\"ArrowUp\", \"ArrowDown\"].includes(key)) return void 0;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\nfunction focusFirst(candidates, preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root = RovingFocusGroup;\nvar Item = RovingFocusGroupItem;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-roving-focus/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-select/dist/index.mjs":
/*!****************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/../../node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/../../node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/../../node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/../../node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/../../node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/../../node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/../../node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/../../node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/../../node_modules/aria-hidden/dist/es5/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/../../node_modules/react-remove-scroll/dist/es5/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\" \", \"Enter\", \"ArrowUp\", \"ArrowDown\"];\nvar SELECTION_KEYS = [\" \", \"Enter\"];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n  createCollectionScope,\n  _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props) => {\n  const {\n    __scopeSelect,\n    children,\n    open: openProp,\n    defaultOpen,\n    onOpenChange,\n    value: valueProp,\n    defaultValue,\n    onValueChange,\n    dir,\n    name,\n    autoComplete,\n    disabled,\n    required,\n    form\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n  const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n    prop: openProp,\n    defaultProp: defaultOpen ?? false,\n    onChange: onOpenChange,\n    caller: SELECT_NAME\n  });\n  const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n    caller: SELECT_NAME\n  });\n  const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n  const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n  const nativeSelectKey = Array.from(nativeOptionsSet).map((option) => option.props.value).join(\";\");\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, { ...popperScope, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(\n    SelectProvider,\n    {\n      required,\n      scope: __scopeSelect,\n      trigger,\n      onTriggerChange: setTrigger,\n      valueNode,\n      onValueNodeChange: setValueNode,\n      valueNodeHasChildren,\n      onValueNodeHasChildrenChange: setValueNodeHasChildren,\n      contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n      value,\n      onValueChange: setValue,\n      open,\n      onOpenChange: setOpen,\n      dir: direction,\n      triggerPointerDownPosRef,\n      disabled,\n      children: [\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, { scope: __scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n          SelectNativeOptionsProvider,\n          {\n            scope: props.__scopeSelect,\n            onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option) => {\n              setNativeOptionsSet((prev) => new Set(prev).add(option));\n            }, []),\n            onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option) => {\n              setNativeOptionsSet((prev) => {\n                const optionsSet = new Set(prev);\n                optionsSet.delete(option);\n                return optionsSet;\n              });\n            }, []),\n            children\n          }\n        ) }),\n        isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(\n          SelectBubbleInput,\n          {\n            \"aria-hidden\": true,\n            required,\n            tabIndex: -1,\n            name,\n            autoComplete,\n            value,\n            onChange: (event) => setValue(event.target.value),\n            disabled,\n            form,\n            children: [\n              value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", { value: \"\" }) : null,\n              Array.from(nativeOptionsSet)\n            ]\n          },\n          nativeSelectKey\n        ) : null\n      ]\n    }\n  ) });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.value === context.value);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem !== void 0) {\n        context.onValueChange(nextItem.value);\n      }\n    });\n    const handleOpen = (pointerEvent) => {\n      if (!isDisabled) {\n        context.onOpenChange(true);\n        resetTypeahead();\n      }\n      if (pointerEvent) {\n        context.triggerPointerDownPosRef.current = {\n          x: Math.round(pointerEvent.pageX),\n          y: Math.round(pointerEvent.pageY)\n        };\n      }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, { asChild: true, ...popperScope, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button,\n      {\n        type: \"button\",\n        role: \"combobox\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open,\n        \"aria-required\": context.required,\n        \"aria-autocomplete\": \"none\",\n        dir: context.dir,\n        \"data-state\": context.open ? \"open\" : \"closed\",\n        disabled: isDisabled,\n        \"data-disabled\": isDisabled ? \"\" : void 0,\n        \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n        ...triggerProps,\n        ref: composedRefs,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event) => {\n          event.currentTarget.focus();\n          if (pointerTypeRef.current !== \"mouse\") {\n            handleOpen(event);\n          }\n        }),\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event) => {\n          pointerTypeRef.current = event.pointerType;\n          const target = event.target;\n          if (target.hasPointerCapture(event.pointerId)) {\n            target.releasePointerCapture(event.pointerId);\n          }\n          if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n            handleOpen(event);\n            event.preventDefault();\n          }\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event) => {\n          const isTypingAhead = searchRef.current !== \"\";\n          const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n          if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n          if (isTypingAhead && event.key === \" \") return;\n          if (OPEN_KEYS.includes(event.key)) {\n            handleOpen();\n            event.preventDefault();\n          }\n        })\n      }\n    ) });\n  }\n);\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n      onValueNodeHasChildrenChange(hasChildren);\n    }, [onValueNodeHasChildrenChange, hasChildren]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span,\n      {\n        ...valueProps,\n        ref: composedRefs,\n        style: { pointerEvents: \"none\" },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: placeholder }) : children\n      }\n    );\n  }\n);\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, { \"aria-hidden\": true, ...iconProps, ref: forwardedRef, children: children || \"\\u25BC\" });\n  }\n);\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, { asChild: true, ...props });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n      setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n      const frag = fragment;\n      return frag ? react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(\n        /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, { scope: props.__scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, { scope: props.__scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", { children: props.children }) }) }),\n        frag\n      ) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, { ...props, ref: forwardedRef });\n  }\n);\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.createSlot)(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      position = \"item-aligned\",\n      onCloseAutoFocus,\n      onEscapeKeyDown,\n      onPointerDownOutside,\n      //\n      // PopperContent props\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions,\n      //\n      ...contentProps\n    } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node) => setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\n      null\n    );\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_15__.hideOthers)(content);\n    }, [content]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (candidates) => {\n        const [firstItem, ...restItems] = getItems().map((item) => item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates) {\n          if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n          candidate?.scrollIntoView({ block: \"nearest\" });\n          if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n          if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n          candidate?.focus();\n          if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n      },\n      [getItems, viewport]\n    );\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      () => focusFirst([selectedItem, content]),\n      [focusFirst, selectedItem, content]\n    );\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (isPositioned) {\n        focusSelectedItem();\n      }\n    }, [isPositioned, focusSelectedItem]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      if (content) {\n        let pointerMoveDelta = { x: 0, y: 0 };\n        const handlePointerMove = (event) => {\n          pointerMoveDelta = {\n            x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n            y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n          };\n        };\n        const handlePointerUp = (event) => {\n          if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n            event.preventDefault();\n          } else {\n            if (!content.contains(event.target)) {\n              onOpenChange(false);\n            }\n          }\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          triggerPointerDownPosRef.current = null;\n        };\n        if (triggerPointerDownPosRef.current !== null) {\n          document.addEventListener(\"pointermove\", handlePointerMove);\n          document.addEventListener(\"pointerup\", handlePointerUp, { capture: true, once: true });\n        }\n        return () => {\n          document.removeEventListener(\"pointermove\", handlePointerMove);\n          document.removeEventListener(\"pointerup\", handlePointerUp, { capture: true });\n        };\n      }\n    }, [content, onOpenChange, triggerPointerDownPosRef]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const close = () => onOpenChange(false);\n      window.addEventListener(\"blur\", close);\n      window.addEventListener(\"resize\", close);\n      return () => {\n        window.removeEventListener(\"blur\", close);\n        window.removeEventListener(\"resize\", close);\n      };\n    }, [onOpenChange]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search) => {\n      const enabledItems = getItems().filter((item) => !item.disabled);\n      const currentItem = enabledItems.find((item) => item.ref.current === document.activeElement);\n      const nextItem = findNextItem(enabledItems, search, currentItem);\n      if (nextItem) {\n        setTimeout(() => nextItem.ref.current.focus());\n      }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItem(node);\n          if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n      },\n      [context.value]\n    );\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => content?.focus(), [content]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n      (node, value, disabled) => {\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n          setSelectedItemText(node);\n        }\n      },\n      [context.value]\n    );\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n      side,\n      sideOffset,\n      align,\n      alignOffset,\n      arrowPadding,\n      collisionBoundary,\n      collisionPadding,\n      sticky,\n      hideWhenDetached,\n      avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      SelectContentProvider,\n      {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__.RemoveScroll, { as: Slot, allowPinchZoom: true, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n          _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope,\n          {\n            asChild: true,\n            trapped: context.open,\n            onMountAutoFocus: (event) => {\n              event.preventDefault();\n            },\n            onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event) => {\n              context.trigger?.focus({ preventScroll: true });\n              event.preventDefault();\n            }),\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n              _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer,\n              {\n                asChild: true,\n                disableOutsidePointerEvents: true,\n                onEscapeKeyDown,\n                onPointerDownOutside,\n                onFocusOutside: (event) => event.preventDefault(),\n                onDismiss: () => context.onOpenChange(false),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n                  SelectPosition,\n                  {\n                    role: \"listbox\",\n                    id: context.contentId,\n                    \"data-state\": context.open ? \"open\" : \"closed\",\n                    dir: context.dir,\n                    onContextMenu: (event) => event.preventDefault(),\n                    ...contentProps,\n                    ...popperContentProps,\n                    onPlaced: () => setIsPositioned(true),\n                    ref: composedRefs,\n                    style: {\n                      // flex layout so we can place the scroll buttons properly\n                      display: \"flex\",\n                      flexDirection: \"column\",\n                      // reset the outline by default as the content MAY get focused\n                      outline: \"none\",\n                      ...contentProps.style\n                    },\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event) => {\n                      const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                      if (event.key === \"Tab\") event.preventDefault();\n                      if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                      if ([\"ArrowUp\", \"ArrowDown\", \"Home\", \"End\"].includes(event.key)) {\n                        const items = getItems().filter((item) => !item.disabled);\n                        let candidateNodes = items.map((item) => item.ref.current);\n                        if ([\"ArrowUp\", \"End\"].includes(event.key)) {\n                          candidateNodes = candidateNodes.slice().reverse();\n                        }\n                        if ([\"ArrowUp\", \"ArrowDown\"].includes(event.key)) {\n                          const currentElement = event.target;\n                          const currentIndex = candidateNodes.indexOf(currentElement);\n                          candidateNodes = candidateNodes.slice(currentIndex + 1);\n                        }\n                        setTimeout(() => focusFirst(candidateNodes));\n                        event.preventDefault();\n                      }\n                    })\n                  }\n                )\n              }\n            )\n          }\n        ) })\n      }\n    );\n  }\n);\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onPlaced, ...popperProps } = props;\n  const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n  const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n  const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node) => setContent(node));\n  const getItems = useCollection(__scopeSelect);\n  const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n  const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n  const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n      const triggerRect = context.trigger.getBoundingClientRect();\n      const contentRect = content.getBoundingClientRect();\n      const valueNodeRect = context.valueNode.getBoundingClientRect();\n      const itemTextRect = selectedItemText.getBoundingClientRect();\n      if (context.dir !== \"rtl\") {\n        const itemTextOffset = itemTextRect.left - contentRect.left;\n        const left = valueNodeRect.left - itemTextOffset;\n        const leftDelta = triggerRect.left - left;\n        const minContentWidth = triggerRect.width + leftDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const rightEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n          CONTENT_MARGIN,\n          // Prevents the content from going off the starting edge of the\n          // viewport. It may still go off the ending edge, but this can be\n          // controlled by the user since they may want to manage overflow in a\n          // specific way.\n          // https://github.com/radix-ui/primitives/issues/2049\n          Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.left = clampedLeft + \"px\";\n      } else {\n        const itemTextOffset = contentRect.right - itemTextRect.right;\n        const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n        const rightDelta = window.innerWidth - triggerRect.right - right;\n        const minContentWidth = triggerRect.width + rightDelta;\n        const contentWidth = Math.max(minContentWidth, contentRect.width);\n        const leftEdge = window.innerWidth - CONTENT_MARGIN;\n        const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n          CONTENT_MARGIN,\n          Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n        ]);\n        contentWrapper.style.minWidth = minContentWidth + \"px\";\n        contentWrapper.style.right = clampedRight + \"px\";\n      }\n      const items = getItems();\n      const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n      const itemsHeight = viewport.scrollHeight;\n      const contentStyles = window.getComputedStyle(content);\n      const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n      const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n      const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n      const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n      const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n      const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n      const viewportStyles = window.getComputedStyle(viewport);\n      const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n      const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n      const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n      const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n      const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n      const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n      const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n      const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n      const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n      if (willAlignWithoutTopOverflow) {\n        const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n        contentWrapper.style.bottom = \"0px\";\n        const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n        const clampedTriggerMiddleToBottomEdge = Math.max(\n          triggerMiddleToBottomEdge,\n          selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n          (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth\n        );\n        const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n        contentWrapper.style.height = height + \"px\";\n      } else {\n        const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n        contentWrapper.style.top = \"0px\";\n        const clampedTopEdgeToTriggerMiddle = Math.max(\n          topEdgeToTriggerMiddle,\n          contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n          (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight\n        );\n        const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n        contentWrapper.style.height = height + \"px\";\n        viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n      }\n      contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n      contentWrapper.style.minHeight = minContentHeight + \"px\";\n      contentWrapper.style.maxHeight = availableHeight + \"px\";\n      onPlaced?.();\n      requestAnimationFrame(() => shouldExpandOnScrollRef.current = true);\n    }\n  }, [\n    getItems,\n    context.trigger,\n    context.valueNode,\n    contentWrapper,\n    content,\n    viewport,\n    selectedItem,\n    selectedItemText,\n    context.dir,\n    onPlaced\n  ]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => position(), [position]);\n  const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n  }, [content]);\n  const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (node) => {\n      if (node && shouldRepositionRef.current === true) {\n        position();\n        focusSelectedItem?.();\n        shouldRepositionRef.current = false;\n      }\n    },\n    [position, focusSelectedItem]\n  );\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    SelectViewportProvider,\n    {\n      scope: __scopeSelect,\n      contentWrapper,\n      shouldExpandOnScrollRef,\n      onScrollButtonChange: handleScrollButtonChange,\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"div\",\n        {\n          ref: setContentWrapper,\n          style: {\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"fixed\",\n            zIndex: contentZIndex\n          },\n          children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n            _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n            {\n              ...popperProps,\n              ref: composedRefs,\n              style: {\n                // When we get the height of the content, it includes borders. If we were to set\n                // the height without having `boxSizing: 'border-box'` it would be too big.\n                boxSizing: \"border-box\",\n                // We need to ensure the content doesn't get taller than the wrapper\n                maxHeight: \"100%\",\n                ...popperProps.style\n              }\n            }\n          )\n        }\n      )\n    }\n  );\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const {\n    __scopeSelect,\n    align = \"start\",\n    collisionPadding = CONTENT_MARGIN,\n    ...popperProps\n  } = props;\n  const popperScope = usePopperScope(__scopeSelect);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content,\n    {\n      ...popperScope,\n      ...popperProps,\n      ref: forwardedRef,\n      align,\n      collisionPadding,\n      style: {\n        // Ensure border-box for floating-ui calculations\n        boxSizing: \"border-box\",\n        ...popperProps.style,\n        // re-namespace exposed content custom properties\n        ...{\n          \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n          \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n          \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n          \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n          \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n        }\n      }\n    }\n  );\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        \"style\",\n        {\n          dangerouslySetInnerHTML: {\n            __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n          },\n          nonce\n        }\n      ),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, { scope: __scopeSelect, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n        _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n        {\n          \"data-radix-select-viewport\": \"\",\n          role: \"presentation\",\n          ...viewportProps,\n          ref: composedRefs,\n          style: {\n            // we use position: 'relative' here on the `viewport` so that when we call\n            // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n            // (independent of the scrollUpButton).\n            position: \"relative\",\n            flex: 1,\n            // Viewport should only be scrollable in the vertical direction.\n            // This won't work in vertical writing modes, so we'll need to\n            // revisit this if/when that is supported\n            // https://developer.chrome.com/blog/vertical-form-controls\n            overflow: \"hidden auto\",\n            ...viewportProps.style\n          },\n          onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event) => {\n            const viewport = event.currentTarget;\n            const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n            if (shouldExpandOnScrollRef?.current && contentWrapper) {\n              const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n              if (scrolledBy > 0) {\n                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                const cssHeight = parseFloat(contentWrapper.style.height);\n                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                if (prevHeight < availableHeight) {\n                  const nextHeight = prevHeight + scrolledBy;\n                  const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                  const heightDiff = nextHeight - clampedNextHeight;\n                  contentWrapper.style.height = clampedNextHeight + \"px\";\n                  if (contentWrapper.style.bottom === \"0px\") {\n                    viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                    contentWrapper.style.justifyContent = \"flex-end\";\n                  }\n                }\n              }\n            }\n            prevScrollTopRef.current = viewport.scrollTop;\n          })\n        }\n      ) })\n    ] });\n  }\n);\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, { scope: __scopeSelect, id: groupId, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, { role: \"group\", \"aria-labelledby\": groupId, ...groupProps, ref: forwardedRef }) });\n  }\n);\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, { id: groupContext.id, ...labelProps, ref: forwardedRef });\n  }\n);\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeSelect,\n      value,\n      disabled = false,\n      textValue: textValueProp,\n      ...itemProps\n    } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(\n      forwardedRef,\n      (node) => contentContext.itemRefCallback?.(node, value, disabled)\n    );\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = () => {\n      if (!disabled) {\n        context.onValueChange(value);\n        context.onOpenChange(false);\n      }\n    };\n    if (value === \"\") {\n      throw new Error(\n        \"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\"\n      );\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      SelectItemContextProvider,\n      {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node) => {\n          setTextValue((prevTextValue) => prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n          Collection.ItemSlot,\n          {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n              _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n              {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, () => setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, () => setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, () => {\n                  if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, () => {\n                  if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event) => {\n                  pointerTypeRef.current = event.pointerType;\n                  if (disabled) {\n                    contentContext.onItemLeave?.();\n                  } else if (pointerTypeRef.current === \"mouse\") {\n                    event.currentTarget.focus({ preventScroll: true });\n                  }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event) => {\n                  if (event.currentTarget === document.activeElement) {\n                    contentContext.onItemLeave?.();\n                  }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event) => {\n                  const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                  if (isTypingAhead && event.key === \" \") return;\n                  if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                  if (event.key === \" \") event.preventDefault();\n                })\n              }\n            )\n          }\n        )\n      }\n    );\n  }\n);\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(\n      forwardedRef,\n      (node) => setItemTextNode(node),\n      itemContext.onItemTextChange,\n      (node) => contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled)\n    );\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n      () => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", { value: itemContext.value, disabled: itemContext.disabled, children: textContent }, itemContext.value),\n      [itemContext.disabled, itemContext.value, textContent]\n    );\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n      onNativeOptionAdd(nativeOption);\n      return () => onNativeOptionRemove(nativeOption);\n    }, [onNativeOptionAdd, onNativeOptionRemove, nativeOption]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, { children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, { id: itemContext.textId, ...itemTextProps, ref: composedRefs }),\n      itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n    ] });\n  }\n);\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, { \"aria-hidden\": true, ...itemIndicatorProps, ref: forwardedRef }) : null;\n  }\n);\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const canScrollUp2 = viewport.scrollTop > 0;\n        setCanScrollUp(canScrollUp2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n  const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n  const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    if (contentContext.viewport && contentContext.isPositioned) {\n      let handleScroll2 = function() {\n        const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n        const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n        setCanScrollDown(canScrollDown2);\n      };\n      var handleScroll = handleScroll2;\n      const viewport = contentContext.viewport;\n      handleScroll2();\n      viewport.addEventListener(\"scroll\", handleScroll2);\n      return () => viewport.removeEventListener(\"scroll\", handleScroll2);\n    }\n  }, [contentContext.viewport, contentContext.isPositioned]);\n  return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    SelectScrollButtonImpl,\n    {\n      ...props,\n      ref: composedRefs,\n      onAutoScroll: () => {\n        const { viewport, selectedItem } = contentContext;\n        if (viewport && selectedItem) {\n          viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n        }\n      }\n    }\n  ) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n  const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n  const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n  const getItems = useCollection(__scopeSelect);\n  const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    if (autoScrollTimerRef.current !== null) {\n      window.clearInterval(autoScrollTimerRef.current);\n      autoScrollTimerRef.current = null;\n    }\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => clearAutoScrollTimer();\n  }, [clearAutoScrollTimer]);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(() => {\n    const activeItem = getItems().find((item) => item.ref.current === document.activeElement);\n    activeItem?.ref.current?.scrollIntoView({ block: \"nearest\" });\n  }, [getItems]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n    _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div,\n    {\n      \"aria-hidden\": true,\n      ...scrollIndicatorProps,\n      ref: forwardedRef,\n      style: { flexShrink: 0, ...scrollIndicatorProps.style },\n      onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, () => {\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, () => {\n        contentContext.onItemLeave?.();\n        if (autoScrollTimerRef.current === null) {\n          autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n        }\n      }),\n      onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, () => {\n        clearAutoScrollTimer();\n      })\n    }\n  );\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, { \"aria-hidden\": true, ...separatorProps, ref: forwardedRef });\n  }\n);\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, { ...popperScope, ...arrowProps, ref: forwardedRef }) : null;\n  }\n);\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  ({ __scopeSelect, value, ...props }, forwardedRef) => {\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const select = ref.current;\n      if (!select) return;\n      const selectProto = window.HTMLSelectElement.prototype;\n      const descriptor = Object.getOwnPropertyDescriptor(\n        selectProto,\n        \"value\"\n      );\n      const setValue = descriptor.set;\n      if (prevValue !== value && setValue) {\n        const event = new Event(\"change\", { bubbles: true });\n        setValue.call(select, value);\n        select.dispatchEvent(event);\n      }\n    }, [prevValue, value]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.select,\n      {\n        ...props,\n        style: { ..._radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VISUALLY_HIDDEN_STYLES, ...props.style },\n        ref: composedRefs,\n        defaultValue: value\n      }\n    );\n  }\n);\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n  return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n  const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n  const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n  const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n  const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (key) => {\n      const search = searchRef.current + key;\n      handleSearchChange(search);\n      (function updateSearch(value) {\n        searchRef.current = value;\n        window.clearTimeout(timerRef.current);\n        if (value !== \"\") timerRef.current = window.setTimeout(() => updateSearch(\"\"), 1e3);\n      })(search);\n    },\n    [handleSearchChange]\n  );\n  const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(() => {\n    searchRef.current = \"\";\n    window.clearTimeout(timerRef.current);\n  }, []);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    return () => window.clearTimeout(timerRef.current);\n  }, []);\n  return [searchRef, handleTypeaheadSearch, resetTypeahead];\n}\nfunction findNextItem(items, search, currentItem) {\n  const isRepeated = search.length > 1 && Array.from(search).every((char) => char === search[0]);\n  const normalizedSearch = isRepeated ? search[0] : search;\n  const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n  let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n  const excludeCurrentItem = normalizedSearch.length === 1;\n  if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v) => v !== currentItem);\n  const nextItem = wrappedItems.find(\n    (item) => item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase())\n  );\n  return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n  return array.map((_, index) => array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Slot),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable),\n/* harmony export */   createSlot: () => (/* binding */ createSlot),\n/* harmony export */   createSlottable: () => (/* binding */ createSlottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/../../node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/slot.tsx\n\n\n\n// @__NO_SIDE_EFFECTS__\nfunction createSlot(ownerName) {\n  const SlotClone = /* @__PURE__ */ createSlotClone(ownerName);\n  const Slot2 = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n    const slottable = childrenArray.find(isSlottable);\n    if (slottable) {\n      const newElement = slottable.props.children;\n      const newChildren = childrenArray.map((child) => {\n        if (child === slottable) {\n          if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n          return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n        } else {\n          return child;\n        }\n      });\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n  });\n  Slot2.displayName = `${ownerName}.Slot`;\n  return Slot2;\n}\nvar Slot = /* @__PURE__ */ createSlot(\"Slot\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlotClone(ownerName) {\n  const SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { children, ...slotProps } = props;\n    if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n      const childrenRef = getElementRef(children);\n      const props2 = mergeProps(slotProps, children.props);\n      if (children.type !== react__WEBPACK_IMPORTED_MODULE_0__.Fragment) {\n        props2.ref = forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef;\n      }\n      return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, props2);\n    }\n    return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n  });\n  SlotClone.displayName = `${ownerName}.SlotClone`;\n  return SlotClone;\n}\nvar SLOTTABLE_IDENTIFIER = Symbol(\"radix.slottable\");\n// @__NO_SIDE_EFFECTS__\nfunction createSlottable(ownerName) {\n  const Slottable2 = ({ children }) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n  };\n  Slottable2.displayName = `${ownerName}.Slottable`;\n  Slottable2.__radixId = SLOTTABLE_IDENTIFIER;\n  return Slottable2;\n}\nvar Slottable = /* @__PURE__ */ createSlottable(\"Slottable\");\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && typeof child.type === \"function\" && \"__radixId\" in child.type && child.type.__radixId === SLOTTABLE_IDENTIFIER;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          const result = childPropValue(...args);\n          slotPropValue(...args);\n          return result;\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs":
/*!**************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-tabs/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   List: () => (/* binding */ List),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTabsScope: () => (/* binding */ createTabsScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/../../node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/../../node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/../../node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/../../node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/../../node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/../../node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\"use client\";\n\n// src/tabs.tsx\n\n\n\n\n\n\n\n\n\n\n\nvar TABS_NAME = \"Tabs\";\nvar [createTabsContext, createTabsScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(TABS_NAME, [\n  _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.createRovingFocusGroupScope\n]);\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.createRovingFocusGroupScope)();\nvar [TabsProvider, useTabsContext] = createTabsContext(TABS_NAME);\nvar Tabs = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = \"horizontal\",\n      dir,\n      activationMode = \"automatic\",\n      ...tabsProps\n    } = props;\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? \"\",\n      caller: TABS_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      TabsProvider,\n      {\n        scope: __scopeTabs,\n        baseId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_6__.useId)(),\n        value,\n        onValueChange: setValue,\n        orientation,\n        dir: direction,\n        activationMode,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div,\n          {\n            dir: direction,\n            \"data-orientation\": orientation,\n            ...tabsProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nTabs.displayName = TABS_NAME;\nvar TAB_LIST_NAME = \"TabsList\";\nvar TabsList = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.Root,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        orientation: context.orientation,\n        dir: context.dir,\n        loop,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div,\n          {\n            role: \"tablist\",\n            \"aria-orientation\": context.orientation,\n            ...listProps,\n            ref: forwardedRef\n          }\n        )\n      }\n    );\n  }\n);\nTabsList.displayName = TAB_LIST_NAME;\nvar TRIGGER_NAME = \"TabsTrigger\";\nvar TabsTrigger = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_3__.Item,\n      {\n        asChild: true,\n        ...rovingFocusGroupScope,\n        focusable: !disabled,\n        active: isSelected,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n          _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button,\n          {\n            type: \"button\",\n            role: \"tab\",\n            \"aria-selected\": isSelected,\n            \"aria-controls\": contentId,\n            \"data-state\": isSelected ? \"active\" : \"inactive\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            id: triggerId,\n            ...triggerProps,\n            ref: forwardedRef,\n            onMouseDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onMouseDown, (event) => {\n              if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                context.onValueChange(value);\n              } else {\n                event.preventDefault();\n              }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event) => {\n              if ([\" \", \"Enter\"].includes(event.key)) context.onValueChange(value);\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, () => {\n              const isAutomaticActivation = context.activationMode !== \"manual\";\n              if (!isSelected && !disabled && isAutomaticActivation) {\n                context.onValueChange(value);\n              }\n            })\n          }\n        )\n      }\n    );\n  }\n);\nTabsTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"TabsContent\";\nvar TabsContent = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isSelected);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const rAF = requestAnimationFrame(() => isMountAnimationPreventedRef.current = false);\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, { present: forceMount || isSelected, children: ({ present }) => /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div,\n      {\n        \"data-state\": isSelected ? \"active\" : \"inactive\",\n        \"data-orientation\": context.orientation,\n        role: \"tabpanel\",\n        \"aria-labelledby\": triggerId,\n        hidden: !present,\n        id: contentId,\n        tabIndex: 0,\n        ...contentProps,\n        ref: forwardedRef,\n        style: {\n          ...props.style,\n          animationDuration: isMountAnimationPreventedRef.current ? \"0s\" : void 0\n        },\n        children: present && children\n      }\n    ) });\n  }\n);\nTabsContent.displayName = CONTENT_NAME;\nfunction makeTriggerId(baseId, value) {\n  return `${baseId}-trigger-${value}`;\n}\nfunction makeContentId(baseId, value) {\n  return `${baseId}-content-${value}`;\n}\nvar Root2 = Tabs;\nvar List = TabsList;\nvar Trigger = TabsTrigger;\nvar Content = TabsContent;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-tabs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/use-callback-ref.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0I7QUFDQSxzQkFBc0IseUNBQVk7QUFDbEMsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBLEdBQUc7QUFDSCxTQUFTLDBDQUFhO0FBQ3RCO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtY2FsbGJhY2stcmVmL2Rpc3QvaW5kZXgubWpzP2ViZDEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWNhbGxiYWNrLXJlZi9zcmMvdXNlLWNhbGxiYWNrLXJlZi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlQ2FsbGJhY2tSZWYoY2FsbGJhY2spIHtcbiAgY29uc3QgY2FsbGJhY2tSZWYgPSBSZWFjdC51c2VSZWYoY2FsbGJhY2spO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNhbGxiYWNrUmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgfSk7XG4gIHJldHVybiBSZWFjdC51c2VNZW1vKCgpID0+ICguLi5hcmdzKSA9PiBjYWxsYmFja1JlZi5jdXJyZW50Py4oLi4uYXJncyksIFtdKTtcbn1cbmV4cG9ydCB7XG4gIHVzZUNhbGxiYWNrUmVmXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs":
/*!********************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useControllableState: () => (/* binding */ useControllableState),\n/* harmony export */   useControllableStateReducer: () => (/* binding */ useControllableStateReducer)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-effect-event */ \"(ssr)/../../node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\");\n// src/use-controllable-state.tsx\n\n\nvar useInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()] || _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect;\nfunction useControllableState({\n  prop,\n  defaultProp,\n  onChange = () => {\n  },\n  caller\n}) {\n  const [uncontrolledProp, setUncontrolledProp, onChangeRef] = useUncontrolledState({\n    defaultProp,\n    onChange\n  });\n  const isControlled = prop !== void 0;\n  const value = isControlled ? prop : uncontrolledProp;\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(prop !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const setValue = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (nextValue) => {\n      if (isControlled) {\n        const value2 = isFunction(nextValue) ? nextValue(prop) : nextValue;\n        if (value2 !== prop) {\n          onChangeRef.current?.(value2);\n        }\n      } else {\n        setUncontrolledProp(nextValue);\n      }\n    },\n    [isControlled, prop, setUncontrolledProp, onChangeRef]\n  );\n  return [value, setValue];\n}\nfunction useUncontrolledState({\n  defaultProp,\n  onChange\n}) {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(defaultProp);\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(value);\n  const onChangeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n  useInsertionEffect(() => {\n    onChangeRef.current = onChange;\n  }, [onChange]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== value) {\n      onChangeRef.current?.(value);\n      prevValueRef.current = value;\n    }\n  }, [value, prevValueRef]);\n  return [value, setValue, onChangeRef];\n}\nfunction isFunction(value) {\n  return typeof value === \"function\";\n}\n\n// src/use-controllable-state-reducer.tsx\n\n\nvar SYNC_STATE = Symbol(\"RADIX:SYNC_STATE\");\nfunction useControllableStateReducer(reducer, userArgs, initialArg, init) {\n  const { prop: controlledState, defaultProp, onChange: onChangeProp, caller } = userArgs;\n  const isControlled = controlledState !== void 0;\n  const onChange = (0,_radix_ui_react_use_effect_event__WEBPACK_IMPORTED_MODULE_2__.useEffectEvent)(onChangeProp);\n  if (true) {\n    const isControlledRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(controlledState !== void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n      const wasControlled = isControlledRef.current;\n      if (wasControlled !== isControlled) {\n        const from = wasControlled ? \"controlled\" : \"uncontrolled\";\n        const to = isControlled ? \"controlled\" : \"uncontrolled\";\n        console.warn(\n          `${caller} is changing from ${from} to ${to}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`\n        );\n      }\n      isControlledRef.current = isControlled;\n    }, [isControlled, caller]);\n  }\n  const args = [{ ...initialArg, state: defaultProp }];\n  if (init) {\n    args.push(init);\n  }\n  const [internalState, dispatch] = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(\n    (state2, action) => {\n      if (action.type === SYNC_STATE) {\n        return { ...state2, state: action.state };\n      }\n      const next = reducer(state2, action);\n      if (isControlled && !Object.is(next.state, state2.state)) {\n        onChange(next.state);\n      }\n      return next;\n    },\n    ...args\n  );\n  const uncontrolledState = internalState.state;\n  const prevValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(uncontrolledState);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (prevValueRef.current !== uncontrolledState) {\n      prevValueRef.current = uncontrolledState;\n      if (!isControlled) {\n        onChange(uncontrolledState);\n      }\n    }\n  }, [onChange, uncontrolledState, prevValueRef, isControlled]);\n  const state = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    const isControlled2 = controlledState !== void 0;\n    if (isControlled2) {\n      return { ...internalState, state: controlledState };\n    }\n    return internalState;\n  }, [internalState, controlledState]);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    if (isControlled && !Object.is(controlledState, internalState.state)) {\n      dispatch({ type: SYNC_STATE, state: controlledState });\n    }\n  }, [controlledState, internalState.state, isControlled]);\n  return [state, dispatch];\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-effect-event/dist/index.mjs":
/*!**************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-effect-event/dist/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEffectEvent: () => (/* binding */ useEffectEvent)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// src/use-effect-event.tsx\n\n\nvar useReactEffectEvent = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useEffectEvent \".trim().toString()];\nvar useReactInsertionEffect = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\" useInsertionEffect \".trim().toString()];\nfunction useEffectEvent(callback) {\n  if (typeof useReactEffectEvent === \"function\") {\n    return useReactEffectEvent(callback);\n  }\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(() => {\n    throw new Error(\"Cannot call an event handler while rendering.\");\n  });\n  if (typeof useReactInsertionEffect === \"function\") {\n    useReactInsertionEffect(() => {\n      ref.current = callback;\n    });\n  } else {\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n      ref.current = callback;\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => ref.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZWZmZWN0LWV2ZW50L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNvRTtBQUNyQztBQUMvQiwwQkFBMEIseUxBQUs7QUFDL0IsOEJBQThCLHlMQUFLO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyx5Q0FBWTtBQUMxQjtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wsSUFBSTtBQUNKLElBQUksa0ZBQWU7QUFDbkI7QUFDQSxLQUFLO0FBQ0w7QUFDQSxTQUFTLDBDQUFhO0FBQ3RCO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZWZmZWN0LWV2ZW50L2Rpc3QvaW5kZXgubWpzPzQyYzMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZS1lZmZlY3QtZXZlbnQudHN4XG5pbXBvcnQgeyB1c2VMYXlvdXRFZmZlY3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VSZWFjdEVmZmVjdEV2ZW50ID0gUmVhY3RbXCIgdXNlRWZmZWN0RXZlbnQgXCIudHJpbSgpLnRvU3RyaW5nKCldO1xudmFyIHVzZVJlYWN0SW5zZXJ0aW9uRWZmZWN0ID0gUmVhY3RbXCIgdXNlSW5zZXJ0aW9uRWZmZWN0IFwiLnRyaW0oKS50b1N0cmluZygpXTtcbmZ1bmN0aW9uIHVzZUVmZmVjdEV2ZW50KGNhbGxiYWNrKSB7XG4gIGlmICh0eXBlb2YgdXNlUmVhY3RFZmZlY3RFdmVudCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgcmV0dXJuIHVzZVJlYWN0RWZmZWN0RXZlbnQoY2FsbGJhY2spO1xuICB9XG4gIGNvbnN0IHJlZiA9IFJlYWN0LnVzZVJlZigoKSA9PiB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQ2Fubm90IGNhbGwgYW4gZXZlbnQgaGFuZGxlciB3aGlsZSByZW5kZXJpbmcuXCIpO1xuICB9KTtcbiAgaWYgKHR5cGVvZiB1c2VSZWFjdEluc2VydGlvbkVmZmVjdCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgdXNlUmVhY3RJbnNlcnRpb25FZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfSBlbHNlIHtcbiAgICB1c2VMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgICAgcmVmLmN1cnJlbnQgPSBjYWxsYmFjaztcbiAgICB9KTtcbiAgfVxuICByZXR1cm4gUmVhY3QudXNlTWVtbygoKSA9PiAoLi4uYXJncykgPT4gcmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlRWZmZWN0RXZlbnRcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-effect-event/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs":
/*!****************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useEscapeKeydown: () => (/* binding */ useEscapeKeydown)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/../../node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n// packages/react/use-escape-keydown/src/use-escape-keydown.tsx\n\n\nfunction useEscapeKeydown(onEscapeKeyDownProp, ownerDocument = globalThis?.document) {\n  const onEscapeKeyDown = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_1__.useCallbackRef)(onEscapeKeyDownProp);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const handleKeyDown = (event) => {\n      if (event.key === \"Escape\") {\n        onEscapeKeyDown(event);\n      }\n    };\n    ownerDocument.addEventListener(\"keydown\", handleKeyDown, { capture: true });\n    return () => ownerDocument.removeEventListener(\"keydown\", handleKeyDown, { capture: true });\n  }, [onEscapeKeyDown, ownerDocument]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZXNjYXBlLWtleWRvd24vZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDK0I7QUFDbUM7QUFDbEU7QUFDQSwwQkFBMEIsZ0ZBQWM7QUFDeEMsRUFBRSw0Q0FBZTtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0RBQStELGVBQWU7QUFDOUUsK0VBQStFLGVBQWU7QUFDOUYsR0FBRztBQUNIO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtZXNjYXBlLWtleWRvd24vZGlzdC9pbmRleC5tanM/OWJjYiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtZXNjYXBlLWtleWRvd24vc3JjL3VzZS1lc2NhcGUta2V5ZG93bi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2tSZWYgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWZcIjtcbmZ1bmN0aW9uIHVzZUVzY2FwZUtleWRvd24ob25Fc2NhcGVLZXlEb3duUHJvcCwgb3duZXJEb2N1bWVudCA9IGdsb2JhbFRoaXM/LmRvY3VtZW50KSB7XG4gIGNvbnN0IG9uRXNjYXBlS2V5RG93biA9IHVzZUNhbGxiYWNrUmVmKG9uRXNjYXBlS2V5RG93blByb3ApO1xuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC5rZXkgPT09IFwiRXNjYXBlXCIpIHtcbiAgICAgICAgb25Fc2NhcGVLZXlEb3duKGV2ZW50KTtcbiAgICAgIH1cbiAgICB9O1xuICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcihcImtleWRvd25cIiwgaGFuZGxlS2V5RG93biwgeyBjYXB0dXJlOiB0cnVlIH0pO1xuICAgIHJldHVybiAoKSA9PiBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoXCJrZXlkb3duXCIsIGhhbmRsZUtleURvd24sIHsgY2FwdHVyZTogdHJ1ZSB9KTtcbiAgfSwgW29uRXNjYXBlS2V5RG93biwgb3duZXJEb2N1bWVudF0pO1xufVxuZXhwb3J0IHtcbiAgdXNlRXNjYXBlS2V5ZG93blxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs":
/*!*************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsHydrated: () => (/* binding */ useIsHydrated)\n/* harmony export */ });\n/* harmony import */ var use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! use-sync-external-store/shim */ \"(ssr)/../../node_modules/use-sync-external-store/shim/index.js\");\n// src/use-is-hydrated.tsx\n\nfunction useIsHydrated() {\n  return (0,use_sync_external_store_shim__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore)(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\nfunction subscribe() {\n  return () => {\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtaXMtaHlkcmF0ZWQvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNvRTtBQUNwRTtBQUNBLFNBQVMsa0ZBQW9CO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9Ad2FsaS9jbGllbnQtd2ViLy4uLy4uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWlzLWh5ZHJhdGVkL2Rpc3QvaW5kZXgubWpzPzZjZTUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZS1pcy1oeWRyYXRlZC50c3hcbmltcG9ydCB7IHVzZVN5bmNFeHRlcm5hbFN0b3JlIH0gZnJvbSBcInVzZS1zeW5jLWV4dGVybmFsLXN0b3JlL3NoaW1cIjtcbmZ1bmN0aW9uIHVzZUlzSHlkcmF0ZWQoKSB7XG4gIHJldHVybiB1c2VTeW5jRXh0ZXJuYWxTdG9yZShcbiAgICBzdWJzY3JpYmUsXG4gICAgKCkgPT4gdHJ1ZSxcbiAgICAoKSA9PiBmYWxzZVxuICApO1xufVxuZnVuY3Rpb24gc3Vic2NyaWJlKCkge1xuICByZXR1cm4gKCkgPT4ge1xuICB9O1xufVxuZXhwb3J0IHtcbiAgdXNlSXNIeWRyYXRlZFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/use-layout-effect.tsx\n\nvar useLayoutEffect2 = globalThis?.document ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQytCO0FBQy9CLDhDQUE4QyxrREFBcUI7QUFDbkU7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8uLi8uLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzPzRhZDUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvdXNlLWxheW91dC1lZmZlY3Qvc3JjL3VzZS1sYXlvdXQtZWZmZWN0LnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlTGF5b3V0RWZmZWN0MiA9IGdsb2JhbFRoaXM/LmRvY3VtZW50ID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogKCkgPT4ge1xufTtcbmV4cG9ydCB7XG4gIHVzZUxheW91dEVmZmVjdDIgYXMgdXNlTGF5b3V0RWZmZWN0XG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-previous/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-previous/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-previous/src/use-previous.tsx\n\nfunction usePrevious(value) {\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef({ value, previous: value });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => {\n    if (ref.current.value !== value) {\n      ref.current.previous = ref.current.value;\n      ref.current.value = value;\n    }\n    return ref.current.previous;\n  }, [value]);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtcHJldmlvdXMvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLGNBQWMseUNBQVksR0FBRyx3QkFBd0I7QUFDckQsU0FBUywwQ0FBYTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBR0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtcHJldmlvdXMvZGlzdC9pbmRleC5tanM/MDY0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC91c2UtcHJldmlvdXMvc3JjL3VzZS1wcmV2aW91cy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gdXNlUHJldmlvdXModmFsdWUpIHtcbiAgY29uc3QgcmVmID0gUmVhY3QudXNlUmVmKHsgdmFsdWUsIHByZXZpb3VzOiB2YWx1ZSB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4ge1xuICAgIGlmIChyZWYuY3VycmVudC52YWx1ZSAhPT0gdmFsdWUpIHtcbiAgICAgIHJlZi5jdXJyZW50LnByZXZpb3VzID0gcmVmLmN1cnJlbnQudmFsdWU7XG4gICAgICByZWYuY3VycmVudC52YWx1ZSA9IHZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gcmVmLmN1cnJlbnQucHJldmlvdXM7XG4gIH0sIFt2YWx1ZV0pO1xufVxuZXhwb3J0IHtcbiAgdXNlUHJldmlvdXNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-previous/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-use-size/dist/index.mjs":
/*!******************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-use-size/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSize: () => (/* binding */ useSize)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/../../node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n// packages/react/use-size/src/use-size.tsx\n\n\nfunction useSize(element) {\n  const [size, setSize] = react__WEBPACK_IMPORTED_MODULE_0__.useState(void 0);\n  (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(() => {\n    if (element) {\n      setSize({ width: element.offsetWidth, height: element.offsetHeight });\n      const resizeObserver = new ResizeObserver((entries) => {\n        if (!Array.isArray(entries)) {\n          return;\n        }\n        if (!entries.length) {\n          return;\n        }\n        const entry = entries[0];\n        let width;\n        let height;\n        if (\"borderBoxSize\" in entry) {\n          const borderSizeEntry = entry[\"borderBoxSize\"];\n          const borderSize = Array.isArray(borderSizeEntry) ? borderSizeEntry[0] : borderSizeEntry;\n          width = borderSize[\"inlineSize\"];\n          height = borderSize[\"blockSize\"];\n        } else {\n          width = element.offsetWidth;\n          height = element.offsetHeight;\n        }\n        setSize({ width, height });\n      });\n      resizeObserver.observe(element, { box: \"border-box\" });\n      return () => resizeObserver.unobserve(element);\n    } else {\n      setSize(void 0);\n    }\n  }, [element]);\n  return size;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-use-size/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs":
/*!*************************************************************************!*\
  !*** ../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   VISUALLY_HIDDEN_STYLES: () => (/* binding */ VISUALLY_HIDDEN_STYLES),\n/* harmony export */   VisuallyHidden: () => (/* binding */ VisuallyHidden)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/../../node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/visually-hidden.tsx\n\n\n\nvar VISUALLY_HIDDEN_STYLES = Object.freeze({\n  // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n  position: \"absolute\",\n  border: 0,\n  width: 1,\n  height: 1,\n  padding: 0,\n  margin: -1,\n  overflow: \"hidden\",\n  clip: \"rect(0, 0, 0, 0)\",\n  whiteSpace: \"nowrap\",\n  wordWrap: \"normal\"\n});\nvar NAME = \"VisuallyHidden\";\nvar VisuallyHidden = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (props, forwardedRef) => {\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n      _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.span,\n      {\n        ...props,\n        ref: forwardedRef,\n        style: { ...VISUALLY_HIDDEN_STYLES, ...props.style }\n      }\n    );\n  }\n);\nVisuallyHidden.displayName = NAME;\nvar Root = VisuallyHidden;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC12aXN1YWxseS1oaWRkZW4vZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDdUI7QUFDZDtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQSxxQkFBcUIsNkNBQWdCO0FBQ3JDO0FBQ0EsMkJBQTJCLHNEQUFHO0FBQzlCLE1BQU0sZ0VBQVM7QUFDZjtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBS0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0B3YWxpL2NsaWVudC13ZWIvLi4vLi4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC12aXN1YWxseS1oaWRkZW4vZGlzdC9pbmRleC5tanM/Y2E3OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvdmlzdWFsbHktaGlkZGVuLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZVwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgVklTVUFMTFlfSElEREVOX1NUWUxFUyA9IE9iamVjdC5mcmVlemUoe1xuICAvLyBTZWU6IGh0dHBzOi8vZ2l0aHViLmNvbS90d2JzL2Jvb3RzdHJhcC9ibG9iL21haW4vc2Nzcy9taXhpbnMvX3Zpc3VhbGx5LWhpZGRlbi5zY3NzXG4gIHBvc2l0aW9uOiBcImFic29sdXRlXCIsXG4gIGJvcmRlcjogMCxcbiAgd2lkdGg6IDEsXG4gIGhlaWdodDogMSxcbiAgcGFkZGluZzogMCxcbiAgbWFyZ2luOiAtMSxcbiAgb3ZlcmZsb3c6IFwiaGlkZGVuXCIsXG4gIGNsaXA6IFwicmVjdCgwLCAwLCAwLCAwKVwiLFxuICB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiLFxuICB3b3JkV3JhcDogXCJub3JtYWxcIlxufSk7XG52YXIgTkFNRSA9IFwiVmlzdWFsbHlIaWRkZW5cIjtcbnZhciBWaXN1YWxseUhpZGRlbiA9IFJlYWN0LmZvcndhcmRSZWYoXG4gIChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goXG4gICAgICBQcmltaXRpdmUuc3BhbixcbiAgICAgIHtcbiAgICAgICAgLi4ucHJvcHMsXG4gICAgICAgIHJlZjogZm9yd2FyZGVkUmVmLFxuICAgICAgICBzdHlsZTogeyAuLi5WSVNVQUxMWV9ISURERU5fU1RZTEVTLCAuLi5wcm9wcy5zdHlsZSB9XG4gICAgICB9XG4gICAgKTtcbiAgfVxuKTtcblZpc3VhbGx5SGlkZGVuLmRpc3BsYXlOYW1lID0gTkFNRTtcbnZhciBSb290ID0gVmlzdWFsbHlIaWRkZW47XG5leHBvcnQge1xuICBSb290LFxuICBWSVNVQUxMWV9ISURERU5fU1RZTEVTLFxuICBWaXN1YWxseUhpZGRlblxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\n");

/***/ })

};
;