"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const config_1 = require("@nestjs/config");
const helmet_1 = require("helmet");
const compression = require("compression");
const express_rate_limit_1 = require("express-rate-limit");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    app.use((0, helmet_1.default)());
    app.use(compression());
    app.use((0, express_rate_limit_1.default)({
        windowMs: 15 * 60 * 1000,
        max: 100,
        message: 'Trop de requêtes depuis cette IP, ve<PERSON><PERSON><PERSON> réessayer plus tard.',
    }));
    app.enableCors({
        origin: configService.get('ALLOWED_ORIGINS')?.split(',') || [
            'http://localhost:3000',
            'http://localhost:3001',
            'http://localhost:3002',
        ],
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.setGlobalPrefix('api/v1');
    const config = new swagger_1.DocumentBuilder()
        .setTitle('WALI Livraison API')
        .setDescription('API pour l\'application de livraison WALI')
        .setVersion('1.0')
        .addBearerAuth({
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: 'Enter JWT token',
        in: 'header',
    }, 'JWT-auth')
        .addTag('Auth', 'Authentification et autorisation')
        .addTag('Users', 'Gestion des utilisateurs')
        .addTag('Orders', 'Gestion des commandes')
        .addTag('Drivers', 'Gestion des livreurs')
        .addTag('Partners', 'Gestion des partenaires')
        .addTag('Restaurants', 'Gestion des restaurants')
        .addTag('Stores', 'Gestion des magasins')
        .addTag('Products', 'Gestion des produits')
        .addTag('Payments', 'Gestion des paiements')
        .addTag('Notifications', 'Système de notifications')
        .addTag('Admin', 'Administration')
        .build();
    const document = swagger_1.SwaggerModule.createDocument(app, config);
    swagger_1.SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
            persistAuthorization: true,
        },
    });
    const port = configService.get('PORT') || 3001;
    await app.listen(port);
    console.log(`🚀 WALI Livraison API démarrée sur le port ${port}`);
    console.log(`📚 Documentation Swagger disponible sur http://localhost:${port}/api/docs`);
}
bootstrap();
//# sourceMappingURL=main.js.map