{"name": "wali-l<PERSON><PERSON><PERSON>", "private": true, "description": "WALI Livraison - Plateforme de livraison multi-services en Côte d'Ivoire", "version": "1.0.0", "author": "WALI Team", "license": "MIT", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "type-check": "turbo run type-check", "test": "turbo run test", "test:e2e": "turbo run test:e2e", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "db:studio": "turbo run db:studio", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,md,json}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,md,json}\"", "changeset": "changeset", "version-packages": "changeset version", "release": "turbo run build --filter=docs^... && changeset publish", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "deploy:staging": "npm run build && npm run deploy:staging:api && npm run deploy:staging:web", "deploy:production": "npm run build && npm run deploy:prod:api && npm run deploy:prod:web"}, "devDependencies": {"@changesets/cli": "^2.26.0", "eslint": "^8.48.0", "prettier": "^3.0.0", "turbo": "latest", "typescript": "^5.0.0", "concurrently": "^8.2.0"}, "packageManager": "npm@9.0.0", "workspaces": ["apps/api", "apps/client-web", "apps/admin-panel", "packages/database", "packages/shared", "packages/ui"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["liv<PERSON><PERSON>", "delivery", "cote-divoire", "mobile-money", "e-commerce", "logistics"], "repository": {"type": "git", "url": "https://github.com/wali-team/wali-livraison.git"}, "bugs": {"url": "https://github.com/wali-team/wali-livraison/issues"}, "homepage": "https://wali-livraison.ci"}