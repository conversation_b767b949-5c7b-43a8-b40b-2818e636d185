export interface Transaction {
    id: string;
    orderId: string;
    userId: string;
    type: TransactionType;
    method: PaymentMethod;
    amount: number;
    currency: string;
    status: TransactionStatus;
    externalId?: string;
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
}
export declare enum TransactionType {
    PAYMENT = "PAYMENT",
    REFUND = "REFUND",
    COMMISSION = "COMMISSION",
    DRIVER_PAYOUT = "DRIVER_PAYOUT",
    PARTNER_PAYOUT = "PARTNER_PAYOUT"
}
export declare enum PaymentMethod {
    CASH = "CASH",
    STRIPE = "STRIPE",
    ORANGE_MONEY = "ORANGE_MONEY",
    MTN_MONEY = "MTN_MONEY",
    WAVE = "WAVE"
}
export declare enum TransactionStatus {
    PENDING = "PENDING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    CANCELLED = "CANCELLED",
    REFUNDED = "REFUNDED"
}
