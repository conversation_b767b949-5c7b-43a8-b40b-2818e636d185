{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/calculator.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/clock.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/package.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/star.js", "(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CElisee%5CDocuments%5Caugment-projects%5CMientior%20livraison%20app%5Capps%5Cclient-web%5Csrc%5Capp%5Cpage.tsx&server=false!", "(app-pages-browser)/./src/app/page.tsx", "(app-pages-browser)/./src/components/delivery-form.tsx", "(app-pages-browser)/./src/components/ui/textarea.tsx", "(app-pages-browser)/./src/hooks/useOrders.ts", "(app-pages-browser)/./src/lib/api-client.ts"]}