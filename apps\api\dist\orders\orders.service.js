"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var OrdersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrdersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const pricing_service_1 = require("./pricing.service");
const client_1 = require("@prisma/client");
let OrdersService = OrdersService_1 = class OrdersService {
    constructor(prisma, pricingService) {
        this.prisma = prisma;
        this.pricingService = pricingService;
        this.logger = new common_1.Logger(OrdersService_1.name);
    }
    async calculatePrice(priceCalculationDto) {
        const validation = this.pricingService.validateOrder(priceCalculationDto);
        if (!validation.valid) {
            throw new common_1.BadRequestException(validation.errors.join(', '));
        }
        return this.pricingService.calculatePrice(priceCalculationDto);
    }
    async createOrder(userId, createOrderDto) {
        const { items, scheduledAt, ...orderData } = createOrderDto;
        const priceCalculation = await this.calculatePrice({
            type: createOrderDto.type,
            pickupLatitude: createOrderDto.pickupLatitude,
            pickupLongitude: createOrderDto.pickupLongitude,
            deliveryLatitude: createOrderDto.deliveryLatitude,
            deliveryLongitude: createOrderDto.deliveryLongitude,
            items,
        });
        const orderNumber = await this.generateOrderNumber();
        const order = await this.prisma.order.create({
            data: {
                orderNumber,
                clientId: userId,
                type: orderData.type,
                status: client_1.OrderStatus.PENDING,
                pickupAddress: orderData.pickupAddress,
                pickupLatitude: orderData.pickupLatitude,
                pickupLongitude: orderData.pickupLongitude,
                deliveryAddress: orderData.deliveryAddress,
                deliveryLatitude: orderData.deliveryLatitude,
                deliveryLongitude: orderData.deliveryLongitude,
                basePrice: priceCalculation.basePrice,
                deliveryFee: priceCalculation.deliveryFee,
                totalAmount: priceCalculation.totalAmount,
                estimatedDuration: priceCalculation.estimatedDuration,
                notes: orderData.notes,
                scheduledAt: scheduledAt ? new Date(scheduledAt) : null,
                items: {
                    create: items.map(item => ({
                        name: item.name,
                        description: item.description,
                        quantity: item.quantity,
                        unitPrice: item.unitPrice,
                        totalPrice: item.quantity * item.unitPrice,
                    })),
                },
            },
            include: {
                items: true,
                client: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                    },
                },
            },
        });
        await this.createTrackingEvent(order.id, client_1.OrderStatus.PENDING, 'Commande créée');
        this.logger.log(`Nouvelle commande créée: ${order.orderNumber} pour ${userId}`);
        return this.mapOrderToInterface(order);
    }
    async getUserOrders(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [orders, total] = await Promise.all([
            this.prisma.order.findMany({
                where: { clientId: userId },
                include: {
                    items: true,
                    client: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            phone: true,
                        },
                    },
                    driver: {
                        select: {
                            id: true,
                            firstName: true,
                            lastName: true,
                            phone: true,
                        },
                    },
                },
                orderBy: { createdAt: 'desc' },
                skip,
                take: limit,
            }),
            this.prisma.order.count({
                where: { clientId: userId },
            }),
        ]);
        return {
            orders: orders.map(order => this.mapOrderToInterface(order)),
            total,
        };
    }
    async getOrderById(orderId, userId) {
        const order = await this.prisma.order.findFirst({
            where: {
                id: orderId,
                clientId: userId,
            },
            include: {
                items: true,
                client: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                    },
                },
                driver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                    },
                },
                trackingEvents: {
                    orderBy: { createdAt: 'desc' },
                },
            },
        });
        if (!order) {
            throw new common_1.NotFoundException('Commande non trouvée');
        }
        return this.mapOrderToInterface(order);
    }
    async updateOrder(orderId, userId, updateOrderDto) {
        const existingOrder = await this.prisma.order.findFirst({
            where: {
                id: orderId,
                clientId: userId,
            },
        });
        if (!existingOrder) {
            throw new common_1.NotFoundException('Commande non trouvée');
        }
        if (existingOrder.status === client_1.OrderStatus.DELIVERED ||
            existingOrder.status === client_1.OrderStatus.CANCELLED ||
            existingOrder.status === client_1.OrderStatus.FAILED) {
            throw new common_1.BadRequestException('Cette commande ne peut plus être modifiée');
        }
        const { scheduledAt, ...updateData } = updateOrderDto;
        const updatedOrder = await this.prisma.order.update({
            where: { id: orderId },
            data: {
                ...updateData,
                scheduledAt: scheduledAt ? new Date(scheduledAt) : undefined,
            },
            include: {
                items: true,
                client: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                    },
                },
                driver: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                    },
                },
            },
        });
        if (updateOrderDto.status && updateOrderDto.status !== existingOrder.status) {
            await this.createTrackingEvent(orderId, updateOrderDto.status, `Statut mis à jour: ${updateOrderDto.status}`);
        }
        this.logger.log(`Commande mise à jour: ${orderId}`);
        return this.mapOrderToInterface(updatedOrder);
    }
    async cancelOrder(orderId, userId, reason) {
        const order = await this.prisma.order.findFirst({
            where: {
                id: orderId,
                clientId: userId,
            },
        });
        if (!order) {
            throw new common_1.NotFoundException('Commande non trouvée');
        }
        if (order.status === client_1.OrderStatus.DELIVERED ||
            order.status === client_1.OrderStatus.CANCELLED ||
            order.status === client_1.OrderStatus.FAILED) {
            throw new common_1.BadRequestException('Cette commande ne peut pas être annulée');
        }
        if (order.status === client_1.OrderStatus.PICKED_UP || order.status === client_1.OrderStatus.IN_TRANSIT) {
            throw new common_1.BadRequestException('Cette commande est en cours de livraison et ne peut pas être annulée');
        }
        const updatedOrder = await this.prisma.order.update({
            where: { id: orderId },
            data: {
                status: client_1.OrderStatus.CANCELLED,
                notes: reason ? `${order.notes || ''}\nAnnulée: ${reason}`.trim() : order.notes,
            },
            include: {
                items: true,
                client: {
                    select: {
                        id: true,
                        firstName: true,
                        lastName: true,
                        phone: true,
                    },
                },
            },
        });
        await this.createTrackingEvent(orderId, client_1.OrderStatus.CANCELLED, reason || 'Commande annulée par le client');
        this.logger.log(`Commande annulée: ${orderId} - ${reason || 'Aucune raison'}`);
        return this.mapOrderToInterface(updatedOrder);
    }
    async generateOrderNumber() {
        const today = new Date();
        const year = today.getFullYear().toString().slice(-2);
        const month = (today.getMonth() + 1).toString().padStart(2, '0');
        const day = today.getDate().toString().padStart(2, '0');
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
        const todayOrdersCount = await this.prisma.order.count({
            where: {
                createdAt: {
                    gte: startOfDay,
                    lt: endOfDay,
                },
            },
        });
        const sequence = (todayOrdersCount + 1).toString().padStart(4, '0');
        return `WL${year}${month}${day}${sequence}`;
    }
    async createTrackingEvent(orderId, status, description) {
        await this.prisma.trackingEvent.create({
            data: {
                orderId,
                status,
                description,
            },
        });
    }
    mapOrderToInterface(order) {
        return {
            id: order.id,
            orderNumber: order.orderNumber,
            clientId: order.clientId,
            driverId: order.driverId,
            type: order.type,
            status: order.status,
            pickupAddress: order.pickupAddress,
            pickupLatitude: order.pickupLatitude,
            pickupLongitude: order.pickupLongitude,
            deliveryAddress: order.deliveryAddress,
            deliveryLatitude: order.deliveryLatitude,
            deliveryLongitude: order.deliveryLongitude,
            basePrice: order.basePrice,
            deliveryFee: order.deliveryFee,
            totalAmount: order.totalAmount,
            estimatedDuration: order.estimatedDuration,
            scheduledAt: order.scheduledAt,
            pickedUpAt: order.pickedUpAt,
            deliveredAt: order.deliveredAt,
            notes: order.notes,
            proofOfDelivery: order.proofOfDelivery,
            createdAt: order.createdAt,
            updatedAt: order.updatedAt,
            items: order.items,
            client: order.client,
            driver: order.driver,
            trackingEvents: order.trackingEvents,
        };
    }
};
exports.OrdersService = OrdersService;
exports.OrdersService = OrdersService = OrdersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        pricing_service_1.PricingService])
], OrdersService);
//# sourceMappingURL=orders.service.js.map