import { PrismaService } from '../prisma/prisma.service';
import { PricingService } from './pricing.service';
import { CreateOrderDto, UpdateOrderDto, PriceCalculationDto } from './dto';
import { Order as OrderInterface, PriceCalculationResult } from '@wali/shared';
export declare class OrdersService {
    private readonly prisma;
    private readonly pricingService;
    private readonly logger;
    constructor(prisma: PrismaService, pricingService: PricingService);
    calculatePrice(priceCalculationDto: PriceCalculationDto): Promise<PriceCalculationResult>;
    createOrder(userId: string, createOrderDto: CreateOrderDto): Promise<OrderInterface>;
    getUserOrders(userId: string, page?: number, limit?: number): Promise<{
        orders: OrderInterface[];
        total: number;
    }>;
    getOrderById(orderId: string, userId: string): Promise<OrderInterface>;
    updateOrder(orderId: string, userId: string, updateOrderDto: UpdateOrderDto): Promise<OrderInterface>;
    cancelOrder(orderId: string, userId: string, reason?: string): Promise<OrderInterface>;
    private generateOrderNumber;
    private createTrackingEvent;
    private mapOrderToInterface;
}
