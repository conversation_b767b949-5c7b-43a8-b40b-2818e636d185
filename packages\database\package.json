{"name": "@wali/database", "version": "1.0.0", "main": "index.js", "scripts": {"db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx seed.ts"}, "dependencies": {"@prisma/client": "^5.0.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "prisma": "^5.0.0", "react-native-paper": "^5.14.5", "react-native-toast-message": "^2.3.3", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"tsx": "^4.0.0"}}