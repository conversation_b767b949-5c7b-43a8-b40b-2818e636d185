{"name": "wali-l<PERSON><PERSON><PERSON>", "private": true, "description": "WALI Livraison - Plateforme de livraison multi-services en Côte d'Ivoire", "version": "1.0.0", "author": "WALI Team", "license": "MIT", "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "type-check": "turbo run type-check", "test": "turbo run test", "db:generate": "turbo run db:generate", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,md,json}\"", "setup:deps": "npm install turbo prettier eslint typescript --save-dev", "setup:web": "cd apps/api && npm install && cd ../client-web && npm install && cd ../admin-panel && npm install", "setup:mobile": "cd apps/mobile-client && npm install --legacy-peer-deps && cd ../mobile-driver && npm install --legacy-peer-deps"}, "devDependencies": {"turbo": "latest", "prettier": "^3.0.0", "eslint": "^8.48.0", "typescript": "^5.0.0"}, "workspaces": ["apps/api", "apps/client-web", "apps/admin-panel", "packages/database", "packages/shared", "packages/ui"], "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}