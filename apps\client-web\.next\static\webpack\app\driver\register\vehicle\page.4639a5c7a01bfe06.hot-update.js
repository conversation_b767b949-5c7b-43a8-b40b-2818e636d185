"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/driver/register/vehicle/page",{

/***/ "(app-pages-browser)/./src/app/driver/register/vehicle/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/driver/register/vehicle/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DriverVehicleRegistrationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/bike.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/driver-verification */ \"(app-pages-browser)/./src/lib/driver-verification.ts\");\n/* harmony import */ var _hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useDriver */ \"(app-pages-browser)/./src/hooks/useDriver.ts\");\n/* harmony import */ var _hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useWaliAuth */ \"(app-pages-browser)/./src/hooks/useWaliAuth.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DriverVehicleRegistrationPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isDriver } = (0,_hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__.useWaliAuth)();\n    const { createProfile, isLoading } = (0,_hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__.useDriver)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.MOTO,\n        brand: \"\",\n        model: \"\",\n        year: new Date().getFullYear(),\n        color: \"\",\n        registrationNumber: \"\",\n        engineNumber: \"\",\n        chassisNumber: \"\"\n    });\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Redirection si pas livreur\n    if (user && !isDriver) {\n        router.push(\"/dashboard\");\n        return null;\n    }\n    const vehicleTypeOptions = [\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.MOTO,\n            label: \"Motocyclette\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Motorcycle,\n            description: \"Id\\xe9al pour les livraisons rapides en ville\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.SCOOTER,\n            label: \"Scooter\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Motorcycle,\n            description: \"Parfait pour les courtes distances\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.VELO,\n            label: \"V\\xe9lo\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: \"\\xc9cologique et \\xe9conomique\",\n            requiresLicense: false\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.VOITURE,\n            label: \"Voiture\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: \"Pour les gros colis et longues distances\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.TRICYCLE,\n            label: \"Tricycle\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Capacit\\xe9 de charge importante\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.CAMIONNETTE,\n            label: \"Camionnette\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Pour les livraisons volumineuses\",\n            requiresLicense: true\n        }\n    ];\n    const validateField = (field, value)=>{\n        const errors = {\n            ...validationErrors\n        };\n        switch(field){\n            case \"brand\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.brand = \"La marque est obligatoire\";\n                } else {\n                    delete errors.brand;\n                }\n                break;\n            case \"model\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.model = \"Le mod\\xe8le est obligatoire\";\n                } else {\n                    delete errors.model;\n                }\n                break;\n            case \"color\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.color = \"La couleur est obligatoire\";\n                } else {\n                    delete errors.color;\n                }\n                break;\n            case \"registrationNumber\":\n                if (!value || typeof value === \"string\" && value.trim().length === 0) {\n                    errors.registrationNumber = \"Le num\\xe9ro d'immatriculation est obligatoire\";\n                } else if (typeof value === \"string\" && !(0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.validateIvorianPlateNumber)(value)) {\n                    errors.registrationNumber = \"Format invalide (ex: 1234 CI 01)\";\n                } else {\n                    delete errors.registrationNumber;\n                }\n                break;\n            case \"year\":\n                const currentYear = new Date().getFullYear();\n                if (typeof value === \"number\" && (value < 1990 || value > currentYear)) {\n                    errors.year = \"L'ann\\xe9e doit \\xeatre entre 1990 et \".concat(currentYear);\n                } else {\n                    delete errors.year;\n                }\n                break;\n        }\n        setValidationErrors(errors);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        validateField(field, value);\n    };\n    const handleVehicleTypeChange = (type)=>{\n        setFormData((prev)=>({\n                ...prev,\n                type,\n                brand: \"\",\n                model: \"\"\n            }));\n        // Clear brand/model errors when type changes\n        const errors = {\n            ...validationErrors\n        };\n        delete errors.brand;\n        delete errors.model;\n        setValidationErrors(errors);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation finale\n        const requiredFields = [\n            \"brand\",\n            \"model\",\n            \"color\",\n            \"registrationNumber\"\n        ];\n        const missingFields = requiredFields.filter((field)=>!formData[field] || String(formData[field]).trim().length === 0);\n        if (missingFields.length > 0) {\n            toast.error(\"Veuillez remplir tous les champs obligatoires\");\n            return;\n        }\n        if (Object.keys(validationErrors).length > 0) {\n            toast.error(\"Veuillez corriger les erreurs dans le formulaire\");\n            return;\n        }\n        try {\n            await createProfile(formData);\n            // Redirection selon le type de véhicule\n            if ((0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.requiresLicense)(formData.type)) {\n                router.push(\"/driver/register/documents\");\n            } else {\n                router.push(\"/driver/dashboard\");\n            }\n        } catch (error) {\n        // L'erreur est déjà gérée par le hook\n        }\n    };\n    const availableBrands = _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VEHICLE_BRANDS[formData.type] || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-2xl font-bold text-gray-900\",\n                                    children: \"WALI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Inscription Livreur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"\\xc9tape 1 : Informations sur votre v\\xe9hicule\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Progression\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_8__.Progress, {\n                            value: 33,\n                            className: \"h-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Informations du v\\xe9hicule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Renseignez les d\\xe9tails de votre v\\xe9hicule de livraison\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Type de v\\xe9hicule *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\",\n                                                            children: vehicleTypeOptions.map((option)=>{\n                                                                const IconComponent = option.icon;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(formData.type === option.value ? \"border-blue-500 bg-blue-50 ring-2 ring-blue-200\" : \"border-gray-200 hover:border-gray-300\"),\n                                                                    onClick: ()=>handleVehicleTypeChange(option.value),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"h-6 w-6 text-blue-600 mt-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: option.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                                        children: option.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 265,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    option.requiresLicense && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-orange-600 mt-1\",\n                                                                                        children: \"⚠️ Permis de conduire requis\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 267,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 27\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"brand\",\n                                                            children: \"Marque *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.brand,\n                                                            onValueChange: (value)=>handleInputChange(\"brand\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    className: validationErrors.brand ? \"border-red-500\" : \"\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"S\\xe9lectionnez la marque\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: availableBrands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.brand\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"model\",\n                                                                    children: \"Mod\\xe8le *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"model\",\n                                                                    placeholder: \"Ex: CB 125, Corolla, etc.\",\n                                                                    value: formData.model,\n                                                                    onChange: (e)=>handleInputChange(\"model\", e.target.value),\n                                                                    className: validationErrors.model ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                validationErrors.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-1\",\n                                                                    children: validationErrors.model\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"year\",\n                                                                    children: \"Ann\\xe9e\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"year\",\n                                                                    type: \"number\",\n                                                                    min: \"1990\",\n                                                                    max: new Date().getFullYear(),\n                                                                    value: formData.year,\n                                                                    onChange: (e)=>handleInputChange(\"year\", parseInt(e.target.value)),\n                                                                    className: validationErrors.year ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                validationErrors.year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-1\",\n                                                                    children: validationErrors.year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"color\",\n                                                            children: \"Couleur *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"color\",\n                                                            placeholder: \"Ex: Rouge, Bleu, Noir, etc.\",\n                                                            value: formData.color,\n                                                            onChange: (e)=>handleInputChange(\"color\", e.target.value),\n                                                            className: validationErrors.color ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.color && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.color\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"registrationNumber\",\n                                                            children: \"Num\\xe9ro d'immatriculation *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"registrationNumber\",\n                                                            placeholder: \"Ex: 1234 CI 01\",\n                                                            value: formData.registrationNumber,\n                                                            onChange: (e)=>handleInputChange(\"registrationNumber\", e.target.value.toUpperCase()),\n                                                            className: validationErrors.registrationNumber ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.registrationNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.registrationNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"Format ivoirien : 4 chiffres + CI + 2 chiffres\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"engineNumber\",\n                                                                    children: \"Num\\xe9ro de moteur (optionnel)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"engineNumber\",\n                                                                    placeholder: \"Num\\xe9ro grav\\xe9 sur le moteur\",\n                                                                    value: formData.engineNumber,\n                                                                    onChange: (e)=>handleInputChange(\"engineNumber\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"chassisNumber\",\n                                                                    children: \"Num\\xe9ro de ch\\xe2ssis (optionnel)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"chassisNumber\",\n                                                                    placeholder: \"Num\\xe9ro d'identification du v\\xe9hicule\",\n                                                                    value: formData.chassisNumber,\n                                                                    onChange: (e)=>handleInputChange(\"chassisNumber\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                                href: \"/auth/register\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Retour\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"submit\",\n                                                            disabled: isLoading,\n                                                            children: isLoading ? \"Enregistrement...\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    \"Continuer\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-blue-900\",\n                                                children: \"\\uD83D\\uDCCB Documents requis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: (0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.requiresLicense)(formData.type) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Permis de conduire valide\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Assurance v\\xe9hicule\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Carte grise\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Aucun document requis pour les v\\xe9los\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"\\uD83D\\uDCA1 Conseils\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"text-sm space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• V\\xe9rifiez que votre v\\xe9hicule est en bon \\xe9tat\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Assurez-vous d'avoir tous les documents \\xe0 jour\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Les informations doivent correspondre exactement aux documents officiels\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(DriverVehicleRegistrationPage, \"Q6kCI6VSzfSuJWFTRmrExEtvIok=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__.useWaliAuth,\n        _hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__.useDriver\n    ];\n});\n_c = DriverVehicleRegistrationPage;\nvar _c;\n$RefreshReg$(_c, \"DriverVehicleRegistrationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/driver/register/vehicle/page.tsx\n"));

/***/ })

});