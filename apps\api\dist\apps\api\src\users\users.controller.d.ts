import { UsersService } from './users.service';
import { UpdateProfileDto } from './dto';
import { User } from '@prisma/client';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    getProfile(user: User): Promise<Omit<{
        phone: string;
        email: string | null;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.UserRole;
        id: string;
        avatar: string | null;
        isActive: boolean;
        isVerified: boolean;
        createdAt: Date;
        updatedAt: Date;
    }, "createdAt" | "updatedAt">>;
    updateProfile(user: User, updateProfileDto: UpdateProfileDto): Promise<Omit<{
        phone: string;
        email: string | null;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.UserRole;
        id: string;
        avatar: string | null;
        isActive: boolean;
        isVerified: boolean;
        createdAt: Date;
        updatedAt: Date;
    }, "createdAt" | "updatedAt">>;
    getUserStats(user: User): Promise<{
        totalOrders: number;
        completedOrders: number;
        totalSpent: number;
        averageRating: number;
    }>;
    getUserById(id: string): Promise<Partial<{
        phone: string;
        email: string | null;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.UserRole;
        id: string;
        avatar: string | null;
        isActive: boolean;
        isVerified: boolean;
        createdAt: Date;
        updatedAt: Date;
    }>>;
    deleteAccount(user: User): Promise<{
        message: string;
    }>;
}
