"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const dto_1 = require("./dto");
const public_decorator_1 = require("./decorators/public.decorator");
const current_user_decorator_1 = require("./decorators/current-user.decorator");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async register(registerDto) {
        return this.authService.register(registerDto);
    }
    async login(loginDto) {
        return this.authService.login(loginDto);
    }
    async verifyOtp(verifyOtpDto) {
        return this.authService.verifyOtp(verifyOtpDto);
    }
    async refreshToken(refreshTokenDto) {
        return this.authService.refreshToken(refreshTokenDto);
    }
    async getProfile(user) {
        const { createdAt, updatedAt, ...userProfile } = user;
        return userProfile;
    }
    async checkToken(user) {
        return {
            valid: true,
            user: {
                id: user.id,
                phone: user.phone,
                role: user.role
            }
        };
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('register'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Inscription d\'un nouvel utilisateur',
        description: 'Crée un nouveau compte utilisateur et envoie un code OTP par SMS pour vérification'
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Compte créé avec succès. Code OTP envoyé par SMS.',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Compte créé avec succès. Un code de vérification a été envoyé par SMS.' },
                phone: { type: 'string', example: '+2250701234567' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Un compte existe déjà avec ce numéro de téléphone ou cette adresse email'
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Données d\'inscription invalides'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.RegisterDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Connexion d\'un utilisateur',
        description: 'Initie la connexion d\'un utilisateur existant en envoyant un code OTP par SMS'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Code OTP envoyé par SMS.',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Un code de vérification a été envoyé par SMS.' },
                phone: { type: 'string', example: '+2250701234567' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Aucun compte trouvé avec ce numéro de téléphone'
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Compte désactivé'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.LoginDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('verify-otp'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Vérification du code OTP',
        description: 'Vérifie le code OTP et retourne les tokens d\'authentification'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Code OTP vérifié avec succès. Utilisateur authentifié.',
        schema: {
            type: 'object',
            properties: {
                user: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        phone: { type: 'string' },
                        email: { type: 'string' },
                        firstName: { type: 'string' },
                        lastName: { type: 'string' },
                        avatar: { type: 'string' },
                        role: { type: 'string', enum: ['CLIENT', 'DRIVER', 'PARTNER', 'ADMIN'] },
                        isActive: { type: 'boolean' },
                        isVerified: { type: 'boolean' }
                    }
                },
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Code de vérification invalide ou expiré'
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Utilisateur non trouvé'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.VerifyOtpDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "verifyOtp", null);
__decorate([
    (0, public_decorator_1.Public)(),
    (0, common_1.Post)('refresh'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Rafraîchissement du token d\'accès',
        description: 'Génère un nouveau token d\'accès à partir du token de rafraîchissement'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Nouveau token d\'accès généré.',
        schema: {
            type: 'object',
            properties: {
                accessToken: { type: 'string' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Token de rafraîchissement invalide ou expiré'
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.RefreshTokenDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refreshToken", null);
__decorate([
    (0, common_1.Get)('profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Profil de l\'utilisateur connecté',
        description: 'Retourne les informations du profil de l\'utilisateur actuellement connecté'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Profil utilisateur récupéré avec succès.',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                phone: { type: 'string' },
                email: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                avatar: { type: 'string' },
                role: { type: 'string', enum: ['CLIENT', 'DRIVER', 'PARTNER', 'ADMIN'] },
                isActive: { type: 'boolean' },
                isVerified: { type: 'boolean' }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Token d\'authentification requis'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Get)('check'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Vérification du token',
        description: 'Vérifie la validité du token d\'authentification'
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Token valide.',
        schema: {
            type: 'object',
            properties: {
                valid: { type: 'boolean', example: true },
                user: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        phone: { type: 'string' },
                        role: { type: 'string' }
                    }
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Token invalide ou expiré'
    }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "checkToken", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('Authentification'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map