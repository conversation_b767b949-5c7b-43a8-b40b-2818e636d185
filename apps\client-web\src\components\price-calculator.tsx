'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calculator, MapPin, Clock, Package } from 'lucide-react';

interface PriceCalculatorProps {
  onPriceCalculated?: (result: any) => void;
}

export function PriceCalculator({ onPriceCalculated }: PriceCalculatorProps) {
  const [formData, setFormData] = useState({
    type: 'DELIVERY',
    pickupAddress: '',
    deliveryAddress: '',
  });

  const [isCalculating, setIsCalculating] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleCalculate = async () => {
    if (!formData.pickupAddress || !formData.deliveryAddress) {
      alert('Veuillez remplir les deux adresses');
      return;
    }

    setIsCalculating(true);

    // Simulation du calcul de prix
    setTimeout(() => {
      const mockResult = {
        distance: Math.random() * 20 + 2, // 2-22 km
        estimatedDuration: Math.random() * 60 + 15, // 15-75 min
        basePrice: formData.type === 'DELIVERY' ? 1000 : formData.type === 'FOOD' ? 1500 : 2000,
        deliveryFee: Math.random() * 2000 + 500,
        totalAmount: 0,
        breakdown: {
          distanceFee: Math.random() * 1000 + 200,
          timeFee: Math.random() * 300,
          typeFee: formData.type === 'FOOD' ? 300 : formData.type === 'SHOPPING' ? 500 : 0,
          itemsFee: formData.type === 'SHOPPING' ? Math.random() * 400 : 0,
        }
      };

      mockResult.totalAmount = mockResult.basePrice + mockResult.deliveryFee;

      setResult(mockResult);
      setIsCalculating(false);
      onPriceCalculated?.(mockResult);
    }, 2000);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calculator className="h-5 w-5" />
          <span>Calculateur de Prix</span>
        </CardTitle>
        <CardDescription>
          Estimez le coût de votre livraison en quelques clics
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Type de livraison */}
        <div>
          <Label>Type de livraison</Label>
          <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="DELIVERY">
                <div className="flex items-center space-x-2">
                  <Package className="h-4 w-4" />
                  <span>Livraison Express</span>
                </div>
              </SelectItem>
              <SelectItem value="FOOD">
                <div className="flex items-center space-x-2">
                  <span>🍽️</span>
                  <span>Livraison de Repas</span>
                </div>
              </SelectItem>
              <SelectItem value="SHOPPING">
                <div className="flex items-center space-x-2">
                  <span>🛒</span>
                  <span>Courses et Achats</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Adresses */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="pickup">Adresse de récupération</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="pickup"
                placeholder="Où récupérer ?"
                value={formData.pickupAddress}
                onChange={(e) => setFormData(prev => ({ ...prev, pickupAddress: e.target.value }))}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="delivery">Adresse de livraison</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="delivery"
                placeholder="Où livrer ?"
                value={formData.deliveryAddress}
                onChange={(e) => setFormData(prev => ({ ...prev, deliveryAddress: e.target.value }))}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        {/* Bouton de calcul */}
        <Button
          onClick={handleCalculate}
          disabled={isCalculating || !formData.pickupAddress || !formData.deliveryAddress}
          className="w-full"
        >
          {isCalculating ? 'Calcul en cours...' : 'Calculer le Prix'}
        </Button>

        {/* Résultat */}
        {result && (
          <div className="mt-6 p-4 bg-primary/5 rounded-lg border border-primary/20">
            <div className="flex items-center space-x-2 mb-3">
              <Clock className="h-4 w-4 text-primary" />
              <span className="text-sm text-muted-foreground">
                {result.distance.toFixed(1)} km • {Math.round(result.estimatedDuration)} min
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Prix de base:</span>
                <span>{result.basePrice.toLocaleString()} FCFA</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Frais de livraison:</span>
                <span>{Math.round(result.deliveryFee).toLocaleString()} FCFA</span>
              </div>
              <hr />
              <div className="flex justify-between font-bold">
                <span>Total:</span>
                <span className="text-primary">{Math.round(result.totalAmount).toLocaleString()} FCFA</span>
              </div>
            </div>

            {/* Détail des frais */}
            <details className="mt-3">
              <summary className="text-xs text-muted-foreground cursor-pointer">
                Voir le détail des frais
              </summary>
              <div className="mt-2 space-y-1 text-xs text-muted-foreground">
                <div className="flex justify-between">
                  <span>Frais de distance:</span>
                  <span>{Math.round(result.breakdown.distanceFee)} FCFA</span>
                </div>
                {result.breakdown.timeFee > 0 && (
                  <div className="flex justify-between">
                    <span>Frais de temps:</span>
                    <span>{Math.round(result.breakdown.timeFee)} FCFA</span>
                  </div>
                )}
                {result.breakdown.typeFee > 0 && (
                  <div className="flex justify-between">
                    <span>Frais de type:</span>
                    <span>{Math.round(result.breakdown.typeFee)} FCFA</span>
                  </div>
                )}
                {result.breakdown.itemsFee > 0 && (
                  <div className="flex justify-between">
                    <span>Frais d'articles:</span>
                    <span>{Math.round(result.breakdown.itemsFee)} FCFA</span>
                  </div>
                )}
              </div>
            </details>
          </div>
        )}

        {/* Informations tarifaires */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Prix de base: 1000 FCFA (Livraison), 1500 FCFA (Repas), 2000 FCFA (Courses)</p>
          <p>• 200 FCFA/km après les 2 premiers kilomètres gratuits</p>
          <p>• Majorations: Nuit (+50%), Weekend (+30%)</p>
        </div>
      </CardContent>
    </Card>
  );
}
