'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calculator, MapPin, Clock, Package } from 'lucide-react';
import { usePriceCalculator } from '@/hooks/useOrders';
import { OrderType } from '@wali/shared';

interface PriceCalculatorProps {
  onPriceCalculated?: (result: any) => void;
}

export function PriceCalculator({ onPriceCalculated }: PriceCalculatorProps) {
  const { isCalculating, priceResult, calculatePrice, resetCalculation } = usePriceCalculator();

  const [formData, setFormData] = useState({
    type: OrderType.DELIVERY,
    pickupAddress: '',
    deliveryAddress: '',
  });

  const handleCalculate = async () => {
    if (!formData.pickupAddress || !formData.deliveryAddress) {
      alert('Veuillez remplir les deux adresses');
      return;
    }

    try {
      // Pour l'instant, utilisons des coordonnées simulées
      // TODO: Intégrer la géolocalisation réelle
      const result = await calculatePrice({
        type: formData.type,
        pickupLatitude: 5.3364, // Coordonnées d'Abidjan
        pickupLongitude: -4.0267,
        deliveryLatitude: 5.3400,
        deliveryLongitude: -4.0300,
        items: formData.type === OrderType.SHOPPING ? [
          { name: 'Article exemple', quantity: 1, unitPrice: 1000 }
        ] : undefined,
      });

      onPriceCalculated?.(result);
    } catch (error) {
      // L'erreur est déjà gérée par le hook
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calculator className="h-5 w-5" />
          <span>Calculateur de Prix</span>
        </CardTitle>
        <CardDescription>
          Estimez le coût de votre livraison en quelques clics
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Type de livraison */}
        <div>
          <Label>Type de livraison</Label>
          <Select value={formData.type} onValueChange={(value) => setFormData(prev => ({ ...prev, type: value }))}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={OrderType.DELIVERY}>
                <div className="flex items-center space-x-2">
                  <Package className="h-4 w-4" />
                  <span>Livraison Express</span>
                </div>
              </SelectItem>
              <SelectItem value={OrderType.FOOD}>
                <div className="flex items-center space-x-2">
                  <span>🍽️</span>
                  <span>Livraison de Repas</span>
                </div>
              </SelectItem>
              <SelectItem value={OrderType.SHOPPING}>
                <div className="flex items-center space-x-2">
                  <span>🛒</span>
                  <span>Courses et Achats</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Adresses */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="pickup">Adresse de récupération</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="pickup"
                placeholder="Où récupérer ?"
                value={formData.pickupAddress}
                onChange={(e) => setFormData(prev => ({ ...prev, pickupAddress: e.target.value }))}
                className="pl-10"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="delivery">Adresse de livraison</Label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="delivery"
                placeholder="Où livrer ?"
                value={formData.deliveryAddress}
                onChange={(e) => setFormData(prev => ({ ...prev, deliveryAddress: e.target.value }))}
                className="pl-10"
              />
            </div>
          </div>
        </div>

        {/* Bouton de calcul */}
        <Button
          onClick={handleCalculate}
          disabled={isCalculating || !formData.pickupAddress || !formData.deliveryAddress}
          className="w-full"
        >
          {isCalculating ? 'Calcul en cours...' : 'Calculer le Prix'}
        </Button>

        {/* Résultat */}
        {priceResult && (
          <div className="mt-6 p-4 bg-primary/5 rounded-lg border border-primary/20">
            <div className="flex items-center space-x-2 mb-3">
              <Clock className="h-4 w-4 text-primary" />
              <span className="text-sm text-muted-foreground">
                {priceResult.distance.toFixed(1)} km • {Math.round(priceResult.estimatedDuration)} min
              </span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Prix de base:</span>
                <span>{priceResult.basePrice.toLocaleString()} FCFA</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Frais de livraison:</span>
                <span>{Math.round(priceResult.deliveryFee).toLocaleString()} FCFA</span>
              </div>
              <hr />
              <div className="flex justify-between font-bold">
                <span>Total:</span>
                <span className="text-primary">{Math.round(priceResult.totalAmount).toLocaleString()} FCFA</span>
              </div>
            </div>

            {/* Détail des frais */}
            <details className="mt-3">
              <summary className="text-xs text-muted-foreground cursor-pointer">
                Voir le détail des frais
              </summary>
              <div className="mt-2 space-y-1 text-xs text-muted-foreground">
                <div className="flex justify-between">
                  <span>Frais de distance:</span>
                  <span>{Math.round(priceResult.breakdown.distanceFee)} FCFA</span>
                </div>
                {priceResult.breakdown.timeFee > 0 && (
                  <div className="flex justify-between">
                    <span>Frais de temps:</span>
                    <span>{Math.round(priceResult.breakdown.timeFee)} FCFA</span>
                  </div>
                )}
                {priceResult.breakdown.typeFee > 0 && (
                  <div className="flex justify-between">
                    <span>Frais de type:</span>
                    <span>{Math.round(priceResult.breakdown.typeFee)} FCFA</span>
                  </div>
                )}
                {priceResult.breakdown.itemsFee > 0 && (
                  <div className="flex justify-between">
                    <span>Frais d'articles:</span>
                    <span>{Math.round(priceResult.breakdown.itemsFee)} FCFA</span>
                  </div>
                )}
              </div>
            </details>
          </div>
        )}

        {/* Informations tarifaires */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Prix de base: 1000 FCFA (Livraison), 1500 FCFA (Repas), 2000 FCFA (Courses)</p>
          <p>• 200 FCFA/km après les 2 premiers kilomètres gratuits</p>
          <p>• Majorations: Nuit (+50%), Weekend (+30%)</p>
        </div>
      </CardContent>
    </Card>
  );
}
