{"name": "@googlemaps/react-wrapper", "version": "1.2.0", "description": "React component that wraps the loading of Google Maps JavaScript API.", "keywords": ["google", "maps", "react"], "homepage": "https://github.com/googlemaps/react-wrapper", "bugs": {"url": "https://github.com/googlemaps/react-wrapper/issues"}, "repository": {"type": "git", "url": "https://github.com/googlemaps/react-wrapper.git"}, "license": "Apache-2.0", "author": "<PERSON>", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "browser": "dist/index.umd.js", "types": "dist/src/index.d.ts", "files": ["dist", "src"], "scripts": {"build": "rm -rf dist && rollup -c && npm run build:examples", "build:examples": "rm -rf public && rollup -c rollup.config.examples.js", "docs": "typedoc src/index.tsx && npm run build:examples && cp -r public docs/public", "format": "prettier *config.json *.js src/* --write && eslint src/* --fix", "lint": "gts check src/*", "prepare": "rollup -c", "test": "jest -i src/*", "test:e2e": "jest e2e/*"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.6"}, "devDependencies": {"@babel/preset-react": "^7.14.5", "@rollup/plugin-babel": "^6.0.0", "@rollup/plugin-commonjs": "^26.0.1", "@rollup/plugin-html": "^1.0.0", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-replace": "^5.0.0", "@rollup/plugin-typescript": "^11.0.0", "@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.0.0", "@types/google.maps": "^3.45.6", "@types/jest": "^29.5.12", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-react": "^7.22.0", "gts": "^6.0.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.5", "react": "^19.0.0", "react-dom": "^19.0.0", "rollup": "^2.40.0", "rollup-plugin-filesize": "^10.0.0", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^29.1.2", "tslib": "^2.1.0", "typedoc": "^0.28.1", "typescript": "^5.8.2"}, "peerDependencies": {"react": ">=16.8.0"}, "publishConfig": {"access": "public", "registry": "https://wombat-dressing-room.appspot.com"}, "prettier": {"trailingComma": "es5"}}