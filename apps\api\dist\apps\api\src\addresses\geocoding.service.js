"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var GeocodingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeocodingService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let GeocodingService = GeocodingService_1 = class GeocodingService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(GeocodingService_1.name);
        this.googleMapsApiKey = this.configService.get('GOOGLE_MAPS_API_KEY') || '';
    }
    async geocodeAddress(address) {
        try {
            if (!this.googleMapsApiKey || this.configService.get('NODE_ENV') === 'development') {
                return this.getMockGeocodeResult(address);
            }
            const encodedAddress = encodeURIComponent(`${address}, Côte d'Ivoire`);
            const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedAddress}&key=${this.googleMapsApiKey}`;
            const response = await fetch(url);
            const data = await response.json();
            if (data.status === 'OK' && data.results.length > 0) {
                const result = data.results[0];
                const location = result.geometry.location;
                const addressComponents = result.address_components;
                let city = '';
                let district = '';
                for (const component of addressComponents) {
                    if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    else if (component.types.includes('sublocality') || component.types.includes('neighborhood')) {
                        district = component.long_name;
                    }
                }
                return {
                    latitude: location.lat,
                    longitude: location.lng,
                    formattedAddress: result.formatted_address,
                    city: city || 'Abidjan',
                    district,
                    confidence: this.calculateConfidence(result),
                };
            }
            this.logger.warn(`Geocoding failed for address: ${address}`);
            return null;
        }
        catch (error) {
            this.logger.error(`Error geocoding address: ${address}`, error);
            return null;
        }
    }
    async reverseGeocode(latitude, longitude) {
        try {
            if (!this.googleMapsApiKey || this.configService.get('NODE_ENV') === 'development') {
                return this.getMockReverseGeocodeResult(latitude, longitude);
            }
            const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${this.googleMapsApiKey}`;
            const response = await fetch(url);
            const data = await response.json();
            if (data.status === 'OK' && data.results.length > 0) {
                const result = data.results[0];
                const addressComponents = result.address_components;
                let street = '';
                let city = '';
                let district = '';
                for (const component of addressComponents) {
                    if (component.types.includes('route')) {
                        street = component.long_name;
                    }
                    else if (component.types.includes('street_number')) {
                        street = `${component.long_name} ${street}`.trim();
                    }
                    else if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    else if (component.types.includes('sublocality') || component.types.includes('neighborhood')) {
                        district = component.long_name;
                    }
                }
                return {
                    street: street || 'Rue inconnue',
                    city: city || 'Abidjan',
                    district,
                    formattedAddress: result.formatted_address,
                };
            }
            this.logger.warn(`Reverse geocoding failed for coordinates: ${latitude}, ${longitude}`);
            return null;
        }
        catch (error) {
            this.logger.error(`Error reverse geocoding coordinates: ${latitude}, ${longitude}`, error);
            return null;
        }
    }
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371;
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        const distance = R * c;
        return Math.round(distance * 100) / 100;
    }
    isWithinIvoryCoast(latitude, longitude) {
        const bounds = {
            north: 10.74,
            south: 4.34,
            east: -2.49,
            west: -8.60
        };
        return latitude >= bounds.south &&
            latitude <= bounds.north &&
            longitude >= bounds.west &&
            longitude <= bounds.east;
    }
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    calculateConfidence(result) {
        const locationType = result.geometry.location_type;
        switch (locationType) {
            case 'ROOFTOP':
                return 1.0;
            case 'RANGE_INTERPOLATED':
                return 0.8;
            case 'GEOMETRIC_CENTER':
                return 0.6;
            case 'APPROXIMATE':
                return 0.4;
            default:
                return 0.5;
        }
    }
    getMockGeocodeResult(address) {
        const mockLocations = {
            'cocody': { lat: 5.3364, lng: -4.0267, district: 'Cocody' },
            'plateau': { lat: 5.3200, lng: -4.0100, district: 'Plateau' },
            'yopougon': { lat: 5.3458, lng: -4.0732, district: 'Yopougon' },
            'adjame': { lat: 5.3667, lng: -4.0167, district: 'Adjamé' },
            'marcory': { lat: 5.2833, lng: -3.9833, district: 'Marcory' },
        };
        const addressLower = address.toLowerCase();
        let location = mockLocations['plateau'];
        for (const [key, value] of Object.entries(mockLocations)) {
            if (addressLower.includes(key)) {
                location = value;
                break;
            }
        }
        return {
            latitude: location.lat,
            longitude: location.lng,
            formattedAddress: `${address}, Abidjan, Côte d'Ivoire`,
            city: 'Abidjan',
            district: location.district,
            confidence: 0.8,
        };
    }
    getMockReverseGeocodeResult(latitude, longitude) {
        let district = 'Plateau';
        if (latitude > 5.35)
            district = 'Cocody';
        else if (longitude < -4.05)
            district = 'Yopougon';
        else if (latitude < 5.30)
            district = 'Marcory';
        return {
            street: 'Rue de la République',
            city: 'Abidjan',
            district,
            formattedAddress: `Rue de la République, ${district}, Abidjan, Côte d'Ivoire`,
        };
    }
};
exports.GeocodingService = GeocodingService;
exports.GeocodingService = GeocodingService = GeocodingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], GeocodingService);
//# sourceMappingURL=geocoding.service.js.map