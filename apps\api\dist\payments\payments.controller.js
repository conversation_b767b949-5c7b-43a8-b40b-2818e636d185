"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaymentsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const payments_service_1 = require("./payments.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const get_user_decorator_1 = require("../auth/decorators/get-user.decorator");
const dto_1 = require("./dto");
let PaymentsController = class PaymentsController {
    constructor(paymentsService) {
        this.paymentsService = paymentsService;
    }
    async processPayment(processPaymentDto, user) {
        return this.paymentsService.processPayment(processPaymentDto, user.id);
    }
    async getPaymentMethods() {
        return this.paymentsService.getAvailablePaymentMethods();
    }
    async createStripeIntent(createIntentDto, user) {
        return this.paymentsService.createStripePaymentIntent(createIntentDto, user.id);
    }
    async mobileMoneyPayment(mobileMoneyDto, user) {
        return this.paymentsService.processMobileMoneyPayment(mobileMoneyDto, user.id);
    }
    async getPaymentStatus(id) {
        return this.paymentsService.getPaymentStatus(id);
    }
    async refundPayment(id, refundDto, user) {
        return this.paymentsService.refundPayment(id, refundDto, user.id);
    }
    async stripeWebhook(payload) {
        return this.paymentsService.handleStripeWebhook(payload);
    }
    async orangeMoneyWebhook(payload) {
        return this.paymentsService.handleOrangeMoneyWebhook(payload);
    }
    async mtnMoneyWebhook(payload) {
        return this.paymentsService.handleMtnMoneyWebhook(payload);
    }
    async waveWebhook(payload) {
        return this.paymentsService.handleWaveWebhook(payload);
    }
};
exports.PaymentsController = PaymentsController;
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('process'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Traitement d\'un paiement' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Paiement traité avec succès' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Données de paiement invalides' }),
    (0, swagger_1.ApiResponse)({ status: 402, description: 'Paiement échoué' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof dto_1.ProcessPaymentDto !== "undefined" && dto_1.ProcessPaymentDto) === "function" ? _b : Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "processPayment", null);
__decorate([
    (0, common_1.Get)('methods'),
    (0, swagger_1.ApiOperation)({ summary: 'Méthodes de paiement disponibles' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Liste des méthodes récupérée' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getPaymentMethods", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('stripe/intent'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Création d\'un PaymentIntent Stripe' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'PaymentIntent créé avec succès' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Données invalides' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof dto_1.CreateStripeIntentDto !== "undefined" && dto_1.CreateStripeIntentDto) === "function" ? _c : Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "createStripeIntent", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)('mobile-money'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Paiement via Mobile Money' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Paiement Mobile Money initié' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Données invalides' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof dto_1.MobileMoneyPaymentDto !== "undefined" && dto_1.MobileMoneyPaymentDto) === "function" ? _d : Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "mobileMoneyPayment", null);
__decorate([
    (0, common_1.Get)(':id/status'),
    (0, swagger_1.ApiOperation)({ summary: 'Statut d\'un paiement' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Statut récupéré avec succès' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Paiement non trouvé' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "getPaymentStatus", null);
__decorate([
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Post)(':id/refund'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({ summary: 'Remboursement d\'un paiement' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Remboursement traité avec succès' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Remboursement impossible' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Paiement non trouvé' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, get_user_decorator_1.GetUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_e = typeof dto_1.RefundPaymentDto !== "undefined" && dto_1.RefundPaymentDto) === "function" ? _e : Object, Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "refundPayment", null);
__decorate([
    (0, common_1.Post)('webhooks/stripe'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Webhook Stripe' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "stripeWebhook", null);
__decorate([
    (0, common_1.Post)('webhooks/orange-money'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Webhook Orange Money' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "orangeMoneyWebhook", null);
__decorate([
    (0, common_1.Post)('webhooks/mtn-money'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Webhook MTN Mobile Money' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "mtnMoneyWebhook", null);
__decorate([
    (0, common_1.Post)('webhooks/wave'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({ summary: 'Webhook Wave' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], PaymentsController.prototype, "waveWebhook", null);
exports.PaymentsController = PaymentsController = __decorate([
    (0, swagger_1.ApiTags)('Payments'),
    (0, common_1.Controller)('payments'),
    __metadata("design:paramtypes", [typeof (_a = typeof payments_service_1.PaymentsService !== "undefined" && payments_service_1.PaymentsService) === "function" ? _a : Object])
], PaymentsController);
//# sourceMappingURL=payments.controller.js.map