import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';
import { SmsService } from './sms.service';
import { RegisterDto, LoginDto, VerifyOtpDto, RefreshTokenDto } from './dto';
import { User, UserRole } from '@prisma/client';
export interface AuthResponse {
    user: Omit<User, 'createdAt' | 'updatedAt'>;
    accessToken: string;
    refreshToken: string;
}
export interface JwtPayload {
    sub: string;
    phone: string;
    role: UserRole;
    type: 'access' | 'refresh';
}
export declare class AuthService {
    private prisma;
    private jwtService;
    private configService;
    private smsService;
    private readonly logger;
    constructor(prisma: PrismaService, jwtService: JwtService, configService: ConfigService, smsService: SmsService);
    register(registerDto: RegisterDto): Promise<{
        message: string;
        phone: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        message: string;
        phone: string;
    }>;
    verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<AuthResponse>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
    }>;
    private generateTokens;
    private generateAccessToken;
    private generateRefreshToken;
    private normalizePhoneNumber;
    validateUser(userId: string): Promise<User | null>;
}
