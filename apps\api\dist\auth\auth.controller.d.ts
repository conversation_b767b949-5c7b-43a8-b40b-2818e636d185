import { AuthService } from './auth.service';
import { RegisterDto, LoginDto, VerifyPhoneDto, SendSmsDto, RefreshTokenDto, ForgotPasswordDto, ResetPasswordDto } from './dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<any>;
    login(req: any, loginDto: LoginDto): Promise<any>;
    verifyPhone(verifyPhoneDto: VerifyPhoneDto): Promise<any>;
    sendSms(sendSmsDto: SendSmsDto): Promise<any>;
    refresh(refreshTokenDto: RefreshTokenDto): Promise<any>;
    logout(req: any): Promise<any>;
    forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<any>;
    resetPassword(resetPasswordDto: ResetPasswordDto): Promise<any>;
    getProfile(req: any): Promise<any>;
}
