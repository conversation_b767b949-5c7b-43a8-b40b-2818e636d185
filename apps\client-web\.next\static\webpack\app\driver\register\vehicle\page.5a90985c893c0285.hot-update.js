"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/driver/register/vehicle/page",{

/***/ "(app-pages-browser)/./src/app/driver/register/vehicle/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/driver/register/vehicle/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DriverVehicleRegistrationPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!../../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/bike.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,ArrowRight,Bike,Car,CheckCircle,Motorcycle,Package,Truck!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/driver-verification */ \"(app-pages-browser)/./src/lib/driver-verification.ts\");\n/* harmony import */ var _hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/hooks/useDriver */ \"(app-pages-browser)/./src/hooks/useDriver.ts\");\n/* harmony import */ var _hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/useWaliAuth */ \"(app-pages-browser)/./src/hooks/useWaliAuth.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_12__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DriverVehicleRegistrationPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { user, isDriver } = (0,_hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__.useWaliAuth)();\n    const { createProfile, isLoading } = (0,_hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__.useDriver)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        type: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.MOTO,\n        brand: \"\",\n        model: \"\",\n        year: new Date().getFullYear(),\n        color: \"\",\n        registrationNumber: \"\",\n        engineNumber: \"\",\n        chassisNumber: \"\"\n    });\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Redirection si pas livreur\n    if (user && !isDriver) {\n        router.push(\"/dashboard\");\n        return null;\n    }\n    const vehicleTypeOptions = [\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.MOTO,\n            label: \"Motocyclette\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Motorcycle,\n            description: \"Id\\xe9al pour les livraisons rapides en ville\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.SCOOTER,\n            label: \"Scooter\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_13__.Motorcycle,\n            description: \"Parfait pour les courtes distances\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.VELO,\n            label: \"V\\xe9lo\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            description: \"\\xc9cologique et \\xe9conomique\",\n            requiresLicense: false\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.VOITURE,\n            label: \"Voiture\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n            description: \"Pour les gros colis et longues distances\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.TRICYCLE,\n            label: \"Tricycle\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Capacit\\xe9 de charge importante\",\n            requiresLicense: true\n        },\n        {\n            value: _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VehicleType.CAMIONNETTE,\n            label: \"Camionnette\",\n            icon: _barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            description: \"Pour les livraisons volumineuses\",\n            requiresLicense: true\n        }\n    ];\n    const validateField = (field, value)=>{\n        const errors = {\n            ...validationErrors\n        };\n        switch(field){\n            case \"brand\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.brand = \"La marque est obligatoire\";\n                } else {\n                    delete errors.brand;\n                }\n                break;\n            case \"model\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.model = \"Le mod\\xe8le est obligatoire\";\n                } else {\n                    delete errors.model;\n                }\n                break;\n            case \"color\":\n                if (!value || typeof value === \"string\" && value.trim().length < 2) {\n                    errors.color = \"La couleur est obligatoire\";\n                } else {\n                    delete errors.color;\n                }\n                break;\n            case \"registrationNumber\":\n                if (!value || typeof value === \"string\" && value.trim().length === 0) {\n                    errors.registrationNumber = \"Le num\\xe9ro d'immatriculation est obligatoire\";\n                } else if (typeof value === \"string\" && !(0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.validateIvorianPlateNumber)(value)) {\n                    errors.registrationNumber = \"Format invalide (ex: 1234 CI 01)\";\n                } else {\n                    delete errors.registrationNumber;\n                }\n                break;\n            case \"year\":\n                const currentYear = new Date().getFullYear();\n                if (typeof value === \"number\" && (value < 1990 || value > currentYear)) {\n                    errors.year = \"L'ann\\xe9e doit \\xeatre entre 1990 et \".concat(currentYear);\n                } else {\n                    delete errors.year;\n                }\n                break;\n        }\n        setValidationErrors(errors);\n    };\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        validateField(field, value);\n    };\n    const handleVehicleTypeChange = (type)=>{\n        setFormData((prev)=>({\n                ...prev,\n                type,\n                brand: \"\",\n                model: \"\"\n            }));\n        // Clear brand/model errors when type changes\n        const errors = {\n            ...validationErrors\n        };\n        delete errors.brand;\n        delete errors.model;\n        setValidationErrors(errors);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Validation finale\n        const requiredFields = [\n            \"brand\",\n            \"model\",\n            \"color\",\n            \"registrationNumber\"\n        ];\n        const missingFields = requiredFields.filter((field)=>!formData[field] || String(formData[field]).trim().length === 0);\n        if (missingFields.length > 0) {\n            alert(\"Veuillez remplir tous les champs obligatoires\");\n            return;\n        }\n        if (Object.keys(validationErrors).length > 0) {\n            alert(\"Veuillez corriger les erreurs dans le formulaire\");\n            return;\n        }\n        try {\n            await createProfile(formData);\n            // Redirection selon le type de véhicule\n            if ((0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.requiresLicense)(formData.type)) {\n                router.push(\"/driver/register/documents\");\n            } else {\n                router.push(\"/driver/dashboard\");\n            }\n        } catch (error) {\n        // L'erreur est déjà gérée par le hook\n        }\n    };\n    const availableBrands = _lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.VEHICLE_BRANDS[formData.type] || [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"h-12 w-12 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-2xl font-bold text-gray-900\",\n                                    children: \"WALI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900\",\n                            children: \"Inscription Livreur\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-2\",\n                            children: \"\\xc9tape 1 : Informations sur votre v\\xe9hicule\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between text-sm text-gray-600 mb-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Progression\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_8__.Progress, {\n                            value: 33,\n                            className: \"h-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"Informations du v\\xe9hicule\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                children: \"Renseignez les d\\xe9tails de votre v\\xe9hicule de livraison\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                            onSubmit: handleSubmit,\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            className: \"text-base font-medium\",\n                                                            children: \"Type de v\\xe9hicule *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\",\n                                                            children: vehicleTypeOptions.map((option)=>{\n                                                                const IconComponent = option.icon;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"p-4 border rounded-lg cursor-pointer transition-all \".concat(formData.type === option.value ? \"border-blue-500 bg-blue-50 ring-2 ring-blue-200\" : \"border-gray-200 hover:border-gray-300\"),\n                                                                    onClick: ()=>handleVehicleTypeChange(option.value),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start space-x-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"h-6 w-6 text-blue-600 mt-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                lineNumber: 262,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                        className: \"font-medium\",\n                                                                                        children: option.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 264,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                                                        children: option.description\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 265,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    option.requiresLicense && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-xs text-orange-600 mt-1\",\n                                                                                        children: \"⚠️ Permis de conduire requis\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                        lineNumber: 267,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                                lineNumber: 263,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 27\n                                                                }, this);\n                                                            })\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"brand\",\n                                                            children: \"Marque *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                            value: formData.brand,\n                                                            onValueChange: (value)=>handleInputChange(\"brand\", value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                                    className: validationErrors.brand ? \"border-red-500\" : \"\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                        placeholder: \"S\\xe9lectionnez la marque\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 283,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                                    children: availableBrands.map((brand)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                            value: brand,\n                                                                            children: brand\n                                                                        }, brand, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                            lineNumber: 288,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 286,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.brand && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.brand\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"model\",\n                                                                    children: \"Mod\\xe8le *\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 302,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"model\",\n                                                                    placeholder: \"Ex: CB 125, Corolla, etc.\",\n                                                                    value: formData.model,\n                                                                    onChange: (e)=>handleInputChange(\"model\", e.target.value),\n                                                                    className: validationErrors.model ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 303,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                validationErrors.model && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-1\",\n                                                                    children: validationErrors.model\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"year\",\n                                                                    children: \"Ann\\xe9e\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 316,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"year\",\n                                                                    type: \"number\",\n                                                                    min: \"1990\",\n                                                                    max: new Date().getFullYear(),\n                                                                    value: formData.year,\n                                                                    onChange: (e)=>handleInputChange(\"year\", parseInt(e.target.value)),\n                                                                    className: validationErrors.year ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                validationErrors.year && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-red-600 mt-1\",\n                                                                    children: validationErrors.year\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"color\",\n                                                            children: \"Couleur *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"color\",\n                                                            placeholder: \"Ex: Rouge, Bleu, Noir, etc.\",\n                                                            value: formData.color,\n                                                            onChange: (e)=>handleInputChange(\"color\", e.target.value),\n                                                            className: validationErrors.color ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.color && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.color\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                            htmlFor: \"registrationNumber\",\n                                                            children: \"Num\\xe9ro d'immatriculation *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            id: \"registrationNumber\",\n                                                            placeholder: \"Ex: 1234 CI 01\",\n                                                            value: formData.registrationNumber,\n                                                            onChange: (e)=>handleInputChange(\"registrationNumber\", e.target.value.toUpperCase()),\n                                                            className: validationErrors.registrationNumber ? \"border-red-500\" : \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        validationErrors.registrationNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-red-600 mt-1\",\n                                                            children: validationErrors.registrationNumber\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 358,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500 mt-1\",\n                                                            children: \"Format ivoirien : 4 chiffres + CI + 2 chiffres\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"engineNumber\",\n                                                                    children: \"Num\\xe9ro de moteur (optionnel)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 368,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"engineNumber\",\n                                                                    placeholder: \"Num\\xe9ro grav\\xe9 sur le moteur\",\n                                                                    value: formData.engineNumber,\n                                                                    onChange: (e)=>handleInputChange(\"engineNumber\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                                    htmlFor: \"chassisNumber\",\n                                                                    children: \"Num\\xe9ro de ch\\xe2ssis (optionnel)\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                                    id: \"chassisNumber\",\n                                                                    placeholder: \"Num\\xe9ro d'identification du v\\xe9hicule\",\n                                                                    value: formData.chassisNumber,\n                                                                    onChange: (e)=>handleInputChange(\"chassisNumber\", e.target.value)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between pt-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_12___default()), {\n                                                                href: \"/auth/register\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-4 w-4 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 392,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Retour\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 390,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            type: \"submit\",\n                                                            disabled: isLoading,\n                                                            children: isLoading ? \"Enregistrement...\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                children: [\n                                                                    \"Continuer\",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"h-4 w-4 ml-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                        lineNumber: 403,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 234,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"border-blue-200 bg-blue-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-blue-900\",\n                                                children: \"\\uD83D\\uDCCB Documents requis\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 417,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3 text-sm\",\n                                                children: (0,_lib_driver_verification__WEBPACK_IMPORTED_MODULE_9__.requiresLicense)(formData.type) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 424,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Permis de conduire valide\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 428,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Assurance v\\xe9hicule\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 text-orange-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 432,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"Carte grise\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_ArrowRight_Bike_Car_CheckCircle_Motorcycle_Package_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4 text-green-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Aucun document requis pour les v\\xe9los\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 437,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                children: \"\\uD83D\\uDCA1 Conseils\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"text-sm space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• V\\xe9rifiez que votre v\\xe9hicule est en bon \\xe9tat\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Assurez-vous d'avoir tous les documents \\xe0 jour\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"• Les informations doivent correspondre exactement aux documents officiels\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\app\\\\driver\\\\register\\\\vehicle\\\\page.tsx\",\n        lineNumber: 207,\n        columnNumber: 5\n    }, this);\n}\n_s(DriverVehicleRegistrationPage, \"Q6kCI6VSzfSuJWFTRmrExEtvIok=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useWaliAuth__WEBPACK_IMPORTED_MODULE_11__.useWaliAuth,\n        _hooks_useDriver__WEBPACK_IMPORTED_MODULE_10__.useDriver\n    ];\n});\n_c = DriverVehicleRegistrationPage;\nvar _c;\n$RefreshReg$(_c, \"DriverVehicleRegistrationPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/driver/register/vehicle/page.tsx\n"));

/***/ })

});