import { AuthService } from './auth.service';
import { RegisterDto, LoginDto, VerifyOtpDto, RefreshTokenDto } from './dto';
import { User } from '@prisma/client';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        message: string;
        phone: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        message: string;
        phone: string;
    }>;
    verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<import("./auth.service").AuthResponse>;
    refreshToken(refreshTokenDto: RefreshTokenDto): Promise<{
        accessToken: string;
    }>;
    getProfile(user: User): Promise<{
        phone: string;
        email: string | null;
        firstName: string;
        lastName: string;
        role: import(".prisma/client").$Enums.UserRole;
        id: string;
        avatar: string | null;
        isActive: boolean;
        isVerified: boolean;
    }>;
    checkToken(user: User): Promise<{
        valid: boolean;
        user: {
            id: string;
            phone: string;
            role: import(".prisma/client").$Enums.UserRole;
        };
    }>;
}
