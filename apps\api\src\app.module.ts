import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';

// Database
import { PrismaModule } from './prisma/prisma.module';

// Core modules
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { AddressesModule } from './addresses/addresses.module';

// Business modules
import { OrdersModule } from './orders/orders.module';
import { DriversModule } from './drivers/drivers.module';
import { PartnersModule } from './partners/partners.module';
import { RestaurantsModule } from './restaurants/restaurants.module';
import { StoresModule } from './stores/stores.module';
import { ProductsModule } from './products/products.module';
import { CategoriesModule } from './categories/categories.module';

// Service modules
import { PaymentsModule } from './payments/payments.module';
import { NotificationsModule } from './notifications/notifications.module';
import { GeolocationModule } from './geolocation/geolocation.module';
import { PricingModule } from './pricing/pricing.module';
import { UploadModule } from './upload/upload.module';
import { RatingsModule } from './ratings/ratings.module';
import { SupportModule } from './support/support.module';

// Admin modules
import { AdminModule } from './admin/admin.module';

// Utility modules
import { HealthModule } from './health/health.module';
import { SearchModule } from './search/search.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Rate limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 60, // 60 requêtes par minute
      },
    ]),

    // Scheduling
    ScheduleModule.forRoot(),

    // Queue management
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
        password: process.env.REDIS_PASSWORD,
      },
    }),

    // Database
    PrismaModule,

    // Core modules
    AuthModule,
    UsersModule,
    AddressesModule,

    // Business modules
    OrdersModule,
    DriversModule,
    PartnersModule,
    RestaurantsModule,
    StoresModule,
    ProductsModule,
    CategoriesModule,

    // Service modules
    PaymentsModule,
    NotificationsModule,
    GeolocationModule,
    PricingModule,
    UploadModule,
    RatingsModule,
    SupportModule,

    // Admin modules
    AdminModule,

    // Utility modules
    HealthModule,
    SearchModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
