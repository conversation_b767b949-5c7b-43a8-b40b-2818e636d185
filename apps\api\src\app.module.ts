import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

// Database
import { PrismaModule } from './prisma/prisma.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),

    // Database
    PrismaModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
