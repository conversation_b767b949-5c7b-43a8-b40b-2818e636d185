import { ConfigService } from '@nestjs/config';
export declare class SmsService {
    private configService;
    private readonly logger;
    private readonly twilioClient;
    private readonly otpSecret;
    constructor(configService: ConfigService);
    generateOtp(phone: string): string;
    verifyOtp(phone: string, otp: string): boolean;
    sendOtp(phone: string, otp: string): Promise<boolean>;
    sendWelcomeSms(phone: string, firstName: string): Promise<boolean>;
}
