{"name": "@wali/mobile-ui", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"react-native-vector-icons": "^10.0.2", "react-native-paper": "^5.11.1"}, "devDependencies": {"typescript": "^5.0.0", "react": "^18.0.0", "react-native": "^0.72.0", "@types/react": "^18.0.0", "@types/react-native": "^0.72.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}}