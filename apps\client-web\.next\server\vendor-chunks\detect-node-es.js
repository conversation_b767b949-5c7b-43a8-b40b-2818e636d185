/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/detect-node-es";
exports.ids = ["vendor-chunks/detect-node-es"];
exports.modules = {

/***/ "(ssr)/../../node_modules/detect-node-es/es5/node.js":
/*!*****************************************************!*\
  !*** ../../node_modules/detect-node-es/es5/node.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("// Only Node.JS has a process variable that is of [[Class]] process\nmodule.exports.isNode = Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL2RldGVjdC1ub2RlLWVzL2VzNS9ub2RlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0EscUJBQXFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHdhbGkvY2xpZW50LXdlYi8uLi8uLi9ub2RlX21vZHVsZXMvZGV0ZWN0LW5vZGUtZXMvZXM1L25vZGUuanM/MDdlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBPbmx5IE5vZGUuSlMgaGFzIGEgcHJvY2VzcyB2YXJpYWJsZSB0aGF0IGlzIG9mIFtbQ2xhc3NdXSBwcm9jZXNzXG5tb2R1bGUuZXhwb3J0cy5pc05vZGUgPSBPYmplY3QucHJvdG90eXBlLnRvU3RyaW5nLmNhbGwodHlwZW9mIHByb2Nlc3MgIT09ICd1bmRlZmluZWQnID8gcHJvY2VzcyA6IDApID09PSAnW29iamVjdCBwcm9jZXNzXSc7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/detect-node-es/es5/node.js\n");

/***/ })

};
;