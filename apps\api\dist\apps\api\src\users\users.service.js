"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var UsersService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let UsersService = UsersService_1 = class UsersService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(UsersService_1.name);
    }
    async getProfile(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                phone: true,
                email: true,
                firstName: true,
                lastName: true,
                avatar: true,
                role: true,
                isActive: true,
                isVerified: true,
            },
        });
        if (!user) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        if (!user.isActive) {
            throw new common_1.NotFoundException('Compte utilisateur désactivé');
        }
        return user;
    }
    async getUserById(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
            select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true,
                role: true,
                isVerified: true,
            },
        });
        if (!user) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        return user;
    }
    async updateProfile(userId, updateProfileDto) {
        const { firstName, lastName, email, avatar } = updateProfileDto;
        const existingUser = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!existingUser) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        if (!existingUser.isActive) {
            throw new common_1.NotFoundException('Compte utilisateur désactivé');
        }
        if (email && email !== existingUser.email) {
            const emailExists = await this.prisma.user.findUnique({
                where: { email },
            });
            if (emailExists) {
                throw new common_1.ConflictException('Cette adresse email est déjà utilisée');
            }
        }
        const updatedUser = await this.prisma.user.update({
            where: { id: userId },
            data: {
                ...(firstName && { firstName }),
                ...(lastName && { lastName }),
                ...(email !== undefined && { email }),
                ...(avatar !== undefined && { avatar }),
            },
            select: {
                id: true,
                phone: true,
                email: true,
                firstName: true,
                lastName: true,
                avatar: true,
                role: true,
                isActive: true,
                isVerified: true,
            },
        });
        this.logger.log(`Profil mis à jour pour l'utilisateur: ${userId}`);
        return updatedUser;
    }
    async deleteAccount(userId) {
        const existingUser = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!existingUser) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        await this.prisma.user.update({
            where: { id: userId },
            data: {
                isActive: false,
                email: null,
            },
        });
        this.logger.log(`Compte supprimé pour l'utilisateur: ${userId}`);
        return {
            message: 'Votre compte a été supprimé avec succès',
        };
    }
    async getUserStats(userId) {
        const user = await this.prisma.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('Utilisateur non trouvé');
        }
        const [totalOrders, completedOrders, transactions, ratings] = await Promise.all([
            this.prisma.order.count({
                where: { clientId: userId },
            }),
            this.prisma.order.count({
                where: {
                    clientId: userId,
                    status: 'DELIVERED',
                },
            }),
            this.prisma.transaction.findMany({
                where: {
                    userId,
                    status: 'COMPLETED',
                },
                select: { amount: true },
            }),
            this.prisma.rating.findMany({
                where: { toUserId: userId },
                select: { rating: true },
            }),
        ]);
        const totalSpent = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);
        const averageRating = ratings.length > 0
            ? ratings.reduce((sum, rating) => sum + rating.rating, 0) / ratings.length
            : 0;
        return {
            totalOrders,
            completedOrders,
            totalSpent,
            averageRating: Math.round(averageRating * 10) / 10,
        };
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = UsersService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UsersService);
//# sourceMappingURL=users.service.js.map