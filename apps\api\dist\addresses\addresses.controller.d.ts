import { AddressesService } from './addresses.service';
import { CreateAddressDto, UpdateAddressDto } from './dto';
import { User } from '@prisma/client';
export declare class AddressesController {
    private readonly addressesService;
    constructor(addressesService: AddressesService);
    getUserAddresses(user: User): Promise<{
        id: string;
        createdAt: Date;
        userId: string | null;
        label: string | null;
        street: string;
        city: string;
        district: string | null;
        landmark: string | null;
        latitude: number;
        longitude: number;
        isDefault: boolean;
        restaurantId: string | null;
        storeId: string | null;
    }[]>;
    getAddressById(id: string, user: User): Promise<{
        id: string;
        createdAt: Date;
        userId: string | null;
        label: string | null;
        street: string;
        city: string;
        district: string | null;
        landmark: string | null;
        latitude: number;
        longitude: number;
        isDefault: boolean;
        restaurantId: string | null;
        storeId: string | null;
    }>;
    createAddress(user: User, createAddressDto: CreateAddressDto): Promise<{
        id: string;
        createdAt: Date;
        userId: string | null;
        label: string | null;
        street: string;
        city: string;
        district: string | null;
        landmark: string | null;
        latitude: number;
        longitude: number;
        isDefault: boolean;
        restaurantId: string | null;
        storeId: string | null;
    }>;
    updateAddress(id: string, user: User, updateAddressDto: UpdateAddressDto): Promise<{
        id: string;
        createdAt: Date;
        userId: string | null;
        label: string | null;
        street: string;
        city: string;
        district: string | null;
        landmark: string | null;
        latitude: number;
        longitude: number;
        isDefault: boolean;
        restaurantId: string | null;
        storeId: string | null;
    }>;
    deleteAddress(id: string, user: User): Promise<{
        message: string;
    }>;
    setDefaultAddress(id: string, user: User): Promise<{
        id: string;
        createdAt: Date;
        userId: string | null;
        label: string | null;
        street: string;
        city: string;
        district: string | null;
        landmark: string | null;
        latitude: number;
        longitude: number;
        isDefault: boolean;
        restaurantId: string | null;
        storeId: string | null;
    }>;
    searchNearbyAddresses(user: User, latitude: number, longitude: number, radius?: number): Promise<({
        id: string;
        createdAt: Date;
        userId: string | null;
        label: string | null;
        street: string;
        city: string;
        district: string | null;
        landmark: string | null;
        latitude: number;
        longitude: number;
        isDefault: boolean;
        restaurantId: string | null;
        storeId: string | null;
    } & {
        distance: number;
    })[]>;
    geocodeAddress(address: string): Promise<any>;
    reverseGeocode(latitude: number, longitude: number): Promise<any>;
}
