"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SmsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SmsService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const twilio_1 = require("twilio");
const otplib_1 = require("otplib");
let SmsService = SmsService_1 = class SmsService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(SmsService_1.name);
        const accountSid = this.configService.get('TWILIO_ACCOUNT_SID');
        const authToken = this.configService.get('TWILIO_AUTH_TOKEN');
        if (accountSid && authToken) {
            this.twilioClient = new twilio_1.Twilio(accountSid, authToken);
        }
        this.otpSecret = this.configService.get('OTP_SECRET') || 'WALI_LIVRAISON_SECRET';
    }
    generateOtp(phone) {
        const token = `${this.otpSecret}_${phone}_${Date.now()}`;
        otplib_1.authenticator.options = {
            digits: 6,
            step: 300,
            window: 1
        };
        return otplib_1.authenticator.generate(token);
    }
    verifyOtp(phone, otp) {
        try {
            if (this.configService.get('NODE_ENV') === 'development' && otp === '123456') {
                this.logger.debug(`Code OTP de développement utilisé pour ${phone}`);
                return true;
            }
            const token = `${this.otpSecret}_${phone}`;
            otplib_1.authenticator.options = {
                digits: 6,
                step: 300,
                window: 1
            };
            const isValid = otplib_1.authenticator.check(otp, token);
            if (isValid) {
                this.logger.log(`Code OTP vérifié avec succès pour ${phone}`);
            }
            else {
                this.logger.warn(`Code OTP invalide pour ${phone}`);
            }
            return isValid;
        }
        catch (error) {
            this.logger.error(`Erreur lors de la vérification OTP pour ${phone}:`, error);
            return false;
        }
    }
    async sendOtp(phone, otp) {
        try {
            const message = `Votre code de vérification WALI Livraison est: ${otp}. Ce code expire dans 5 minutes.`;
            if (this.configService.get('NODE_ENV') === 'development') {
                this.logger.debug(`[DEV] Code OTP pour ${phone}: ${otp}`);
                return true;
            }
            if (!this.twilioClient) {
                this.logger.error('Twilio n\'est pas configuré. Vérifiez TWILIO_ACCOUNT_SID et TWILIO_AUTH_TOKEN');
                return false;
            }
            const fromNumber = this.configService.get('TWILIO_PHONE_NUMBER');
            if (!fromNumber) {
                this.logger.error('TWILIO_PHONE_NUMBER n\'est pas configuré');
                return false;
            }
            const result = await this.twilioClient.messages.create({
                body: message,
                from: fromNumber,
                to: phone
            });
            this.logger.log(`SMS envoyé avec succès à ${phone}. SID: ${result.sid}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'envoi du SMS à ${phone}:`, error);
            return false;
        }
    }
    async sendWelcomeSms(phone, firstName) {
        try {
            const message = `Bienvenue sur WALI Livraison, ${firstName}! Votre compte a été créé avec succès. Commandez maintenant et faites-vous livrer partout en Côte d'Ivoire.`;
            if (this.configService.get('NODE_ENV') === 'development') {
                this.logger.debug(`[DEV] SMS de bienvenue pour ${phone}: ${message}`);
                return true;
            }
            if (!this.twilioClient) {
                this.logger.warn('Twilio non configuré - SMS de bienvenue non envoyé');
                return false;
            }
            const fromNumber = this.configService.get('TWILIO_PHONE_NUMBER');
            const result = await this.twilioClient.messages.create({
                body: message,
                from: fromNumber,
                to: phone
            });
            this.logger.log(`SMS de bienvenue envoyé à ${phone}. SID: ${result.sid}`);
            return true;
        }
        catch (error) {
            this.logger.error(`Erreur lors de l'envoi du SMS de bienvenue à ${phone}:`, error);
            return false;
        }
    }
};
exports.SmsService = SmsService;
exports.SmsService = SmsService = SmsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], SmsService);
//# sourceMappingURL=sms.service.js.map