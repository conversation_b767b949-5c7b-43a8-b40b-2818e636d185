import { OrdersService } from './orders.service';
import { CreateOrderDto, UpdateOrderDto, PriceCalculationDto } from './dto';
import { User } from '@prisma/client';
export declare class OrdersController {
    private readonly ordersService;
    constructor(ordersService: OrdersService);
    calculatePrice(priceCalculationDto: PriceCalculationDto): Promise<import("@wali/shared").PriceCalculationResult>;
    createOrder(user: User, createOrderDto: CreateOrderDto): Promise<import("@wali/shared").Order>;
    getUserOrders(user: User, page?: number, limit?: number): Promise<{
        orders: import("@wali/shared").Order[];
        total: number;
    }>;
    getOrderById(id: string, user: User): Promise<import("@wali/shared").Order>;
    updateOrder(id: string, user: User, updateOrderDto: UpdateOrderDto): Promise<import("@wali/shared").Order>;
    cancelOrder(id: string, user: User, reason?: string): Promise<import("@wali/shared").Order>;
    getOrderTracking(id: string, user: User): Promise<{
        order: import("@wali/shared").Order;
        currentLocation: any;
        estimatedArrival: any;
        trackingEvents: import("@wali/shared").TrackingEvent[];
    }>;
    rateOrder(id: string, user: User, rating: number, comment?: string): Promise<{
        message: string;
        rating: number;
        comment: string;
    }>;
}
