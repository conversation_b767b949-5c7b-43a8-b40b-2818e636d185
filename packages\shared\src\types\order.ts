export interface Order {
  id: string;
  orderNumber: string;
  clientId: string;
  driverId?: string;
  type: OrderType;
  status: OrderStatus;
  pickupAddress: string;
  pickupLatitude: number;
  pickupLongitude: number;
  deliveryAddress: string;
  deliveryLatitude: number;
  deliveryLongitude: number;
  basePrice: number;
  deliveryFee: number;
  totalAmount: number;
  estimatedDuration?: number;
  scheduledAt?: Date;
  pickedUpAt?: Date;
  deliveredAt?: Date;
  notes?: string;
  proofOfDelivery?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum OrderType {
  DELIVERY = 'DELIVERY',
  FOOD = 'FOOD',
  SHOPPING = 'SHOPPING'
}

export enum OrderStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  ASSIGNED = 'ASSIGNED',
  PICKED_UP = 'PICKED_UP',
  IN_TRANSIT = 'IN_TRANSIT',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  FAILED = 'FAILED'
}

export interface OrderItem {
  id: string;
  orderId: string;
  productId?: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export interface TrackingEvent {
  id: string;
  orderId: string;
  status: OrderStatus;
  latitude?: number;
  longitude?: number;
  description?: string;
  createdAt: Date;
}
