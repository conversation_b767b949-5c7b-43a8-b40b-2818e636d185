"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/addresses/page",{

/***/ "(app-pages-browser)/./src/components/addresses/AddressForm.tsx":
/*!**************************************************!*\
  !*** ./src/components/addresses/AddressForm.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AddressForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAddresses__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAddresses */ \"(app-pages-browser)/./src/hooks/useAddresses.ts\");\n/* harmony import */ var _wali_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wali/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AddressForm(param) {\n    let { address, onSuccess, onCancel } = param;\n    _s();\n    const { createAddress, updateAddress, geocodeAddress, getCurrentLocation, isLoading, error, clearError } = (0,_hooks_useAddresses__WEBPACK_IMPORTED_MODULE_2__.useAddresses)();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        street: \"\",\n        city: \"Abidjan\",\n        district: \"\",\n        landmark: \"\",\n        latitude: 5.3364,\n        longitude: -4.0267,\n        isDefault: false\n    });\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isGeolocating, setIsGeolocating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialiser le formulaire avec les données de l'adresse existante\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (address) {\n            setFormData({\n                label: address.label || \"\",\n                street: address.street,\n                city: address.city,\n                district: address.district || \"\",\n                landmark: address.landmark || \"\",\n                latitude: address.latitude,\n                longitude: address.longitude,\n                isDefault: address.isDefault\n            });\n        }\n    }, [\n        address\n    ]);\n    const handleInputChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n        clearError();\n        setValidationErrors([]);\n    };\n    const validateForm = ()=>{\n        const errors = [];\n        if (!formData.street.trim() || formData.street.length < 5) {\n            errors.push(\"La rue doit contenir au moins 5 caract\\xe8res\");\n        }\n        if (!formData.city) {\n            errors.push(\"La ville est requise\");\n        }\n        if (formData.city === \"Abidjan\" && !formData.district) {\n            errors.push(\"Le quartier est obligatoire pour Abidjan\");\n        }\n        if (formData.latitude < -90 || formData.latitude > 90) {\n            errors.push(\"La latitude doit \\xeatre comprise entre -90 et 90\");\n        }\n        if (formData.longitude < -180 || formData.longitude > 180) {\n            errors.push(\"La longitude doit \\xeatre comprise entre -180 et 180\");\n        }\n        return errors;\n    };\n    const handleGeocodeAddress = async ()=>{\n        if (!formData.street.trim() || !formData.city) {\n            setValidationErrors([\n                \"Veuillez remplir la rue et la ville avant de g\\xe9olocaliser\"\n            ]);\n            return;\n        }\n        setIsGeolocating(true);\n        try {\n            const addressString = \"\".concat(formData.street, \", \").concat(formData.district ? formData.district + \", \" : \"\").concat(formData.city);\n            const result = await geocodeAddress(addressString);\n            setFormData((prev)=>({\n                    ...prev,\n                    latitude: result.latitude,\n                    longitude: result.longitude,\n                    city: result.city,\n                    district: result.district || prev.district\n                }));\n            setValidationErrors([]);\n        } catch (error) {\n            console.error(\"Erreur g\\xe9ocodage:\", error);\n        } finally{\n            setIsGeolocating(false);\n        }\n    };\n    const handleGetCurrentLocation = async ()=>{\n        setIsGeolocating(true);\n        try {\n            const location = await getCurrentLocation();\n            setFormData((prev)=>({\n                    ...prev,\n                    latitude: location.latitude,\n                    longitude: location.longitude\n                }));\n            setValidationErrors([]);\n        } catch (error) {\n            setValidationErrors([\n                error instanceof Error ? error.message : \"Erreur de g\\xe9olocalisation\"\n            ]);\n        } finally{\n            setIsGeolocating(false);\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        clearError();\n        // Validation côté client\n        const errors = validateForm();\n        if (errors.length > 0) {\n            setValidationErrors(errors);\n            return;\n        }\n        try {\n            let result;\n            if (address) {\n                // Mise à jour\n                const updateData = {};\n                if (formData.label !== address.label) updateData.label = formData.label;\n                if (formData.street !== address.street) updateData.street = formData.street;\n                if (formData.city !== address.city) updateData.city = formData.city;\n                if (formData.district !== address.district) updateData.district = formData.district;\n                if (formData.landmark !== address.landmark) updateData.landmark = formData.landmark;\n                if (formData.latitude !== address.latitude) updateData.latitude = formData.latitude;\n                if (formData.longitude !== address.longitude) updateData.longitude = formData.longitude;\n                if (formData.isDefault !== address.isDefault) updateData.isDefault = formData.isDefault;\n                result = await updateAddress(address.id, updateData);\n            } else {\n                // Création\n                result = await createAddress(formData);\n            }\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(result);\n        } catch (error) {\n            console.error(\"Erreur sauvegarde adresse:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-2xl mx-auto bg-white rounded-lg shadow-md p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: address ? \"Modifier l'adresse\" : \"Nouvelle adresse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onCancel,\n                        className: \"text-gray-500 hover:text-gray-700\",\n                        children: \"✕\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            (error || validationErrors.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\",\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 21\n                    }, this),\n                    validationErrors.map((err, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: err\n                        }, index, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                lineNumber: 197,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"label\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Libell\\xe9 (optionnel)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"label\",\n                                value: formData.label,\n                                onChange: (e)=>handleInputChange(\"label\", e.target.value),\n                                placeholder: \"Maison, Bureau, Chez maman...\",\n                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"street\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Rue et num\\xe9ro *\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"street\",\n                                value: formData.street,\n                                onChange: (e)=>handleInputChange(\"street\", e.target.value),\n                                placeholder: \"Rue des Jardins, R\\xe9sidence Les Palmiers, Villa 12\",\n                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"city\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: \"Ville *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"city\",\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange(\"city\", e.target.value),\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                        required: true,\n                                        children: _wali_shared__WEBPACK_IMPORTED_MODULE_3__.IVORY_COAST_CITIES.map((city)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: city,\n                                                children: city\n                                            }, city, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"district\",\n                                        className: \"block text-sm font-medium text-gray-700\",\n                                        children: [\n                                            \"Quartier \",\n                                            formData.city === \"Abidjan\" && \"*\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this),\n                                    formData.city === \"Abidjan\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"district\",\n                                        value: formData.district,\n                                        onChange: (e)=>handleInputChange(\"district\", e.target.value),\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                        required: formData.city === \"Abidjan\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"S\\xe9lectionner un quartier\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            _wali_shared__WEBPACK_IMPORTED_MODULE_3__.ABIDJAN_DISTRICTS.map((district)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: district,\n                                                    children: district\n                                                }, district, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"district\",\n                                        value: formData.district,\n                                        onChange: (e)=>handleInputChange(\"district\", e.target.value),\n                                        placeholder: \"Quartier ou zone\",\n                                        className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 238,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"landmark\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Point de rep\\xe8re (optionnel)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"landmark\",\n                                value: formData.landmark,\n                                onChange: (e)=>handleInputChange(\"landmark\", e.target.value),\n                                placeholder: \"Pr\\xe8s de la pharmacie du carrefour, face \\xe0 l'\\xe9cole...\",\n                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                children: \"Coordonn\\xe9es GPS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"latitude\",\n                                                className: \"block text-xs text-gray-500\",\n                                                children: \"Latitude\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"latitude\",\n                                                value: formData.latitude,\n                                                onChange: (e)=>handleInputChange(\"latitude\", parseFloat(e.target.value)),\n                                                step: \"0.000001\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                htmlFor: \"longitude\",\n                                                className: \"block text-xs text-gray-500\",\n                                                children: \"Longitude\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"number\",\n                                                id: \"longitude\",\n                                                value: formData.longitude,\n                                                onChange: (e)=>handleInputChange(\"longitude\", parseFloat(e.target.value)),\n                                                step: \"0.000001\",\n                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500\",\n                                                required: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2 mt-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleGeocodeAddress,\n                                        disabled: isGeolocating,\n                                        className: \"flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium disabled:opacity-50\",\n                                        children: isGeolocating ? \"G\\xe9olocalisation...\" : \"\\uD83D\\uDCCD G\\xe9olocaliser l'adresse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleGetCurrentLocation,\n                                        disabled: isGeolocating,\n                                        className: \"flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium disabled:opacity-50\",\n                                        children: isGeolocating ? \"Localisation...\" : \"\\uD83C\\uDFAF Ma position\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                id: \"isDefault\",\n                                checked: formData.isDefault,\n                                onChange: (e)=>handleInputChange(\"isDefault\", e.target.checked),\n                                className: \"h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"isDefault\",\n                                className: \"ml-2 block text-sm text-gray-900\",\n                                children: \"D\\xe9finir comme adresse par d\\xe9faut\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3\",\n                        children: [\n                            onCancel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onCancel,\n                                className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-md text-sm font-medium\",\n                                children: \"Annuler\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: isLoading,\n                                className: \"bg-orange-600 hover:bg-orange-700 text-white px-6 py-2 rounded-md text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed\",\n                                children: isLoading ? \"Enregistrement...\" : address ? \"Modifier\" : \"Cr\\xe9er\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                                lineNumber: 383,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\addresses\\\\AddressForm.tsx\",\n        lineNumber: 180,\n        columnNumber: 5\n    }, this);\n}\n_s(AddressForm, \"kuCx5zgqQtcJeO0ipA635fdEIY4=\", false, function() {\n    return [\n        _hooks_useAddresses__WEBPACK_IMPORTED_MODULE_2__.useAddresses\n    ];\n});\n_c = AddressForm;\nvar _c;\n$RefreshReg$(_c, \"AddressForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/addresses/AddressForm.tsx\n"));

/***/ })

});