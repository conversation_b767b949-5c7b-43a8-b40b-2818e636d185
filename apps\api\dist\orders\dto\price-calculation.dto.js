"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.PriceCalculationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const shared_1 = require("@wali/shared");
const create_order_dto_1 = require("./create-order.dto");
class PriceCalculationDto {
}
exports.PriceCalculationDto = PriceCalculationDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type de commande',
        enum: shared_1.OrderType,
        example: shared_1.OrderType.FOOD
    }),
    (0, class_validator_1.IsEnum)(shared_1.OrderType, { message: 'Le type de commande doit être DELIVERY, FOOD ou SHOPPING' }),
    __metadata("design:type", typeof (_a = typeof shared_1.OrderType !== "undefined" && shared_1.OrderType) === "function" ? _a : Object)
], PriceCalculationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Latitude de l\'adresse de récupération',
        example: 5.3364,
        minimum: -90,
        maximum: 90
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La latitude de récupération doit être un nombre' }),
    (0, class_validator_1.Min)(-90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    (0, class_validator_1.Max)(90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    __metadata("design:type", Number)
], PriceCalculationDto.prototype, "pickupLatitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Longitude de l\'adresse de récupération',
        example: -4.0267,
        minimum: -180,
        maximum: 180
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La longitude de récupération doit être un nombre' }),
    (0, class_validator_1.Min)(-180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    (0, class_validator_1.Max)(180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    __metadata("design:type", Number)
], PriceCalculationDto.prototype, "pickupLongitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Latitude de l\'adresse de livraison',
        example: 5.3400,
        minimum: -90,
        maximum: 90
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La latitude de livraison doit être un nombre' }),
    (0, class_validator_1.Min)(-90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    (0, class_validator_1.Max)(90, { message: 'La latitude doit être comprise entre -90 et 90' }),
    __metadata("design:type", Number)
], PriceCalculationDto.prototype, "deliveryLatitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Longitude de l\'adresse de livraison',
        example: -4.0300,
        minimum: -180,
        maximum: 180
    }),
    (0, class_validator_1.IsNumber)({}, { message: 'La longitude de livraison doit être un nombre' }),
    (0, class_validator_1.Min)(-180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    (0, class_validator_1.Max)(180, { message: 'La longitude doit être comprise entre -180 et 180' }),
    __metadata("design:type", Number)
], PriceCalculationDto.prototype, "deliveryLongitude", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Liste des articles (optionnel, requis pour les courses)',
        type: [create_order_dto_1.CreateOrderItemDto],
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Les articles doivent être un tableau' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => create_order_dto_1.CreateOrderItemDto),
    __metadata("design:type", Array)
], PriceCalculationDto.prototype, "items", void 0);
//# sourceMappingURL=price-calculation.dto.js.map