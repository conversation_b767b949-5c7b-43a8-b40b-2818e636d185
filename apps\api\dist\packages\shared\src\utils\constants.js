"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PRICING = exports.DEFAULT_COORDINATES = exports.BUSINESS_TYPES = exports.VEHICLE_TYPES = exports.ORDER_STATUSES = exports.PAYMENT_METHODS = exports.API_ENDPOINTS = exports.APP_CONFIG = void 0;
exports.APP_CONFIG = {
    NAME: 'WALI Livraison',
    VERSION: '1.0.0',
    DESCRIPTION: 'Plateforme de livraison multi-services en Côte d\'Ivoire'
};
exports.API_ENDPOINTS = {
    AUTH: '/auth',
    USERS: '/users',
    ORDERS: '/orders',
    DRIVERS: '/drivers',
    PAYMENTS: '/payments',
    RESTAURANTS: '/restaurants',
    STORES: '/stores',
    PRODUCTS: '/products'
};
exports.PAYMENT_METHODS = {
    CASH: 'Espèces',
    STRIPE: 'Carte bancaire',
    ORANGE_MONEY: 'Orange Money',
    MTN_MONEY: 'MTN Mobile Money',
    WAVE: 'Wave'
};
exports.ORDER_STATUSES = {
    PENDING: 'En attente',
    CONFIRMED: 'Confirmée',
    ASSIGNED: 'Assignée',
    PICKED_UP: 'Récupérée',
    IN_TRANSIT: 'En transit',
    DELIVERED: 'Livrée',
    CANCELLED: 'Annulée',
    FAILED: 'Échec'
};
exports.VEHICLE_TYPES = {
    MOTO: 'Moto',
    CAR: 'Voiture',
    TRUCK: 'Camion',
    BICYCLE: 'Vélo'
};
exports.BUSINESS_TYPES = {
    RESTAURANT: 'Restaurant',
    STORE: 'Magasin',
    PHARMACY: 'Pharmacie',
    SUPERMARKET: 'Supermarché'
};
exports.DEFAULT_COORDINATES = {
    ABIDJAN: {
        latitude: 5.3600,
        longitude: -4.0083
    }
};
exports.PRICING = {
    BASE_DELIVERY_PRICE: 1000,
    PRICE_PER_KM: 200,
    COMMISSION_RATE: 0.15
};
//# sourceMappingURL=constants.js.map