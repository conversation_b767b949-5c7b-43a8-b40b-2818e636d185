import { PaymentsService } from './payments.service';
import { ProcessPaymentDto, CreateStripeIntentDto, MobileMoneyPaymentDto, RefundPaymentDto } from './dto';
export declare class PaymentsController {
    private readonly paymentsService;
    constructor(paymentsService: PaymentsService);
    processPayment(processPaymentDto: ProcessPaymentDto, user: any): Promise<any>;
    getPaymentMethods(): Promise<any>;
    createStripeIntent(createIntentDto: CreateStripeIntentDto, user: any): Promise<any>;
    mobileMoneyPayment(mobileMoneyDto: MobileMoneyPaymentDto, user: any): Promise<any>;
    getPaymentStatus(id: string): Promise<any>;
    refundPayment(id: string, refundDto: RefundPaymentDto, user: any): Promise<any>;
    stripeWebhook(payload: any): Promise<any>;
    orangeMoneyWebhook(payload: any): Promise<any>;
    mtnMoneyWebhook(payload: any): Promise<any>;
    waveWebhook(payload: any): Promise<any>;
}
