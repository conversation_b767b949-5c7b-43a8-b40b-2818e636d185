import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';

import { RestaurantsService } from './restaurants.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { GetUser } from '../auth/decorators/get-user.decorator';
import {
  CreateRestaurantDto,
  UpdateRestaurantDto,
  RestaurantQueryDto,
} from './dto';

@ApiTags('Restaurants')
@Controller('restaurants')
export class RestaurantsController {
  constructor(private readonly restaurantsService: RestaurantsService) {}

  @Get()
  @ApiOperation({ summary: 'Liste des restaurants' })
  @ApiResponse({ status: 200, description: 'Liste récupérée avec succès' })
  async findAll(@Query() query: RestaurantQueryDto) {
    return this.restaurantsService.findAll(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Détails d\'un restaurant' })
  @ApiResponse({ status: 200, description: 'Restaurant trouvé' })
  @ApiResponse({ status: 404, description: 'Restaurant non trouvé' })
  async findOne(@Param('id') id: string) {
    return this.restaurantsService.findOne(id);
  }

  @Get(':id/menu')
  @ApiOperation({ summary: 'Menu d\'un restaurant' })
  @ApiResponse({ status: 200, description: 'Menu récupéré avec succès' })
  async getMenu(@Param('id') id: string) {
    return this.restaurantsService.getMenu(id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('PARTNER', 'ADMIN')
  @Post()
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Création d\'un restaurant' })
  @ApiResponse({ status: 201, description: 'Restaurant créé avec succès' })
  @ApiResponse({ status: 403, description: 'Accès refusé' })
  async create(
    @Body() createRestaurantDto: CreateRestaurantDto,
    @GetUser() user: any,
  ) {
    return this.restaurantsService.create(createRestaurantDto, user.id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('PARTNER', 'ADMIN')
  @Patch(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Modification d\'un restaurant' })
  @ApiResponse({ status: 200, description: 'Restaurant modifié avec succès' })
  @ApiResponse({ status: 403, description: 'Accès refusé' })
  @ApiResponse({ status: 404, description: 'Restaurant non trouvé' })
  async update(
    @Param('id') id: string,
    @Body() updateRestaurantDto: UpdateRestaurantDto,
    @GetUser() user: any,
  ) {
    return this.restaurantsService.update(id, updateRestaurantDto, user.id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('PARTNER', 'ADMIN')
  @Patch(':id/hours')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Mise à jour des horaires d\'ouverture' })
  @ApiResponse({ status: 200, description: 'Horaires mis à jour avec succès' })
  async updateHours(
    @Param('id') id: string,
    @Body() openingHours: any,
    @GetUser() user: any,
  ) {
    return this.restaurantsService.updateOpeningHours(id, openingHours, user.id);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @Delete(':id')
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Suppression d\'un restaurant' })
  @ApiResponse({ status: 200, description: 'Restaurant supprimé avec succès' })
  @ApiResponse({ status: 403, description: 'Accès refusé' })
  @ApiResponse({ status: 404, description: 'Restaurant non trouvé' })
  async remove(@Param('id') id: string) {
    return this.restaurantsService.remove(id);
  }
}
