import { ConfigService } from '@nestjs/config';
import { GeocodeResult, ReverseGeocodeResult } from '@wali/shared';
export declare class GeocodingService {
    private readonly configService;
    private readonly logger;
    private readonly googleMapsApiKey;
    constructor(configService: ConfigService);
    geocodeAddress(address: string): Promise<GeocodeResult | null>;
    reverseGeocode(latitude: number, longitude: number): Promise<ReverseGeocodeResult | null>;
    calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number;
    isWithinIvoryCoast(latitude: number, longitude: number): boolean;
    private toRadians;
    private calculateConfidence;
    private getMockGeocodeResult;
    private getMockReverseGeocodeResult;
    private getRealGeocodeResult;
    private getRealReverseGeocodeResult;
}
