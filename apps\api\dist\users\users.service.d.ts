import { PrismaService } from '../prisma/prisma.service';
import { UpdateProfileDto } from './dto';
import { User } from '@prisma/client';
export declare class UsersService {
    private readonly prisma;
    private readonly logger;
    constructor(prisma: PrismaService);
    getProfile(userId: string): Promise<Omit<User, 'createdAt' | 'updatedAt'>>;
    getUserById(userId: string): Promise<Partial<User>>;
    updateProfile(userId: string, updateProfileDto: UpdateProfileDto): Promise<Omit<User, 'createdAt' | 'updatedAt'>>;
    deleteAccount(userId: string): Promise<{
        message: string;
    }>;
    getUserStats(userId: string): Promise<{
        totalOrders: number;
        completedOrders: number;
        totalSpent: number;
        averageRating: number;
    }>;
}
