"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/hooks/useAuth.ts":
/*!******************************!*\
  !*** ./src/hooks/useAuth.ts ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useAuth auto */ \nconst API_BASE_URL = \"http://localhost:3001/api/v1\" || 0;\nconst useAuth = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        user: null,\n        accessToken: null,\n        refreshToken: null,\n        isAuthenticated: false,\n        isLoading: true,\n        error: null\n    });\n    // Charger les tokens depuis le localStorage au démarrage\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const loadTokens = ()=>{\n            try {\n                const accessToken = localStorage.getItem(\"wali_access_token\");\n                const refreshToken = localStorage.getItem(\"wali_refresh_token\");\n                const userStr = localStorage.getItem(\"wali_user\");\n                if (accessToken && refreshToken && userStr) {\n                    const user = JSON.parse(userStr);\n                    setState((prev)=>({\n                            ...prev,\n                            user,\n                            accessToken,\n                            refreshToken,\n                            isAuthenticated: true,\n                            isLoading: false\n                        }));\n                } else {\n                    setState((prev)=>({\n                            ...prev,\n                            isLoading: false\n                        }));\n                }\n            } catch (error) {\n                console.error(\"Erreur lors du chargement des tokens:\", error);\n                setState((prev)=>({\n                        ...prev,\n                        isLoading: false\n                    }));\n            }\n        };\n        loadTokens();\n    }, []);\n    // Sauvegarder les tokens dans le localStorage\n    const saveTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((authResponse)=>{\n        try {\n            localStorage.setItem(\"wali_access_token\", authResponse.accessToken);\n            localStorage.setItem(\"wali_refresh_token\", authResponse.refreshToken);\n            localStorage.setItem(\"wali_user\", JSON.stringify(authResponse.user));\n            setState((prev)=>({\n                    ...prev,\n                    user: authResponse.user,\n                    accessToken: authResponse.accessToken,\n                    refreshToken: authResponse.refreshToken,\n                    isAuthenticated: true,\n                    error: null\n                }));\n        } catch (error) {\n            console.error(\"Erreur lors de la sauvegarde des tokens:\", error);\n        }\n    }, []);\n    // Supprimer les tokens\n    const clearTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        localStorage.removeItem(\"wali_access_token\");\n        localStorage.removeItem(\"wali_refresh_token\");\n        localStorage.removeItem(\"wali_user\");\n        setState({\n            user: null,\n            accessToken: null,\n            refreshToken: null,\n            isAuthenticated: false,\n            isLoading: false,\n            error: null\n        });\n    }, []);\n    // Inscription\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/auth/register\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de l'inscription\");\n            }\n            const result = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, []);\n    // Connexion\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/auth/login\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Erreur lors de la connexion\");\n            }\n            const result = await response.json();\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n            return result;\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, []);\n    // Vérification OTP\n    const verifyOtp = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (data)=>{\n        setState((prev)=>({\n                ...prev,\n                isLoading: true,\n                error: null\n            }));\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/auth/verify-otp\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(data)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Code de v\\xe9rification invalide\");\n            }\n            const authResponse = await response.json();\n            saveTokens(authResponse);\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false\n                }));\n        } catch (error) {\n            const errorMessage = error instanceof Error ? error.message : \"Erreur inconnue\";\n            setState((prev)=>({\n                    ...prev,\n                    isLoading: false,\n                    error: errorMessage\n                }));\n            throw error;\n        }\n    }, [\n        saveTokens\n    ]);\n    // Rafraîchissement du token\n    const refreshAccessToken = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async ()=>{\n        if (!state.refreshToken) return false;\n        try {\n            const response = await fetch(\"\".concat(API_BASE_URL, \"/auth/refresh\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refreshToken: state.refreshToken\n                })\n            });\n            if (!response.ok) {\n                clearTokens();\n                return false;\n            }\n            const { accessToken } = await response.json();\n            localStorage.setItem(\"wali_access_token\", accessToken);\n            setState((prev)=>({\n                    ...prev,\n                    accessToken\n                }));\n            return true;\n        } catch (error) {\n            console.error(\"Erreur lors du rafra\\xeechissement du token:\", error);\n            clearTokens();\n            return false;\n        }\n    }, [\n        state.refreshToken,\n        clearTokens\n    ]);\n    // Déconnexion\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        clearTokens();\n    }, [\n        clearTokens\n    ]);\n    // Requête authentifiée\n    const authenticatedFetch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async function(url) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        if (!state.accessToken) {\n            throw new Error(\"Token d'acc\\xe8s manquant\");\n        }\n        const response = await fetch(url, {\n            ...options,\n            headers: {\n                ...options.headers,\n                \"Authorization\": \"Bearer \".concat(state.accessToken),\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        // Si le token a expiré, essayer de le rafraîchir\n        if (response.status === 401) {\n            const refreshed = await refreshAccessToken();\n            if (refreshed) {\n                // Retry avec le nouveau token\n                return fetch(url, {\n                    ...options,\n                    headers: {\n                        ...options.headers,\n                        \"Authorization\": \"Bearer \".concat(state.accessToken),\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n            } else {\n                throw new Error(\"Session expir\\xe9e\");\n            }\n        }\n        return response;\n    }, [\n        state.accessToken,\n        refreshAccessToken\n    ]);\n    return {\n        // État\n        ...state,\n        // Actions\n        register,\n        login,\n        verifyOtp,\n        logout,\n        refreshAccessToken,\n        authenticatedFetch,\n        // Utilitaires\n        clearError: ()=>setState((prev)=>({\n                    ...prev,\n                    error: null\n                }))\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useAuth.ts\n"));

/***/ })

});