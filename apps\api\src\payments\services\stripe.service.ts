import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Strip<PERSON> from 'stripe';

@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);
  private stripe: Stripe;

  constructor(private configService: ConfigService) {
    this.stripe = new Stripe(
      this.configService.get<string>('STRIPE_SECRET_KEY'),
      {
        apiVersion: '2023-10-16',
      },
    );
  }

  async createPaymentIntent(
    amount: number,
    currency: string = 'xof',
    metadata?: Record<string, string>,
  ): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Stripe utilise les centimes
        currency,
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      this.logger.log(`PaymentIntent créé: ${paymentIntent.id}`);
      return paymentIntent;
    } catch (error) {
      this.logger.error('Erreur lors de la création du PaymentIntent', error);
      throw error;
    }
  }

  async confirmPaymentIntent(
    paymentIntentId: string,
    paymentMethodId: string,
  ): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.confirm(
        paymentIntentId,
        {
          payment_method: paymentMethodId,
        },
      );

      this.logger.log(`PaymentIntent confirmé: ${paymentIntent.id}`);
      return paymentIntent;
    } catch (error) {
      this.logger.error('Erreur lors de la confirmation du PaymentIntent', error);
      throw error;
    }
  }

  async retrievePaymentIntent(
    paymentIntentId: string,
  ): Promise<Stripe.PaymentIntent> {
    try {
      return await this.stripe.paymentIntents.retrieve(paymentIntentId);
    } catch (error) {
      this.logger.error('Erreur lors de la récupération du PaymentIntent', error);
      throw error;
    }
  }

  async createRefund(
    paymentIntentId: string,
    amount?: number,
    reason?: string,
  ): Promise<Stripe.Refund> {
    try {
      const refundData: Stripe.RefundCreateParams = {
        payment_intent: paymentIntentId,
      };

      if (amount) {
        refundData.amount = Math.round(amount * 100);
      }

      if (reason) {
        refundData.reason = reason as Stripe.RefundCreateParams.Reason;
      }

      const refund = await this.stripe.refunds.create(refundData);

      this.logger.log(`Remboursement créé: ${refund.id}`);
      return refund;
    } catch (error) {
      this.logger.error('Erreur lors de la création du remboursement', error);
      throw error;
    }
  }

  async constructWebhookEvent(
    payload: string | Buffer,
    signature: string,
  ): Promise<Stripe.Event> {
    const webhookSecret = this.configService.get<string>('STRIPE_WEBHOOK_SECRET');
    
    try {
      return this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret,
      );
    } catch (error) {
      this.logger.error('Erreur lors de la vérification du webhook Stripe', error);
      throw error;
    }
  }

  async createCustomer(
    email?: string,
    phone?: string,
    name?: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.Customer> {
    try {
      const customerData: Stripe.CustomerCreateParams = {};

      if (email) customerData.email = email;
      if (phone) customerData.phone = phone;
      if (name) customerData.name = name;
      if (metadata) customerData.metadata = metadata;

      const customer = await this.stripe.customers.create(customerData);

      this.logger.log(`Client Stripe créé: ${customer.id}`);
      return customer;
    } catch (error) {
      this.logger.error('Erreur lors de la création du client Stripe', error);
      throw error;
    }
  }

  async attachPaymentMethod(
    paymentMethodId: string,
    customerId: string,
  ): Promise<Stripe.PaymentMethod> {
    try {
      const paymentMethod = await this.stripe.paymentMethods.attach(
        paymentMethodId,
        {
          customer: customerId,
        },
      );

      this.logger.log(`Méthode de paiement attachée: ${paymentMethod.id}`);
      return paymentMethod;
    } catch (error) {
      this.logger.error('Erreur lors de l\'attachement de la méthode de paiement', error);
      throw error;
    }
  }

  async listCustomerPaymentMethods(
    customerId: string,
    type: string = 'card',
  ): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: type as Stripe.PaymentMethodListParams.Type,
      });

      return paymentMethods.data;
    } catch (error) {
      this.logger.error('Erreur lors de la récupération des méthodes de paiement', error);
      throw error;
    }
  }

  async createSetupIntent(
    customerId: string,
    metadata?: Record<string, string>,
  ): Promise<Stripe.SetupIntent> {
    try {
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        metadata,
        automatic_payment_methods: {
          enabled: true,
        },
      });

      this.logger.log(`SetupIntent créé: ${setupIntent.id}`);
      return setupIntent;
    } catch (error) {
      this.logger.error('Erreur lors de la création du SetupIntent', error);
      throw error;
    }
  }
}
