"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/maps/SimpleMap.tsx":
/*!*******************************************!*\
  !*** ./src/components/maps/SimpleMap.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleMap: function() { return /* binding */ SimpleMap; },\n/* harmony export */   useGoogleMapsLoaded: function() { return /* binding */ useGoogleMapsLoaded; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Loader2!=!lucide-react */ \"(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./GoogleMapsProvider */ \"(app-pages-browser)/./src/components/maps/GoogleMapsProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ SimpleMap,useGoogleMapsLoaded auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\nconst SimpleMap = (param)=>{\n    let { center = {\n        lat: 5.3364,\n        lng: -4.0267\n    }, zoom = 13, height = \"400px\", width = \"100%\", markers = [], onMapClick, className = \"\" } = param;\n    _s();\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [map, setMap] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isLoaded, loadError } = (0,_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_2__.useGoogleMaps)();\n    const markersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    // Le chargement est maintenant géré par le GoogleMapsProvider\n    // Initialiser la carte quand Google Maps est chargé\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoaded || !mapRef.current || map) return;\n        try {\n            const mapInstance = new google.maps.Map(mapRef.current, {\n                center,\n                zoom,\n                mapTypeControl: false,\n                streetViewControl: false,\n                fullscreenControl: false,\n                styles: [\n                    {\n                        featureType: \"poi\",\n                        elementType: \"labels\",\n                        stylers: [\n                            {\n                                visibility: \"off\"\n                            }\n                        ]\n                    }\n                ]\n            });\n            setMap(mapInstance);\n            // Gestionnaire de clic\n            if (onMapClick) {\n                mapInstance.addListener(\"click\", (event)=>{\n                    if (event.latLng) {\n                        onMapClick({\n                            lat: event.latLng.lat(),\n                            lng: event.latLng.lng()\n                        });\n                    }\n                });\n            }\n            console.log(\"✅ Carte simple initialis\\xe9e avec succ\\xe8s\");\n        } catch (err) {\n            console.error(\"❌ Erreur d'initialisation de la carte:\", err);\n        }\n    }, [\n        isLoaded,\n        center,\n        zoom,\n        onMapClick,\n        map\n    ]);\n    // Gérer les markers\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!map) return;\n        // Supprimer les anciens markers\n        markersRef.current.forEach((marker)=>marker.setMap(null));\n        markersRef.current = [];\n        // Ajouter les nouveaux markers (avec fallback pour compatibilité)\n        markers.forEach((markerData)=>{\n            try {\n                var _google_maps_marker;\n                // Essayer d'utiliser AdvancedMarkerElement si disponible\n                if ((_google_maps_marker = google.maps.marker) === null || _google_maps_marker === void 0 ? void 0 : _google_maps_marker.AdvancedMarkerElement) {\n                    const marker = new google.maps.marker.AdvancedMarkerElement({\n                        position: markerData.position,\n                        map,\n                        title: markerData.title\n                    });\n                    markersRef.current.push(marker);\n                } else {\n                    // Fallback vers Marker classique\n                    const marker = new google.maps.Marker({\n                        position: markerData.position,\n                        map,\n                        title: markerData.title,\n                        icon: getMarkerIcon(markerData.color || \"red\")\n                    });\n                    markersRef.current.push(marker);\n                }\n            } catch (error) {\n                // En cas d'erreur, utiliser Marker classique\n                const marker = new google.maps.Marker({\n                    position: markerData.position,\n                    map,\n                    title: markerData.title,\n                    icon: getMarkerIcon(markerData.color || \"red\")\n                });\n                markersRef.current.push(marker);\n            }\n        });\n    }, [\n        map,\n        markers\n    ]);\n    // Fonction pour obtenir l'icône du marker\n    const getMarkerIcon = (color)=>{\n        const colors = {\n            red: \"#EF4444\",\n            green: \"#10B981\",\n            blue: \"#3B82F6\",\n            orange: \"#EA580C\"\n        };\n        return {\n            url: \"data:image/svg+xml;charset=UTF-8,\" + encodeURIComponent('\\n        <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\\n          <path d=\"M16 2C11.6 2 8 5.6 8 10C8 16 16 30 16 30S24 16 24 10C24 5.6 20.4 2 16 2Z\" fill=\"'.concat(colors[color] || colors.red, '\" stroke=\"#FFFFFF\" stroke-width=\"2\"/>\\n          <circle cx=\"16\" cy=\"10\" r=\"4\" fill=\"#FFFFFF\"/>\\n        </svg>\\n      ')),\n            scaledSize: new google.maps.Size(32, 32),\n            anchor: new google.maps.Point(16, 32)\n        };\n    };\n    if (loadError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center bg-red-50 border border-red-200 rounded-lg \".concat(className),\n            style: {\n                height,\n                width\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center text-red-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        className: \"h-8 w-8 mx-auto mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm\",\n                        children: loadError\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center bg-gray-100 \".concat(className),\n            style: {\n                height,\n                width\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-2 text-primary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"Chargement de la carte...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n                lineNumber: 168,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n            lineNumber: 164,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            height,\n            width\n        },\n        className: className,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: mapRef,\n            className: \"w-full h-full rounded-lg\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Mientior livraison app\\\\apps\\\\client-web\\\\src\\\\components\\\\maps\\\\SimpleMap.tsx\",\n        lineNumber: 177,\n        columnNumber: 5\n    }, undefined);\n};\n_s(SimpleMap, \"GPiGMBwT4M8YZjdVQOJB11NWxb4=\", false, function() {\n    return [\n        _GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_2__.useGoogleMaps\n    ];\n});\n_c = SimpleMap;\n// Hook pour vérifier si Google Maps est disponible (utilise maintenant le Provider)\nfunction useGoogleMapsLoaded() {\n    _s1();\n    return (0,_GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_2__.useGoogleMaps)();\n}\n_s1(useGoogleMapsLoaded, \"VdmNZ8zSCfgaJPg8UadmXH8+HHo=\", false, function() {\n    return [\n        _GoogleMapsProvider__WEBPACK_IMPORTED_MODULE_2__.useGoogleMaps\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"SimpleMap\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/maps/SimpleMap.tsx\n"));

/***/ })

});