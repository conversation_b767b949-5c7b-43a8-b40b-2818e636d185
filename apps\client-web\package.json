{"name": "@wali/client-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.10", "@googlemaps/react-wrapper": "^1.2.0", "@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "@types/google.maps": "^3.58.1", "@types/leaflet": "^1.9.8", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "leaflet": "^1.9.4", "lucide-react": "^0.292.0", "next": "14.0.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-leaflet": "^4.2.1", "sonner": "^2.0.6", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}