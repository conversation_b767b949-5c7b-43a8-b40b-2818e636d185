{"name": "@wali/ui", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "lint": "eslint src --ext .ts,.tsx"}, "dependencies": {"@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.292.0"}, "devDependencies": {"typescript": "^5.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}}